using System.Runtime.Serialization;

namespace UnityEngine.UI.Extensions
{
    public sealed class QuaternionSurrogate : ISerializationSurrogate
    {

	// Method called to serialize a Vector3 object
	public void GetObjectData(System.Object obj,
				  SerializationInfo info, StreamingContext context)
	{

	    Quaternion quaternion = (Quaternion)obj;
	    info.AddValue("x", quaternion.x);
	    info.AddValue("y", quaternion.y);
	    info.AddValue("z", quaternion.z);
	    info.AddValue("w", quaternion.w);
	}

	// Method called to deserialize a Vector3 object
	public System.Object SetObjectData(System.Object obj,
					   SerializationInfo info, StreamingContext context,
					   ISurrogateSelector selector)
	{

	    Quaternion quaternion = (Quaternion)obj;
	    quaternion.x = (float)info.GetValue("x", typeof(float));
	    quaternion.y = (float)info.GetValue("y", typeof(float));
	    quaternion.z = (float)info.GetValue("z", typeof(float));
	    quaternion.w = (float)info.GetValue("w", typeof(float));
	    obj = quaternion;
	    return obj;
	}
    }
}