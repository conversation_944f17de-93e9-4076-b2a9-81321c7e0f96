{"skeleton": {"hash": "Fs/QVL+xTm0", "spine": "4.2.43", "x": -520.12, "y": -395.19, "width": 1055.35, "height": 987.92}, "bones": [{"name": "root"}, {"name": "scale", "parent": "root"}, {"name": "main_C", "parent": "scale", "x": 32.06, "y": -297.35}, {"name": "SPINE", "parent": "main_C", "length": 131.03, "rotation": 100.31, "x": -2.03, "y": 55.5}, {"name": "CHEST", "parent": "SPINE", "length": 191.63, "rotation": -2.24, "x": 130.8, "y": 0.78}, {"name": "banana_c", "parent": "CHEST", "x": -69.03, "y": -172.11}, {"name": "Banana", "parent": "banana_c", "length": 363.37, "rotation": -54.26, "x": -1.03, "y": 1.72}, {"name": "Emotions", "parent": "scale", "x": 875.4, "y": -13.51}, {"name": "HEAD", "parent": "CHEST", "length": 335.11, "rotation": -0.18, "x": 193.44, "y": 1.28}, {"name": "L_hand_4", "parent": "CHEST", "length": 94.04, "rotation": 161.92, "x": 164.55, "y": 215.91, "scaleY": -1}, {"name": "L_hand_5", "parent": "L_hand_4", "length": 82.05, "rotation": 15.7, "x": 95.7, "y": -0.75}, {"name": "L_hand_6", "parent": "L_hand_5", "length": 85.74, "rotation": 9.86, "x": 85.69, "y": 0.02, "inherit": "noScale"}, {"name": "L_arm_2", "parent": "L_hand_6", "length": 73.03, "rotation": 170, "x": 76.88, "y": 3.68}, {"name": "L_brow", "parent": "HEAD", "x": 307.74, "y": 87.54, "scaleY": -1}, {"name": "face_v", "parent": "HEAD", "x": 133.19, "y": 1.34}, {"name": "face_h", "parent": "face_v", "x": -51.34}, {"name": "L_eye", "parent": "face_h", "x": 30.13, "y": -147.21}, {"name": "L_fingeL_4", "parent": "L_arm_2", "length": 76.93, "rotation": 38.7, "x": 106.39, "y": -28.9}, {"name": "L_fingeL_5", "parent": "L_arm_2", "length": 62.34, "rotation": 45.46, "x": 84.61, "y": 27.19}, {"name": "L_fingeL_6", "parent": "L_arm_2", "length": 62.34, "rotation": 61.62, "x": 44.36, "y": 60.74}, {"name": "L_hand_c_1", "parent": "CHEST", "rotation": -98.08, "x": 95.98, "y": -138.79}, {"name": "L_hand_c_2", "parent": "main_C", "x": 254.96, "y": 181.69}, {"name": "R_hand_1", "parent": "CHEST", "length": 94.04, "rotation": -136.65, "x": 89.5, "y": -142.13}, {"name": "R_hand_2", "parent": "R_hand_1", "length": 82.05, "rotation": -18.82, "x": 95.7, "y": -0.75}, {"name": "R_hand_3", "parent": "R_hand_2", "length": 85.74, "rotation": 9.86, "x": 85.69, "y": 0.02}, {"name": "R_arm_1", "parent": "R_hand_3", "length": 73.03, "rotation": 170, "x": 87.86, "y": -8.97}, {"name": "R_brow", "parent": "HEAD", "x": 259.1, "y": -210.04}, {"name": "<PERSON>_eye", "parent": "face_h", "x": 56.46, "y": 66.87}, {"name": "R_finger_1", "parent": "R_arm_1", "length": 76.93, "rotation": 38.7, "x": 65.07, "y": 20.21}, {"name": "R_finger_2", "parent": "R_arm_1", "length": 62.34, "rotation": 45.46, "x": 43.28, "y": 76.3}, {"name": "R_finger_3", "parent": "R_arm_1", "length": 68.34, "rotation": 61.62, "x": 3.03, "y": 109.85}, {"name": "R_leg", "parent": "main_C", "length": 55.54, "rotation": -10.03, "x": 93.69, "y": 14}, {"name": "R_foot", "parent": "R_leg", "length": 79.05, "rotation": 69.16, "x": 40.45, "y": -56.5}, {"name": "R_leg2", "parent": "main_C", "length": 55.54, "rotation": -178.18, "x": -156.62, "y": 8.98, "scaleY": -1}, {"name": "R_foot2", "parent": "R_leg2", "length": 79.05, "rotation": 41.56, "x": -10.27, "y": -60.92}, {"name": "R_foot_finger_2", "parent": "R_foot", "length": 88.05, "rotation": 3.46, "x": 83.04, "y": 0.55}, {"name": "R_foot_finger_5", "parent": "R_foot2", "length": 84.29, "rotation": 2.91, "x": 78.94, "y": -1.24}, {"name": "R_hand_c_1", "parent": "CHEST", "rotation": -98.08, "x": 150.51, "y": 187.63}, {"name": "R_hand_c_2", "parent": "main_C", "x": -377.92, "y": 175.18}, {"name": "eyes", "parent": "scale", "x": 809.2, "y": -11.7}, {"name": "mouth", "parent": "face_h", "x": -106.04, "y": 0.99}, {"name": "CHEST2", "parent": "CHEST", "length": 101.68, "rotation": 176.98, "x": 124.59, "y": 184.78}, {"name": "CHEST3", "parent": "CHEST2", "length": 88.65, "rotation": 79.15, "x": 101.68}, {"name": "CHEST4", "parent": "Banana", "length": 43.58, "rotation": -62.25, "x": -180.82, "y": 218.63}, {"name": "bone", "parent": "L_hand_5", "length": 100, "rotation": 7.6, "x": 82.05, "inherit": "noScale"}, {"name": "R_hand_4", "parent": "R_hand_3", "length": 47.37, "rotation": -2.59, "x": 28.27, "y": 11.53, "inherit": "noScale"}, {"name": "tail base", "parent": "SPINE", "rotation": -100.76, "x": 20.1, "y": 101.85}, {"name": "tail", "parent": "tail base", "x": -185.89, "y": -16.46, "scaleX": 2, "scaleY": 2}, {"name": "tail2", "parent": "tail", "x": -58.91, "y": 56.66}, {"name": "tail3", "parent": "tail", "length": 28.64, "rotation": -167.33, "x": -1.26, "y": 1.93}, {"name": "tail4", "parent": "tail3", "length": 37.88, "rotation": -22.82, "x": 29.58, "y": -0.28}, {"name": "tail5", "parent": "tail4", "length": 41.58, "rotation": -20.63, "x": 35.84, "y": 0.77}, {"name": "tail6", "parent": "tail5", "length": 31.31, "rotation": -38.47, "x": 41.36, "y": 0.44}, {"name": "tail7", "parent": "tail6", "length": 31.7, "rotation": -16.08, "x": 30.66, "y": -0.24}, {"name": "tail8", "parent": "tail7", "length": 32.92, "rotation": -20.02, "x": 31.7}, {"name": "tail9", "parent": "tail8", "length": 27.36, "rotation": -31.68, "x": 32.92}, {"name": "tail10", "parent": "tail9", "length": 27.09, "rotation": -30.38, "x": 27.36}, {"name": "tail11", "parent": "tail10", "length": 33.12, "rotation": -29.6, "x": 27.09}, {"name": "tail12", "parent": "tail11", "length": 24.3, "rotation": -58.4, "x": 32.43, "y": 0.85}, {"name": "tail13", "parent": "tail12", "length": 16.85, "rotation": -44.62, "x": 25.99, "y": -1.09}, {"name": "tail14", "parent": "tail13", "length": 9.79, "rotation": -27.31, "x": 16.61, "y": -1.75}], "slots": [{"name": "R_hand_spline", "bone": "scale", "attachment": "R_hand_spline"}, {"name": "L_hand_spline", "bone": "scale", "attachment": "L_hand_spline"}, {"name": "shadow", "bone": "scale", "attachment": "monkey new/shadow"}, {"name": "tail", "bone": "tail2", "attachment": "monkey new/monkey_tail"}, {"name": "monkey_right_arm2", "bone": "scale"}, {"name": "monkey_right_arm", "bone": "scale", "attachment": "monkey new/monkey_right_arm"}, {"name": "monkey_body", "bone": "scale", "attachment": "monkey new/monkey_body"}, {"name": "monkey_left_leg", "bone": "scale", "attachment": "monkey new/monkey_left_leg"}, {"name": "monkey_right_leg", "bone": "scale", "attachment": "monkey new/monkey_right_leg"}, {"name": "monkeyOLD_banana_3_1", "bone": "Banana", "attachment": "banana_3_1"}, {"name": "monkeyOLD_banana_1", "bone": "Banana", "attachment": "banana_1"}, {"name": "banana_3_2", "bone": "Banana", "attachment": "banana_3_2"}, {"name": "banana_2_1", "bone": "Banana", "attachment": "banana_2_1"}, {"name": "banana_2_2", "bone": "Banana", "attachment": "banana_2_2"}, {"name": "banana_3_3", "bone": "Banana", "attachment": "banana_3_3"}, {"name": "monkey_left_arm", "bone": "scale", "attachment": "monkey new/monkey_left_arm"}, {"name": "monkey_left_hand closed", "bone": "bone", "attachment": "monkey new/monkey_left_hand closed"}, {"name": "monkey_left_hand", "bone": "bone", "attachment": "monkey new/monkey_left_hand"}, {"name": "monkey_right_hand", "bone": "R_hand_4", "attachment": "monkey new/monkey_right_hand"}, {"name": "monkey_ear right", "bone": "scale", "attachment": "monkey new/monkey_ear right"}, {"name": "under_eye2", "bone": "eyes", "attachment": "monkey new/under_eye2"}, {"name": "under_eye1", "bone": "eyes", "attachment": "monkey new/under_eye1"}, {"name": "monkey_eye_rioht_iris", "bone": "scale", "attachment": "monkey new/monkey_eye_rioht_iris"}, {"name": "monkey_eye_left_iris", "bone": "scale", "attachment": "monkey new/monkey_eye_left_iris"}, {"name": "monkey_hand left", "bone": "scale", "attachment": "monkey new/monkey_hand left"}, {"name": "head", "bone": "eyes", "attachment": "monkey new/head"}, {"name": "monkey_ear left", "bone": "scale", "attachment": "monkey new/monkey_ear left"}, {"name": "monkey_left_foot", "bone": "scale", "attachment": "monkey new/monkey_left_foot"}, {"name": "monkey_right_foot", "bone": "scale", "attachment": "monkey new/monkey_right_foot"}, {"name": "closed_eye_right", "bone": "eyes"}, {"name": "closed_eye_left", "bone": "eyes"}, {"name": "monkey_mouth smile closed", "bone": "scale", "attachment": "monkey new/monkey_mouth smile closed"}, {"name": "monkey_hand right", "bone": "scale", "attachment": "monkey new/monkey_hand right"}, {"name": "monkey_mouth angry", "bone": "scale", "attachment": "monkey new/monkey_mouth angry"}, {"name": "monkey_mouth smile open", "bone": "scale", "attachment": "monkey new/monkey_mouth smile open"}, {"name": "monkey_mouth o", "bone": "scale", "attachment": "monkey new/monkey_mouth o"}, {"name": "monkey_nose", "bone": "scale", "attachment": "monkey new/monkey_nose"}, {"name": "monkey_eyebrow_angry", "bone": "scale"}, {"name": "monkey_eyebrow left", "bone": "scale", "attachment": "monkey new/monkey_eyebrow left"}, {"name": "monkey_eyebrow right", "bone": "scale", "attachment": "monkey new/monkey_eyebrow right"}], "ik": [{"name": "CHEST4", "order": 1, "bones": ["CHEST2", "CHEST3"], "target": "CHEST4"}], "transform": [{"name": "L_arm", "order": 4, "bones": ["R_arm_1"], "target": "L_hand_c_2", "rotation": 122.26, "x": 27.95, "y": -33.94, "scaleX": 0.0036, "scaleY": 0.0001, "mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "R_arm", "order": 3, "bones": ["L_arm_2"], "target": "R_hand_c_2", "rotation": 64.01, "x": -39.89, "y": -34.12, "scaleX": 0.0054, "shearY": 180, "mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}], "path": [{"name": "L_hand_ik", "order": 2, "bones": ["R_hand_1", "R_hand_2", "R_hand_3"], "target": "L_hand_spline", "spacingMode": "percent", "rotateMode": "chainScale", "spacing": 0.33, "mixRotate": 0, "mixX": 0}, {"name": "R_hand_ik", "bones": ["L_hand_4", "L_hand_5", "L_hand_6"], "target": "R_hand_spline", "spacingMode": "percent", "rotateMode": "chainScale", "spacing": 0.33, "mixRotate": 0, "mixX": 0}], "physics": [{"name": "tail3", "order": 5, "bone": "tail3", "rotate": 1, "inertia": 0.1353, "damping": 0.85}, {"name": "tail4", "order": 6, "bone": "tail4", "rotate": 1, "inertia": 0.1805, "damping": 0.85}, {"name": "tail5", "order": 7, "bone": "tail5", "rotate": 1, "inertia": 0.1805, "damping": 0.85}, {"name": "tail6", "order": 8, "bone": "tail6", "rotate": 1, "inertia": 0.0526, "damping": 0.85}, {"name": "tail7", "order": 9, "bone": "tail7", "rotate": 1, "inertia": 0.1729, "damping": 0.85}, {"name": "tail8", "order": 10, "bone": "tail8", "rotate": 1, "inertia": 0.1053, "damping": 0.85}, {"name": "tail9", "order": 11, "bone": "tail9", "rotate": 1, "inertia": 0.0602, "damping": 0.85}, {"name": "tail10", "order": 12, "bone": "tail10", "rotate": 1, "inertia": 0.0977, "damping": 0.85}, {"name": "tail11", "order": 13, "bone": "tail11", "rotate": 1, "inertia": 0.0977, "strength": 162.4, "damping": 0.85}, {"name": "tail12", "order": 14, "bone": "tail12", "rotate": 1, "inertia": 0.1203, "damping": 0.85}, {"name": "tail13", "order": 15, "bone": "tail13", "rotate": 1, "inertia": 0.1053, "damping": 0.85}, {"name": "tail14", "order": 16, "bone": "tail14", "rotate": 1, "inertia": 0.0602, "strength": 188, "damping": 0.9699}], "skins": [{"name": "default", "attachments": {"closed_eye_left": {"monkey new/closed_eye_left": {"type": "mesh", "uvs": [0.87243, 0.13839, 0.97187, 0.51371, 0.96858, 0.68769, 0.84338, 0.90056, 0.68208, 0.96404, 0.51573, 0.96276, 0.11728, 0.76385, 0.04102, 0.54098, 0.04717, 0.39376, 0.06837, 0.32606, 0.41439, 0.11805, 0.68404, 0.08546], "triangles": [0, 1, 11, 10, 8, 9, 10, 7, 8, 1, 6, 7, 10, 11, 1, 2, 3, 1, 7, 10, 1, 3, 5, 1, 5, 6, 1, 4, 5, 3], "vertices": [2, 8, 262.25, -14.36, 0.2, 15, 180.4, -15.69, 0.8, 2, 8, 155.75, -23.09, 0.2, 15, 73.9, -24.42, 0.8, 2, 8, 107.97, -15.69, 0.2, 15, 26.12, -17.02, 0.8, 2, 8, 53.4, 21.45, 0.2, 15, -28.45, 20.11, 0.8, 2, 8, 41.11, 61.26, 0.2, 15, -40.74, 59.92, 0.8, 2, 8, 46.8, 99.77, 0.2, 15, -35.04, 98.43, 0.8, 2, 8, 114.35, 184.54, 0.2, 15, 32.51, 183.2, 0.8, 2, 8, 178.15, 193.71, 0.2, 15, 96.3, 192.37, 0.8, 2, 8, 218.47, 186.67, 0.2, 15, 136.62, 185.33, 0.8, 2, 8, 236.42, 179.17, 0.2, 15, 154.58, 177.83, 0.8, 2, 8, 282.56, 91.04, 0.2, 15, 200.72, 89.7, 0.8, 2, 8, 282.87, 27.29, 0.2, 15, 201.02, 25.95, 0.8], "hull": 12}}, "closed_eye_right": {"monkey new/closed_eye_right": {"type": "mesh", "uvs": [0.74636, 0.19626, 0.9171, 0.4675, 0.8909, 0.80794, 0.60702, 0.93157, 0.46577, 0.8858, 0.27435, 0.88837, 0.15176, 0.67635, 0.13741, 0.33012, 0.3858, 0.09163, 0.49844, 0.11107], "triangles": [3, 4, 2, 5, 6, 4, 1, 4, 6, 2, 4, 1, 6, 0, 1, 8, 9, 7, 9, 0, 6, 9, 6, 7], "vertices": [2, 8, 227.63, -225.88, 0.37714, 15, 145.78, -227.22, 0.62286, 2, 8, 140.22, -252.98, 0.37714, 15, 58.37, -254.32, 0.62286, 2, 8, 38.02, -232.8, 0.56857, 15, -43.83, -234.13, 0.43143, 2, 8, 9.47, -163.64, 0.30857, 15, -72.37, -164.98, 0.69143, 2, 8, 27.73, -133.73, 0.37714, 15, -54.11, -135.07, 0.62286, 2, 8, 32.93, -90.49, 0.37714, 15, -48.91, -91.82, 0.62286, 2, 8, 100.92, -71.75, 0.26857, 15, 19.07, -73.09, 0.73143, 2, 8, 206.14, -83.04, 0.37714, 15, 124.29, -84.38, 0.62286, 2, 8, 270.55, -149.02, 0.37714, 15, 188.7, -150.35, 0.62286, 2, 8, 261.15, -173.58, 0.37714, 15, 179.31, -174.92, 0.62286], "hull": 10}}, "head": {"monkey new/head": {"type": "mesh", "uvs": [0.46785, 0, 0.48557, 0, 0.53229, 0.02435, 0.55806, 0.04759, 0.59821, 0.03035, 0.71193, 0.11763, 0.82564, 0.2049, 0.89036, 0.3354, 0.90029, 0.44138, 0.9125, 0.57172, 0.92472, 0.70206, 0.90862, 0.79178, 0.86168, 0.85737, 0.80967, 0.90468, 0.70101, 0.95883, 0.5977, 0.97942, 0.49439, 1, 0.48319, 1, 0.36738, 0.97567, 0.27346, 0.94882, 0.17955, 0.92198, 0.09874, 0.87368, 0.04404, 0.81638, 0.00314, 0.72232, 0.00315, 0.6056, 0.00315, 0.48888, 0.02163, 0.3788, 0.06895, 0.23412, 0.20925, 0.10331, 0.36039, 0.04836, 0.33874, 0.04001, 0.3625, 0.01744, 0.14063, 0.39817, 0.19502, 0.31168, 0.29346, 0.25358, 0.40095, 0.26304, 0.47607, 0.31304, 0.53047, 0.38466, 0.59393, 0.31439, 0.68071, 0.2725, 0.76101, 0.29006, 0.11779, 0.52388, 0.13365, 0.60882, 0.16643, 0.64964, 0.12894, 0.71583, 0.1349, 0.80371, 0.18595, 0.8765, 0.83174, 0.66645, 0.86152, 0.58604, 0.86152, 0.48307, 0.84025, 0.3872, 0.80877, 0.3295, 0.26765, 0.51026, 0.25687, 0.49815, 0.28379, 0.48084, 0.29142, 0.49548, 0.34732, 0.49086, 0.34892, 0.47662, 0.37586, 0.48843, 0.3715, 0.49868, 0.41578, 0.52589, 0.43079, 0.55546, 0.43733, 0.59548, 0.248, 0.5308, 0.23703, 0.52179, 0.23636, 0.55324, 0.22359, 0.54842, 0.22693, 0.58771, 0.21546, 0.58729, 0.22069, 0.62686, 0.23143, 0.62408, 0.25033, 0.66143, 0.24379, 0.66779, 0.27997, 0.69599, 0.28564, 0.68826, 0.32168, 0.69853, 0.32138, 0.706, 0.36217, 0.69388, 0.36454, 0.7019, 0.39156, 0.67996, 0.39592, 0.6869, 0.41379, 0.65597, 0.4199, 0.66188, 0.42648, 0.62882, 0.43428, 0.6305, 0.41175, 0.53109, 0.4322, 0.59462, 0.33161, 0.59289, 0.69932, 0.59675, 0.59586, 0.60711, 0.60403, 0.60711, 0.60646, 0.57869, 0.61608, 0.54921, 0.6315, 0.52248, 0.65207, 0.50543, 0.67725, 0.4958, 0.70167, 0.4952, 0.72686, 0.50302, 0.74588, 0.51507, 0.76126, 0.53152, 0.77204, 0.54914, 0.78012, 0.57301, 0.78319, 0.59848, 0.79338, 0.59888, 0.79319, 0.57281, 0.7855, 0.54292, 0.39368, 0.51344, 0.39971, 0.50604, 0.32029, 0.48898, 0.31736, 0.47381, 0.42692, 0.55812, 0.78109, 0.62596, 0.79135, 0.62787, 0.77438, 0.65029, 0.78182, 0.65449, 0.76271, 0.6695, 0.76906, 0.67421, 0.74941, 0.68339, 0.75465, 0.69077, 0.72883, 0.69503, 0.73311, 0.70496, 0.70819, 0.70025, 0.71014, 0.71082, 0.68572, 0.70076, 0.68499, 0.71082, 0.66277, 0.69439, 0.65935, 0.7019, 0.63943, 0.67982, 0.63284, 0.6867, 0.62122, 0.6589, 0.61353, 0.66412, 0.60945, 0.63401, 0.60066, 0.63758, 0.23843, 0.7626, 0.35628, 0.79606, 0.52143, 0.73249, 0.53105, 0.61371, 0.52864, 0.48155, 0.63269, 0.78293, 0.77299, 0.77373, 0.67375, 0.47786, 0.64452, 0.49365, 0.62526, 0.51554, 0.60909, 0.54245, 0.5974, 0.57869, 0.70251, 0.47443, 0.73185, 0.48412, 0.75405, 0.49699, 0.77289, 0.51961], "triangles": [36, 35, 3, 37, 36, 38, 109, 34, 35, 57, 109, 35, 36, 57, 35, 33, 34, 109, 54, 32, 33, 145, 39, 40, 145, 40, 51, 146, 145, 51, 140, 38, 39, 38, 141, 37, 36, 58, 57, 39, 145, 140, 109, 54, 33, 36, 37, 58, 141, 137, 37, 50, 146, 51, 50, 147, 146, 37, 107, 58, 108, 109, 57, 56, 108, 57, 56, 57, 58, 38, 140, 141, 145, 95, 140, 96, 145, 146, 55, 54, 109, 55, 109, 108, 96, 95, 145, 141, 140, 95, 49, 147, 50, 53, 32, 54, 52, 53, 54, 59, 56, 58, 97, 96, 146, 97, 146, 147, 94, 141, 95, 137, 107, 37, 59, 58, 107, 55, 52, 54, 106, 59, 107, 98, 97, 147, 142, 137, 141, 142, 141, 94, 148, 147, 49, 98, 147, 148, 64, 32, 53, 63, 64, 53, 93, 142, 94, 41, 32, 64, 60, 107, 137, 106, 107, 60, 52, 63, 53, 85, 106, 60, 99, 98, 148, 143, 137, 142, 143, 142, 93, 105, 148, 49, 99, 148, 105, 66, 41, 64, 66, 64, 63, 100, 99, 105, 92, 143, 93, 65, 66, 63, 61, 60, 137, 85, 60, 61, 110, 85, 61, 48, 104, 105, 105, 49, 48, 101, 100, 105, 104, 101, 105, 144, 137, 143, 144, 143, 92, 91, 144, 92, 68, 41, 66, 67, 68, 66, 65, 67, 66, 87, 108, 56, 87, 56, 59, 87, 59, 106, 87, 106, 85, 87, 85, 110, 55, 108, 87, 52, 55, 87, 63, 52, 87, 65, 63, 87, 67, 65, 87, 110, 61, 62, 61, 136, 62, 86, 110, 62, 87, 110, 86, 136, 61, 137, 88, 95, 96, 88, 96, 97, 88, 97, 98, 88, 98, 99, 88, 99, 100, 88, 100, 101, 94, 95, 88, 93, 94, 88, 92, 93, 88, 91, 92, 88, 102, 101, 104, 88, 101, 102, 103, 104, 48, 102, 104, 103, 90, 144, 91, 90, 91, 88, 137, 144, 136, 89, 144, 90, 42, 41, 68, 89, 136, 144, 70, 67, 87, 111, 88, 102, 111, 102, 103, 69, 68, 67, 69, 67, 70, 43, 42, 68, 112, 111, 103, 83, 87, 86, 62, 83, 86, 84, 62, 136, 84, 83, 62, 131, 90, 88, 131, 132, 89, 131, 89, 90, 69, 43, 68, 113, 88, 111, 113, 111, 112, 114, 113, 112, 81, 87, 83, 81, 83, 84, 129, 131, 88, 71, 70, 87, 82, 81, 84, 129, 132, 131, 130, 132, 129, 48, 112, 103, 47, 112, 48, 114, 112, 47, 72, 70, 71, 115, 88, 113, 116, 115, 113, 116, 113, 114, 127, 129, 88, 88, 125, 127, 79, 87, 81, 74, 71, 87, 115, 117, 88, 128, 129, 127, 130, 129, 128, 82, 80, 79, 82, 79, 81, 74, 87, 75, 116, 118, 117, 116, 117, 115, 79, 77, 87, 123, 125, 88, 117, 119, 88, 73, 71, 74, 72, 71, 73, 77, 75, 87, 119, 121, 88, 121, 123, 88, 126, 127, 125, 128, 127, 126, 78, 77, 79, 78, 79, 80, 118, 120, 119, 118, 119, 117, 76, 74, 75, 123, 126, 125, 124, 126, 123, 122, 121, 119, 122, 119, 120, 44, 42, 43, 135, 84, 136, 82, 84, 135, 72, 69, 70, 133, 72, 73, 72, 43, 69, 72, 44, 43, 133, 44, 72, 47, 139, 116, 47, 116, 114, 118, 116, 139, 120, 118, 139, 138, 130, 128, 138, 128, 126, 138, 126, 124, 76, 77, 78, 77, 76, 75, 134, 76, 78, 45, 44, 133, 46, 45, 133, 73, 74, 76, 133, 73, 76, 133, 76, 134, 124, 121, 122, 121, 124, 123, 122, 138, 124, 122, 120, 139, 80, 134, 78, 80, 82, 135, 135, 134, 80, 132, 136, 89, 136, 130, 138, 130, 136, 132, 136, 138, 135, 3, 38, 36, 39, 38, 3, 139, 138, 122, 139, 47, 11, 29, 30, 31, 34, 28, 29, 33, 27, 28, 0, 29, 31, 29, 3, 35, 34, 29, 35, 0, 1, 2, 2, 29, 0, 3, 29, 2, 5, 3, 4, 40, 39, 5, 39, 3, 5, 6, 40, 5, 34, 33, 28, 51, 40, 6, 51, 6, 7, 32, 26, 27, 50, 51, 7, 33, 32, 27, 50, 7, 8, 49, 50, 8, 32, 25, 26, 41, 25, 32, 49, 8, 9, 9, 48, 49, 24, 25, 41, 24, 41, 42, 48, 9, 10, 47, 48, 10, 44, 24, 42, 23, 24, 44, 11, 47, 10, 22, 23, 44, 22, 44, 45, 12, 139, 11, 21, 22, 45, 21, 45, 46, 13, 139, 12, 20, 21, 46, 19, 133, 134, 46, 133, 19, 20, 46, 19, 139, 14, 138, 14, 139, 13, 134, 17, 18, 19, 134, 18, 15, 138, 14, 15, 135, 138, 135, 17, 134, 135, 16, 17, 15, 16, 135], "vertices": [2, 8, 511.09, -61.51, 0.94, 15, 429.24, -62.85, 0.06, 2, 8, 509.47, -73.18, 0.94, 15, 427.62, -74.52, 0.06, 2, 8, 489.84, -101.81, 0.94, 15, 408, -103.14, 0.06, 2, 8, 472.82, -116.73, 0.94, 15, 390.98, -118.07, 0.06, 2, 8, 480.04, -144.68, 0.94, 15, 398.19, -146.01, 0.06, 2, 8, 414.6, -211.91, 0.94, 15, 332.75, -213.24, 0.06, 2, 8, 349.15, -279.14, 0.94, 15, 267.31, -280.48, 0.06, 2, 8, 260.91, -310.33, 0.94, 15, 179.06, -311.67, 0.06, 2, 8, 193.13, -307.6, 0.94, 15, 111.29, -308.94, 0.06, 2, 8, 109.78, -304.24, 0.94, 15, 27.93, -305.58, 0.06, 2, 8, 26.42, -300.89, 0.94, 15, -55.42, -302.22, 0.06, 2, 8, -28.72, -282.44, 0.94, 15, -110.57, -283.78, 0.06, 2, 8, -65.82, -245.8, 0.94, 15, -147.67, -247.14, 0.06, 2, 8, -90.92, -207.43, 0.94, 15, -172.77, -208.77, 0.06, 2, 8, -115.18, -131.16, 0.89429, 15, -197.03, -132.5, 0.10571, 2, 8, -118.74, -61.35, 0.88, 15, -200.59, -62.68, 0.12, 2, 8, -122.3, 8.47, 0.78286, 15, -204.15, 7.13, 0.21714, 2, 8, -121.28, 15.84, 0.76857, 15, -203.13, 14.51, 0.23143, 2, 8, -95.36, 89.96, 0.84857, 15, -177.21, 88.62, 0.15143, 2, 8, -69.86, 149.43, 0.84286, 15, -151.7, 148.1, 0.15714, 2, 8, -44.35, 208.91, 0.84857, 15, -126.19, 207.58, 0.15143, 2, 8, -6.5, 257.89, 0.91429, 15, -88.35, 256.55, 0.08571, 2, 8, 34.64, 288.89, 0.94, 15, -47.2, 287.55, 0.06, 2, 8, 97.73, 307.59, 0.94, 15, 15.88, 306.25, 0.06, 2, 8, 171.37, 297.38, 0.94, 15, 89.52, 296.04, 0.06, 2, 8, 245.01, 287.17, 0.94, 15, 163.17, 285.83, 0.06, 2, 8, 312.79, 265.37, 0.94, 15, 230.94, 264.04, 0.06, 2, 8, 399.76, 221.57, 0.94, 15, 317.91, 220.23, 0.06, 2, 8, 469.49, 117.77, 0.94, 15, 387.65, 116.43, 0.06, 2, 8, 490.38, 13.46, 0.94, 15, 408.53, 12.13, 0.06, 2, 8, 497.62, 26.99, 0.94, 15, 415.77, 25.65, 0.06, 2, 8, 509.69, 9.37, 0.94, 15, 427.85, 8.03, 0.06, 2, 8, 289.71, 188.73, 0.32286, 15, 207.86, 187.39, 0.67714, 2, 8, 339.32, 145.36, 0.32286, 15, 257.47, 144.02, 0.67714, 2, 8, 367, 75.47, 0.32286, 15, 285.15, 74.14, 0.67714, 2, 8, 351.22, 5.53, 0.32286, 15, 269.38, 4.19, 0.67714, 2, 8, 312.82, -39.55, 0.21143, 15, 230.97, -40.89, 0.78857, 2, 8, 262.67, -69.1, 0.16571, 15, 180.82, -70.43, 0.83429, 2, 8, 301.21, -117.02, 0.22857, 15, 219.37, -118.36, 0.77143, 2, 8, 319.73, -177.81, 0.32286, 15, 237.88, -179.15, 0.67714, 2, 8, 301.32, -229.14, 0.32286, 15, 219.47, -230.48, 0.67714, 2, 8, 212.48, 214.76, 0.32286, 15, 130.63, 213.42, 0.67714, 2, 8, 157.43, 211.74, 0.32286, 15, 75.58, 210.41, 0.67714, 2, 8, 128.69, 193.74, 0.32286, 15, 46.84, 192.4, 0.67714, 2, 8, 90.34, 224.2, 0.32286, 15, 8.5, 222.86, 0.67714, 2, 8, 34.35, 227.97, 0.32286, 15, -47.5, 226.63, 0.67714, 2, 8, -16.23, 200.73, 0.54857, 15, -98.08, 199.39, 0.45143, 2, 8, 57.37, -242.79, 0.32286, 15, -24.47, -244.13, 0.67714, 2, 8, 105.39, -269.42, 0.32286, 15, 23.55, -270.76, 0.67714, 2, 8, 170.36, -278.43, 0.32286, 15, 88.52, -279.77, 0.67714, 2, 8, 232.79, -272.81, 0.32286, 15, 150.95, -274.15, 0.67714, 2, 8, 272.07, -257.13, 0.32286, 15, 190.23, -258.47, 0.67714, 2, 8, 207.39, 114.91, 0.32286, 15, 125.55, 113.57, 0.67714, 2, 8, 216.02, 120.95, 0.32286, 15, 134.17, 119.61, 0.67714, 2, 8, 224.48, 101.71, 0.32286, 15, 142.64, 100.37, 0.67714, 2, 8, 214.55, 97.97, 0.32286, 15, 132.7, 96.63, 0.67714, 2, 8, 212.37, 60.76, 0.32286, 15, 130.52, 59.43, 0.67714, 2, 8, 221.21, 58.46, 0.32286, 15, 139.36, 57.13, 0.67714, 2, 8, 211.29, 41.76, 0.32286, 15, 129.45, 40.42, 0.67714, 2, 8, 205.23, 45.53, 0.32286, 15, 123.38, 44.19, 0.67714, 2, 8, 184.02, 18.76, 0.32286, 15, 102.17, 17.42, 0.67714, 2, 8, 163.99, 11.46, 0.32286, 15, 82.15, 10.12, 0.67714, 2, 8, 138.14, 10.66, 0.32286, 15, 56.3, 9.32, 0.67714, 2, 8, 196.23, 129.64, 0.32286, 15, 114.38, 128.3, 0.67714, 2, 8, 202.92, 136.08, 0.32286, 15, 121.07, 134.74, 0.67714, 2, 8, 183.13, 139.27, 0.32286, 15, 101.28, 137.93, 0.67714, 2, 8, 187.34, 147.25, 0.32286, 15, 105.49, 145.92, 0.67714, 2, 8, 162.24, 148.49, 0.32286, 15, 80.39, 147.16, 0.67714, 2, 8, 163.55, 156.01, 0.32286, 15, 81.71, 154.67, 0.67714, 2, 8, 138.11, 156.02, 0.32286, 15, 56.26, 154.69, 0.67714, 2, 8, 138.88, 148.71, 0.32286, 15, 57.04, 147.37, 0.67714, 2, 8, 113.59, 139.53, 0.32286, 15, 31.75, 138.2, 0.67714, 2, 8, 110.17, 144.39, 0.32286, 15, 28.33, 143.06, 0.67714, 2, 8, 89.08, 123.04, 0.32286, 15, 7.23, 121.7, 0.67714, 2, 8, 93.44, 118.64, 0.32286, 15, 11.59, 117.3, 0.67714, 2, 8, 83.67, 95.8, 0.32286, 15, 1.83, 94.47, 0.67714, 2, 8, 78.99, 96.65, 0.32286, 15, -2.86, 95.32, 0.67714, 2, 8, 82.91, 68.74, 0.32286, 15, 1.07, 67.41, 0.67714, 2, 8, 77.63, 67.89, 0.32286, 15, -4.21, 66.55, 0.67714, 2, 8, 89.02, 48.18, 0.32286, 15, 7.17, 46.84, 0.67714, 2, 8, 84.24, 45.91, 0.32286, 15, 2.39, 44.58, 0.67714, 2, 8, 102.12, 31.44, 0.32286, 15, 20.28, 30.1, 0.67714, 2, 8, 97.83, 27.94, 0.32286, 15, 15.99, 26.6, 0.67714, 2, 8, 118.09, 20.72, 0.32286, 15, 36.25, 19.38, 0.67714, 2, 8, 116.32, 15.73, 0.32286, 15, 34.48, 14.39, 0.67714, 2, 8, 181.11, 21.87, 0.32286, 15, 99.26, 20.53, 0.67714, 2, 8, 139.15, 13.96, 0.32286, 15, 57.31, 12.62, 0.67714, 2, 8, 149.42, 80.03, 0.32286, 15, 67.57, 78.69, 0.67714, 2, 8, 113.44, -161.71, 0.32286, 15, 31.59, -163.04, 0.67714, 2, 8, 116.34, -92.69, 0.32286, 15, 34.49, -94.03, 0.67714, 2, 8, 115.59, -98.07, 0.32286, 15, 33.75, -99.41, 0.67714, 2, 8, 133.3, -102.16, 0.32286, 15, 51.45, -103.49, 0.67714, 2, 8, 151.03, -111.06, 0.32286, 15, 69.18, -112.4, 0.67714, 2, 8, 166.49, -123.55, 0.32286, 15, 84.64, -124.89, 0.67714, 2, 8, 175.37, -138.59, 0.32286, 15, 93.52, -139.93, 0.67714, 2, 8, 179.14, -156.01, 0.32286, 15, 97.3, -157.35, 0.67714, 2, 8, 177.3, -172.14, 0.32286, 15, 95.45, -173.47, 0.67714, 2, 8, 170.06, -188.03, 0.32286, 15, 88.22, -189.37, 0.67714, 2, 8, 160.72, -199.5, 0.32286, 15, 78.88, -200.84, 0.67714, 2, 8, 148.94, -208.19, 0.32286, 15, 67.09, -209.53, 0.67714, 2, 8, 136.84, -213.75, 0.32286, 15, 55, -215.09, 0.67714, 2, 8, 121.04, -216.98, 0.32286, 15, 39.2, -218.31, 0.67714, 2, 8, 104.69, -216.77, 0.32286, 15, 22.84, -218.11, 0.67714, 2, 8, 103.51, -223.45, 0.32286, 15, 21.66, -224.78, 0.67714, 2, 8, 119.98, -225.6, 0.32286, 15, 38.13, -226.94, 0.67714, 2, 8, 139.54, -223.15, 0.32286, 15, 57.69, -224.49, 0.67714, 2, 8, 193.89, 32.22, 0.32286, 15, 112.04, 30.88, 0.67714, 2, 8, 198.01, 27.6, 0.32286, 15, 116.16, 26.26, 0.67714, 2, 8, 216.02, 78.39, 0.32286, 15, 134.17, 77.06, 0.67714, 2, 8, 225.86, 79, 0.32286, 15, 144.01, 77.66, 0.67714, 2, 8, 162.67, 14.25, 0.32286, 15, 80.82, 12.91, 0.67714, 2, 8, 87.54, -212.99, 0.32286, 15, 5.7, -214.32, 0.67714, 2, 8, 85.4, -219.57, 0.32286, 15, 3.56, -220.91, 0.67714, 2, 8, 72.81, -206.44, 0.32286, 15, -9.04, -207.78, 0.67714, 2, 8, 69.47, -210.97, 0.32286, 15, -12.37, -212.31, 0.67714, 2, 8, 61.75, -197.08, 0.32286, 15, -20.1, -198.42, 0.67714, 2, 8, 58.19, -200.85, 0.32286, 15, -23.65, -202.18, 0.67714, 2, 8, 54.2, -187.11, 0.32286, 15, -27.65, -188.44, 0.67714, 2, 8, 49.06, -189.91, 0.32286, 15, -32.78, -191.25, 0.67714, 2, 8, 48.73, -172.54, 0.32286, 15, -33.11, -173.88, 0.67714, 2, 8, 42.07, -174.49, 0.32286, 15, -39.77, -175.83, 0.67714, 2, 8, 47.32, -158.5, 0.32286, 15, -34.52, -159.83, 0.67714, 2, 8, 40.47, -158.86, 0.32286, 15, -41.37, -160.19, 0.67714, 2, 8, 49.05, -143.66, 0.32286, 15, -32.8, -145, 0.67714, 2, 8, 42.77, -142.3, 0.32286, 15, -39.08, -143.64, 0.67714, 2, 8, 55.16, -129.11, 0.32286, 15, -26.68, -130.45, 0.67714, 2, 8, 50.73, -126.2, 0.32286, 15, -31.11, -127.54, 0.67714, 2, 8, 66.48, -115.02, 0.32286, 15, -15.36, -116.36, 0.67714, 2, 8, 62.74, -110.08, 0.32286, 15, -19.1, -111.41, 0.67714, 2, 8, 81.35, -104.86, 0.32286, 15, -0.5, -106.2, 0.67714, 2, 8, 78.75, -99.34, 0.32286, 15, -3.09, -100.68, 0.67714, 2, 8, 98.12, -99.28, 0.32286, 15, 16.28, -100.62, 0.67714, 2, 8, 96.67, -93.18, 0.32286, 15, 14.83, -94.52, 0.67714, 2, 8, 50.84, 156.21, 0.39579, 15, -31, 154.87, 0.60421, 2, 8, 18.98, 81.55, 0.53313, 15, -62.87, 80.22, 0.46687, 2, 8, 44.02, -32.72, 0.41123, 15, -37.82, -34.06, 0.58877, 2, 8, 118.09, -49.44, 0.32286, 15, 36.24, -50.78, 0.67714, 2, 8, 201.69, -59.42, 0.32286, 15, 119.85, -60.76, 0.67714, 2, 8, 2.04, -101.56, 0.50439, 15, -79.8, -102.9, 0.49561, 2, 8, -4.95, -194.73, 0.56352, 15, -86.8, -196.07, 0.43648, 2, 8, 190.79, -155.27, 0.32286, 15, 108.94, -156.61, 0.67714, 2, 8, 183.49, -134.65, 0.32286, 15, 101.65, -135.98, 0.67714, 2, 8, 171.44, -120.05, 0.32286, 15, 89.59, -121.39, 0.67714, 2, 8, 155.93, -107.06, 0.32286, 15, 74.08, -108.39, 0.67714, 2, 8, 134.13, -96.19, 0.32286, 15, 52.28, -97.53, 0.67714, 2, 8, 190.32, -174.51, 0.32286, 15, 108.48, -175.84, 0.67714, 2, 8, 181.53, -192.98, 0.32286, 15, 99.69, -194.31, 0.67714, 2, 8, 171.39, -206.46, 0.32286, 15, 89.54, -207.8, 0.67714, 2, 8, 155.39, -216.88, 0.32286, 15, 73.55, -218.22, 0.67714], "hull": 32}}, "L_hand_spline": {"L_hand_spline": {"type": "path", "lengths": [259.23, 535.82], "vertexCount": 6, "vertices": [1, 20, -75.48, 9.43, 1, 1, 20, 0, -0.98, 1, 1, 20, 101.11, -14.93, 1, 1, 21, -11.33, 31.59, 1, 1, 21, 33.18, -44.9, 1, 1, 20, 249.51, -259.34, 1]}}, "monkey_body": {"monkey new/monkey_body": {"type": "mesh", "uvs": [0.76075, 0.01677, 0.89578, 0.14145, 0.98745, 0.32782, 1, 0.54933, 1, 0.71552, 0.94661, 0.87034, 0.83846, 0.97948, 0.73005, 0.94194, 0.59806, 0.99458, 0.34022, 0.99574, 0.20198, 0.96113, 0.10619, 0.86526, 0.00045, 0.58696, 0.00124, 0.3459, 0.07049, 0.17558, 0.14799, 0.077, 0.33978, 0.00204], "triangles": [16, 14, 15, 1, 16, 0, 3, 16, 2, 14, 16, 13, 2, 16, 1, 8, 9, 11, 16, 7, 8, 6, 7, 5, 10, 11, 9, 16, 8, 11, 5, 7, 3, 16, 3, 7, 4, 5, 3, 16, 11, 12, 13, 16, 12], "vertices": [1, 4, 165.68, -83.81, 1, 2, 3, 231.92, -135.84, 0.01184, 4, 106.37, -132.57, 0.98816, 2, 3, 148.76, -159.77, 0.20845, 4, 24.21, -159.73, 0.79155, 2, 3, 57.15, -148.45, 0.6759, 4, -67.76, -152, 0.3241, 2, 3, -10.86, -136.07, 0.90872, 4, -136.21, -142.28, 0.09128, 2, 3, -70.22, -102.51, 0.98667, 4, -196.83, -111.07, 0.01333, 1, 3, -106.77, -49.77, 1, 1, 3, -83.27, -7.86, 1, 1, 3, -94.9, 50.51, 1, 1, 3, -76.02, 156.95, 1, 1, 3, -51.48, 211.39, 1, 2, 3, -5.05, 243.76, 0.99789, 4, -145.25, 237.48, 0.00211, 2, 3, 116.79, 266.64, 0.90329, 4, -24.39, 265.11, 0.09671, 2, 3, 215.39, 248.36, 0.69794, 4, 74.85, 250.7, 0.30206, 2, 3, 279.9, 207.11, 0.49321, 4, 140.92, 212, 0.50679, 2, 3, 314.43, 167.8, 0.36459, 4, 176.96, 174.07, 0.63541, 2, 3, 330.71, 83.11, 0.14651, 4, 196.54, 90.08, 0.85349], "hull": 17}}, "monkey_ear left": {"monkey new/monkey_ear left": {"type": "mesh", "uvs": [0.89386, 0.0596, 1, 0.1315, 1, 0.30886, 0.85127, 0.90398, 0.84714, 1, 0.70216, 1, 0.44722, 0.96019, 0.24523, 0.85943, 0.09118, 0.73227, 0, 0.55029, 0, 0.41007, 0.05502, 0.22488, 0.20739, 0.09832, 0.38791, 0.02117, 0.56847, 0, 0.72332, 0.00713, 0.82706, 0.41407, 0.73189, 0.29313, 0.54153, 0.23373, 0.3607, 0.25494, 0.22427, 0.37164, 0.19572, 0.51803, 0.28773, 0.66867, 0.44635, 0.77475, 0.69064, 0.83628], "triangles": [18, 13, 14, 19, 12, 13, 18, 19, 13, 11, 12, 19, 17, 15, 0, 17, 0, 1, 17, 1, 2, 20, 11, 19, 10, 11, 20, 16, 17, 2, 21, 10, 20, 18, 14, 15, 18, 15, 17, 9, 10, 21, 20, 17, 21, 21, 16, 22, 20, 19, 18, 20, 18, 17, 16, 21, 17, 8, 9, 21, 8, 21, 22, 23, 22, 16, 24, 23, 16, 7, 8, 22, 7, 22, 23, 3, 16, 2, 24, 16, 3, 6, 23, 24, 7, 23, 6, 5, 24, 3, 6, 24, 5, 4, 5, 3], "vertices": [1, 8, 327.52, 253.83, 1, 1, 8, 306.47, 238.11, 1, 1, 8, 260.79, 244.44, 1, 1, 8, 111.07, 291.3, 1, 1, 8, 86.44, 295.44, 1, 1, 8, 89.9, 320.41, 1, 1, 8, 106.24, 362.9, 1, 1, 8, 137.01, 394.09, 1, 1, 8, 173.44, 416.08, 1, 1, 8, 222.48, 425.29, 1, 1, 8, 258.6, 420.28, 1, 1, 8, 304.98, 404.2, 1, 1, 8, 333.93, 373.44, 1, 1, 8, 349.49, 339.59, 1, 1, 8, 350.63, 307.74, 1, 1, 8, 345.1, 281.33, 1, 1, 8, 237.82, 277.98, 1, 1, 8, 271.24, 290.06, 1, 1, 8, 291.08, 320.72, 1, 1, 8, 289.94, 352.63, 1, 1, 8, 263.14, 380.29, 1, 1, 8, 226.12, 390.43, 1, 1, 8, 185.13, 379.96, 1, 1, 8, 154.02, 356.43, 1, 1, 8, 132.34, 316.55, 1], "hull": 16}}, "monkey_ear right": {"monkey new/monkey_ear right": {"type": "mesh", "uvs": [0.47958, 0.01438, 0.6836, 0.06186, 0.8227, 0.13794, 0.93216, 0.24966, 1, 0.39831, 0.96257, 0.57493, 0.8753, 0.71552, 0.74267, 0.82151, 0.56387, 0.90287, 0.42686, 0.94733, 0.22183, 0.94659, 0.18119, 0.81413, 0.13, 0.64727, 0.08854, 0.51213, 0.04584, 0.37294, 0.05599, 0.05463, 0.24263, 0.01461, 0.22924, 0.35432, 0.37605, 0.25908, 0.49954, 0.23031, 0.72566, 0.28903, 0.80356, 0.40508, 0.77477, 0.54397, 0.57929, 0.7531, 0.70081, 0.65897, 0.62512, 0.24168], "triangles": [19, 0, 1, 25, 19, 1, 2, 25, 1, 20, 25, 2, 19, 18, 16, 19, 16, 0, 15, 16, 18, 3, 20, 2, 17, 15, 18, 14, 15, 17, 21, 20, 3, 4, 21, 3, 13, 14, 17, 21, 25, 20, 4, 22, 21, 5, 22, 4, 24, 12, 13, 25, 22, 19, 21, 22, 25, 5, 24, 22, 6, 24, 5, 18, 19, 17, 22, 24, 19, 19, 24, 17, 17, 24, 13, 23, 12, 24, 11, 12, 23, 6, 23, 24, 7, 23, 6, 8, 11, 23, 8, 23, 7, 9, 10, 11, 8, 9, 11], "vertices": [1, 8, 238.69, -355.04, 1, 1, 8, 222.94, -380.97, 1, 1, 8, 201.23, -397.13, 1, 1, 8, 171.13, -408.04, 1, 1, 8, 132.53, -412.04, 1, 1, 8, 88.88, -400.83, 1, 1, 8, 55.22, -384.14, 1, 1, 8, 31.09, -362.52, 1, 1, 8, 14.01, -335.51, 1, 1, 8, 5.41, -315.44, 1, 1, 8, 9.44, -287.74, 1, 1, 8, 43.46, -286.86, 1, 1, 8, 86.32, -285.75, 1, 1, 8, 121.03, -284.84, 1, 1, 8, 156.78, -283.91, 1, 1, 8, 236.52, -296.36, 1, 1, 8, 243.07, -322.99, 1, 1, 8, 158.02, -309.36, 1, 1, 8, 179.19, -332.52, 1, 1, 8, 184.1, -350.22, 1, 1, 8, 165.11, -378.75, 1, 1, 8, 134.51, -385.24, 1, 1, 8, 100.18, -376.52, 1, 1, 8, 51.33, -342.81, 1, 1, 8, 72.69, -362.52, 1, 1, 8, 178.89, -366.8, 1], "hull": 17}}, "monkey_eyebrow left": {"monkey new/monkey_eyebrow left": {"type": "mesh", "uvs": [0.48935, 0.0084, 0.89936, 0.00849, 1, 0.1922, 1, 0.27628, 0.96546, 0.40699, 0.61563, 0.4704, 0.41292, 0.61573, 0.09077, 0.99351, 0, 0.99081, 0, 0.92838, 0.0682, 0.55424, 0.27427, 0.1806], "triangles": [1, 2, 3, 4, 1, 3, 5, 0, 1, 5, 1, 4, 6, 11, 0, 6, 0, 5, 10, 11, 6, 7, 8, 9, 7, 10, 6, 7, 9, 10], "vertices": [1, 13, 14.33, 6.46, 1, 1, 13, 9.21, 43.42, 1, 1, 13, -1.22, 51.22, 1, 1, 13, -5.41, 50.64, 1, 1, 13, -11.5, 46.62, 1, 1, 13, -10.3, 14.65, 1, 1, 13, -15.02, -4.62, 1, 1, 13, -29.84, -36.28, 1, 1, 13, -28.57, -44.44, 1, 1, 13, -25.46, -44.01, 1, 1, 13, -7.64, -35.27, 1, 1, 13, 8.43, -14.11, 1], "hull": 12}}, "monkey_eyebrow right": {"monkey new/monkey_eyebrow right": {"type": "mesh", "uvs": [0.72592, 0.17544, 0.93916, 0.54043, 1, 0.89232, 1, 0.96427, 0.9023, 0.96699, 0.61845, 0.62858, 0.48873, 0.55025, 0.00695, 0.36956, 0.00484, 0.15777, 0.10952, 0.03563, 0.49246, 0.03579], "triangles": [7, 8, 9, 6, 9, 10, 6, 10, 0, 5, 6, 0, 7, 9, 6, 1, 5, 0, 4, 5, 1, 2, 4, 1, 3, 4, 2], "vertices": [1, 26, 7.21, -15.83, 1, 1, 26, -15.98, -27.66, 1, 1, 26, -36.96, -29.04, 1, 1, 26, -41.13, -28.46, 1, 1, 26, -40.35, -21.68, 1, 1, 26, -18.01, -4.75, 1, 1, 26, -12.23, 3.6, 1, 1, 26, 2.86, 35.49, 1, 1, 26, 15.15, 33.94, 1, 1, 26, 21.23, 25.71, 1, 1, 26, 17.55, -0.79, 1], "hull": 11}}, "monkey_eyebrow_angry": {"monkey new/monkey_eyebrow_angry": {"type": "mesh", "uvs": [0.93918, 0.353, 0.96921, 0.66541, 0.74868, 0.80961, 0.47474, 0.99049, 0.21514, 0.78589, 0.02926, 0.61427, 0.02289, 0.484, 0.03438, 0.44093, 0.11947, 0.2643, 0.23843, 0.1392, 0.47691, 0.44794, 0.53597, 0.47124, 0.74392, 0.20643, 0.82412, 0.18375], "triangles": [0, 2, 12, 3, 11, 2, 3, 10, 11, 3, 4, 10, 5, 7, 4, 7, 8, 4, 4, 9, 10, 4, 8, 9, 11, 12, 2, 5, 6, 7, 2, 0, 1, 0, 12, 13], "vertices": [2, 8, 212.01, -254.43, 0.55714, 15, 130.16, -255.77, 0.44286, 2, 8, 141.77, -258.29, 0.51143, 15, 59.92, -259.63, 0.48857, 2, 8, 123.78, -155.95, 0.00571, 15, 41.93, -157.29, 0.99429, 2, 8, 101.05, -28.76, 0.10571, 15, 19.21, -30.1, 0.89429, 2, 8, 161.83, 80.36, 0.15714, 15, 79.98, 79.03, 0.84286, 2, 8, 210.84, 157.73, 0.50286, 15, 128.99, 156.39, 0.49714, 2, 8, 239.75, 156.61, 0.47143, 15, 157.9, 155.27, 0.52857, 2, 8, 248.47, 150.2, 0.45714, 15, 166.62, 148.86, 0.54286, 2, 8, 281.87, 107.07, 0.25143, 15, 200.03, 105.73, 0.74857, 2, 8, 301.96, 50.39, 0.24571, 15, 220.11, 49.06, 0.75429, 2, 8, 219.69, -46.18, 0.09714, 15, 137.84, -47.52, 0.90286, 2, 8, 210.95, -71.71, 0.25714, 15, 129.1, -73.05, 0.74286, 2, 8, 256.11, -172.13, 0.55714, 15, 174.27, -173.47, 0.44286, 2, 8, 256.14, -208.45, 0.55714, 15, 174.29, -209.79, 0.44286], "hull": 14}}, "monkey_eye_left_iris": {"monkey new/monkey_eye_left_iris": {"type": "mesh", "uvs": [0.75151, 0.07823, 0.9311, 0.28907, 0.93046, 0.69321, 0.66284, 0.94412, 0.44009, 0.95626, 0.19276, 0.88108, 0.03004, 0.64475, 0.02908, 0.24689, 0.28568, 0.00358, 0.59541, 0.0036], "triangles": [9, 6, 7, 0, 1, 6, 5, 6, 1, 8, 9, 7, 0, 6, 9, 1, 4, 5, 1, 2, 4, 3, 4, 2], "vertices": [1, 27, 48.47, -40.09, 1, 1, 27, 18.28, -58.29, 1, 1, 27, -33.75, -51, 1, 1, 27, -61.53, -13.79, 1, 1, 27, -59.31, 13.68, 1, 1, 27, -45.44, 42.59, 1, 1, 27, -12.25, 58.28, 1, 1, 27, 39, 51.3, 1, 1, 27, 65.98, 15.57, 1, 1, 27, 60.73, -22.32, 1], "hull": 10}}, "monkey_eye_rioht_iris": {"monkey new/monkey_eye_rioht_iris": {"type": "mesh", "uvs": [0.71101, 0.00343, 0.93538, 0.18899, 0.99653, 0.3391, 0.99584, 0.64153, 0.79944, 0.84876, 0.41175, 0.95785, 0.03513, 0.70272, 0.03104, 0.34453, 0.11259, 0.16549, 0.32616, 0.0034], "triangles": [0, 1, 9, 2, 6, 7, 2, 9, 1, 3, 4, 2, 9, 2, 8, 4, 5, 2, 8, 2, 7, 5, 6, 2], "vertices": [1, 16, 54.65, -30.31, 1, 1, 16, 26.76, -51.47, 1, 1, 16, 6.02, -55.42, 1, 1, 16, -33.89, -49.81, 1, 1, 16, -58.26, -24.52, 1, 1, 16, -66.78, 19.91, 1, 1, 16, -27.39, 56.46, 1, 1, 16, 19.94, 50.36, 1, 1, 16, 42.34, 38.16, 1, 1, 16, 60.49, 11.82, 1], "hull": 10}}, "monkey_hand left": {"monkey new/monkey_hand left": {"type": "mesh", "uvs": [0.17755, 0, 0.39376, 0.06858, 0.42263, 0.27147, 0.47189, 0.30997, 0.7852, 0.32555, 0.85284, 0.45392, 0.8546, 0.63218, 0.80178, 0.83176, 0.74952, 0.88976, 0.14466, 0.83084, 0.08578, 0.80165, 0.00672, 0.59772, 0.00622, 0.34285, 0.06507, 0.11823, 0.15478, 0], "triangles": [7, 8, 6, 6, 8, 3, 6, 4, 5, 3, 4, 6, 8, 9, 3, 9, 2, 3, 0, 2, 11, 11, 14, 0, 0, 1, 2, 11, 2, 9, 9, 10, 11, 13, 14, 12, 14, 11, 12], "vertices": [1, 41, -21.15, -7.78, 1, 2, 41, 1.25, 58.31, 0.9043, 42, 38.36, 109.62, 0.0957, 3, 41, 50.65, 63.04, 0.51211, 42, 52.3, 61.99, 0.47062, 43, -49.03, 52.53, 0.01727, 3, 41, 61.23, 77.61, 0.22356, 42, 68.6, 54.34, 0.65045, 43, -31.44, 48.64, 0.12599, 2, 42, 166.74, 60.54, 0.00091, 43, 62.96, 76.15, 0.99909, 1, 43, 92.85, 53.57, 1, 1, 43, 106.93, 13.08, 1, 2, 42, 184.22, -60.06, 0.00557, 43, 106.39, -37.7, 0.99443, 2, 42, 169.32, -75.59, 0.02186, 43, 95.25, -56.11, 0.97814, 3, 41, 176.98, -35.66, 0.42898, 42, -20.84, -80.66, 0.57072, 43, -89.19, -102.66, 0.00029, 2, 41, 168.36, -53.43, 0.54708, 42, -39.92, -75.54, 0.45292, 2, 41, 117.32, -73.81, 0.92487, 42, -69.54, -29.25, 0.07513, 1, 41, 56.25, -68.56, 1, 1, 41, 4.06, -45.42, 1, 1, 41, -21.78, -14.89, 1], "hull": 15}}, "monkey_hand right": {"monkey new/monkey_hand right": {"type": "mesh", "uvs": [0.80949, 0.17706, 0.87636, 0.3083, 0.87681, 0.42931, 0.6672, 0.72658, 0.49236, 0.84704, 0.2893, 0.84754, 0.15469, 0.77973, 0.15328, 0.48818, 0.48507, 0.15921, 0.72237, 0.13461], "triangles": [3, 8, 2, 9, 1, 8, 0, 1, 9, 2, 8, 1, 7, 8, 3, 7, 5, 6, 3, 4, 7, 4, 5, 7], "vertices": [1, 25, 43.11, -81.53, 1, 1, 25, 19.34, -79.11, 1, 1, 25, 2.73, -68.58, 1, 1, 25, -19.81, -14.16, 1, 1, 25, -21.16, 20.09, 1, 1, 25, -3.64, 47.66, 1, 1, 25, 17.31, 59.97, 1, 1, 25, 57.35, 34.65, 1, 1, 25, 73.65, -39.12, 1, 1, 25, 56.46, -73.44, 1], "hull": 10}}, "monkey_left_arm": {"monkey new/monkey_left_arm": {"type": "mesh", "uvs": [0.92224, 0.1061, 1, 0.30568, 1, 0.39398, 0.69052, 0.61082, 0.63077, 0.68042, 0.57122, 0.81257, 0.4878, 0.99765, 0.34202, 0.99785, 0.19625, 0.99805, 0.03644, 0.79835, 0.03473, 0.56959, 0.14551, 0.37566, 0.29737, 0.18352, 0.43329, 0.09775, 0.56922, 0.01198, 0.67252, 0.01038, 0.77582, 0.00878], "triangles": [10, 7, 9, 10, 11, 7, 6, 7, 5, 8, 9, 7, 4, 13, 3, 12, 13, 4, 11, 12, 4, 4, 5, 11, 7, 11, 5, 14, 3, 13, 3, 0, 1, 3, 15, 16, 3, 16, 0, 2, 3, 1, 3, 14, 15], "vertices": [1, 9, -6.47, 7.73, 1, 1, 9, -7.51, -34.22, 1, 1, 9, 0.01, -49.31, 1, 2, 9, 90.31, -55.18, 0.39708, 10, 15.14, -52.82, 0.60292, 3, 9, 110.11, -61.05, 0.17687, 10, 33.09, -51.76, 0.6917, 11, -38.68, -62.22, 0.13143, 2, 10, 62.41, -57.82, 0.78606, 11, -32.85, -52.99, 0.21394, 2, 10, 94.89, -69.05, 0.40898, 11, 27.96, -64.23, 0.59102, 2, 10, 130.47, -28.37, 0.20492, 11, 39.26, -35.64, 0.79508, 2, 10, 140.01, -19.23, 0.00086, 11, 63.14, -4.83, 0.99914, 2, 10, 139.73, 34.38, 0.06883, 11, 51.3, 47.37, 0.93117, 2, 10, 111.36, 64.77, 0.39098, 11, 15.87, 70.19, 0.60902, 2, 10, 69.97, 71.34, 0.84073, 11, -27.67, 66.65, 0.15927, 3, 9, 145.15, 57.46, 0.06, 10, 22.45, 70.65, 0.82895, 11, -75.9, 54.56, 0.11105, 3, 9, 84.45, 64.36, 0.28006, 10, 6.79, 65.72, 0.71967, 11, -66.48, 78.25, 0.00027, 2, 9, 67.44, 59.39, 0.45323, 10, -41.05, 46.72, 0.54677, 2, 9, 42.33, 48.15, 0.88376, 10, -38.15, 61.52, 0.11624, 1, 9, 19.22, 39.12, 1], "hull": 17}}, "monkey_left_foot": {"monkey new/monkey_left_foot": {"type": "mesh", "uvs": [0.51381, 0, 0.63095, 0.04438, 0.97806, 0.60205, 0.98156, 0.83442, 0.78973, 0.96946, 0.50061, 0.96925, 0.32516, 0.87906, 0, 0.60016, 0, 0.54193, 0.02057, 0.44143, 0.14637, 0.20176, 0.47659, 0], "triangles": [1, 10, 11, 11, 0, 1, 6, 8, 9, 9, 10, 6, 1, 6, 10, 6, 7, 8, 3, 4, 2, 4, 5, 2, 5, 6, 2, 6, 1, 2], "vertices": [2, 34, 143.1, 78.6, 0.33412, 36, 68.13, 76.47, 0.66588, 2, 34, 122.21, 85.93, 0.43006, 36, 47.64, 84.86, 0.56994, 1, 34, 12.94, 50.67, 1, 1, 34, -13.36, 19.99, 1, 2, 34, -2.25, -19.78, 0.9864, 36, -82.02, -14.39, 0.0136, 2, 34, 37.16, -52.49, 0.59505, 36, -44.33, -49.06, 0.40495, 2, 34, 71.08, -60.3, 0.16477, 36, -10.85, -58.58, 0.83523, 1, 36, 64.36, -61.94, 1, 1, 36, 71.22, -54.49, 1, 1, 36, 80.38, -39.17, 1, 2, 34, 170.71, 10.01, 0.00461, 36, 92.23, 6.58, 0.99539, 2, 34, 148.16, 74.38, 0.31388, 36, 72.98, 72.01, 0.68612], "hull": 12}}, "monkey_left_hand": {"monkey new/monkey_left_hand": {"type": "mesh", "uvs": [0.69611, 0.05919, 0.89762, 0.16529, 0.9861, 0.37497, 0.98745, 0.63892, 0.53871, 0.98914, 0.41824, 0.98776, 0.08882, 0.78775, 0.00571, 0.74995, 0.03731, 0.2088, 0.18464, 0.11424, 0.48001, 0.04048], "triangles": [6, 8, 9, 7, 8, 6, 10, 6, 9, 4, 6, 10, 0, 2, 10, 2, 0, 1, 3, 10, 2, 6, 4, 5, 10, 3, 4], "vertices": [-5.76, 50.84, -20.31, 19.61, -11.59, -14.91, 12.69, -48.69, 99.41, -53.94, 113.86, -43.19, 135.18, 11.26, 141.74, 23.38, 87.79, 89.63, 61.21, 88.75, 18.65, 72.21], "hull": 11}}, "monkey_left_hand closed": {"monkey new/monkey_left_hand closed": {"type": "mesh", "uvs": [0.36629, 0, 0.64659, 0.02998, 0.88139, 0.1799, 1, 0.54836, 1, 0.65331, 0.62554, 1, 0.57649, 1, 0.46662, 0.94786, 0.2033, 0.90475, 0.06961, 0.64006, 0, 0.46837, 0, 0.38962, 0.10795, 0.09831, 0.20691, 0], "triangles": [9, 10, 11, 12, 9, 11, 7, 2, 3, 12, 0, 9, 0, 8, 9, 7, 0, 1, 7, 1, 2, 7, 5, 6, 13, 0, 12, 7, 8, 0, 7, 3, 5, 4, 5, 3], "vertices": [12.52, 49.27, -7.06, 13.09, -11.26, -25.25, 15.25, -63.42, 25.51, -70.16, 89.48, -46.66, 93.42, -40.67, 97.14, -23.89, 114.07, 11.07, 98.93, 44.41, 87.73, 63.95, 80.03, 69.01, 42.88, 74.53, 25.32, 68.75], "hull": 14}}, "monkey_left_leg": {"monkey new/monkey_left_leg": {"type": "mesh", "uvs": [0.46321, 0, 0.69025, 0.08096, 0.93448, 0.32058, 1, 0.57188, 1, 0.74909, 0.82637, 0.98962, 0.47275, 0.99199, 0.21338, 0.83881, 0.01101, 0.55811, 0.00852, 0.26637, 0.13081, 0.03792, 0.35308, 0], "triangles": [11, 8, 9, 3, 7, 2, 10, 11, 9, 0, 7, 8, 0, 8, 11, 1, 7, 0, 7, 1, 2, 7, 3, 6, 4, 5, 3, 5, 6, 3], "vertices": [1, 33, 46.24, 154.65, 1, 1, 33, -0.03, 135.41, 1, 1, 33, -48.74, 81.27, 1, 1, 33, -60.5, 25.74, 1, 1, 33, -59.27, -13.12, 1, 1, 33, -21.78, -64.72, 1, 1, 33, 51.18, -62.92, 1, 1, 33, 103.61, -27.64, 1, 1, 33, 143.4, 35.23, 1, 1, 33, 141.88, 99.22, 1, 1, 33, 115.07, 148.51, 1, 1, 33, 68.96, 155.37, 1], "hull": 12}}, "monkey_mouth angry": {"monkey new/monkey_mouth angry": {"type": "mesh", "uvs": [0.47793, 0.24739, 0.57189, 0.25081, 0.75151, 0.17028, 0.8934, 0.3406, 0.94183, 0.51814, 0.80574, 0.84229, 0.62349, 0.9855, 0.33163, 0.97341, 0.09147, 0.71657, 0.05807, 0.47952, 0.14506, 0.24063, 0.35047, 0.12201, 0.49846, 0.47206, 0.41399, 0.48047, 0.32713, 0.53718, 0.25813, 0.60231, 0.22363, 0.69894, 0.25456, 0.74095, 0.29382, 0.68633, 0.34974, 0.64012, 0.41994, 0.5918, 0.49965, 0.5708, 0.58769, 0.56659, 0.59245, 0.47627, 0.68287, 0.51828, 0.66859, 0.5939, 0.71856, 0.65692, 0.75664, 0.575, 0.79828, 0.65272, 0.76972, 0.70944, 0.78995, 0.74935, 0.81374, 0.69684], "triangles": [6, 7, 20, 5, 6, 29, 7, 8, 17, 5, 31, 4, 8, 9, 16, 27, 3, 4, 3, 27, 2, 15, 9, 10, 14, 10, 11, 7, 19, 20, 20, 21, 6, 21, 22, 6, 5, 29, 30, 6, 26, 29, 6, 25, 26, 6, 22, 25, 8, 16, 17, 17, 18, 7, 7, 18, 19, 5, 30, 31, 30, 29, 31, 17, 16, 18, 29, 28, 31, 28, 29, 27, 16, 15, 18, 16, 9, 15, 31, 28, 4, 19, 18, 14, 29, 26, 27, 26, 25, 27, 28, 27, 4, 18, 15, 14, 19, 14, 20, 14, 15, 10, 25, 24, 27, 25, 22, 24, 14, 13, 20, 20, 13, 21, 13, 12, 21, 21, 12, 22, 22, 23, 24, 22, 12, 23, 13, 14, 11, 27, 24, 2, 2, 24, 1, 13, 0, 12, 13, 11, 0, 12, 1, 23, 24, 23, 1, 12, 0, 1], "vertices": [2, 40, 83.41, -13.78, 0.65143, 8, 59.22, -11.45, 0.34857, 2, 40, 77.97, -47.87, 0.65143, 8, 53.78, -45.54, 0.34857, 2, 40, 85.51, -115.5, 0.54857, 8, 61.32, -113.17, 0.45143, 2, 40, 43.26, -162.26, 0.54857, 8, 19.07, -159.93, 0.45143, 2, 40, 4.24, -174.8, 0.54857, 8, -19.95, -172.47, 0.45143, 2, 40, -55.68, -116.04, 0.54857, 8, -79.87, -113.72, 0.45143, 2, 40, -76, -45.65, 0.54857, 8, -100.19, -43.33, 0.45143, 2, 40, -58.79, 60.17, 0.54857, 8, -82.98, 62.5, 0.45143, 2, 40, 6.23, 140.2, 0.54857, 8, -17.96, 142.53, 0.45143, 2, 40, 56.76, 145.58, 0.54857, 8, 32.57, 147.91, 0.45143, 2, 40, 101.59, 107.11, 0.54857, 8, 77.4, 109.44, 0.45143, 2, 40, 115.67, 29.01, 0.54857, 8, 91.48, 31.33, 0.45143, 2, 40, 36.09, -14.83, 0.92, 8, 11.9, -12.51, 0.08, 2, 40, 38.62, 16.13, 0.92, 8, 14.43, 18.46, 0.08, 2, 40, 31.31, 49.35, 0.92, 8, 7.12, 51.68, 0.08, 2, 40, 21.37, 76.31, 0.92, 8, -2.82, 78.64, 0.08, 2, 40, 3.2, 91.62, 0.92, 8, -20.99, 93.95, 0.08, 2, 40, -7.01, 81.57, 0.92, 8, -31.2, 83.9, 0.08, 2, 40, 2.26, 65.73, 0.92, 8, -21.93, 68.05, 0.08, 2, 40, 8.96, 44.07, 0.92, 8, -15.23, 46.39, 0.08, 2, 40, 15.38, 17.15, 0.92, 8, -8.81, 19.48, 0.08, 2, 40, 15.69, -12.45, 0.92, 8, -8.5, -10.12, 0.08, 2, 40, 12.11, -44.59, 0.92, 8, -12.08, -42.27, 0.08, 2, 40, 30.48, -48.91, 0.92, 8, 6.29, -46.58, 0.08, 2, 40, 17.27, -80.6, 0.92, 8, -6.92, -78.27, 0.08, 2, 40, 2.41, -73.25, 0.92, 8, -21.78, -70.92, 0.08, 2, 40, -13.1, -89.62, 0.92, 8, -37.29, -87.29, 0.08, 2, 40, 1.86, -105.81, 0.92, 8, -22.33, -103.48, 0.08, 2, 40, -16.25, -118.74, 0.92, 8, -40.44, -116.41, 0.08, 2, 40, -26.49, -106.73, 0.92, 8, -50.69, -104.41, 0.08, 2, 40, -35.74, -112.95, 0.92, 8, -59.93, -110.62, 0.08, 2, 40, -26.12, -123.11, 0.92, 8, -50.31, -120.78, 0.08], "hull": 12}}, "monkey_mouth o": {"monkey new/monkey_mouth o": {"type": "mesh", "uvs": [0.38607, 0.1696, 0.52404, 0.28183, 0.59934, 0.28917, 0.72236, 0.17539, 0.85255, 0.3258, 0.85325, 0.6406, 0.7262, 0.85716, 0.57023, 0.90238, 0.45725, 0.93514, 0.30173, 0.83746, 0.09452, 0.60251, 0.0722, 0.4882, 0.15531, 0.4157, 0.16824, 0.35738, 0.1682, 0.18064, 0.18391, 0.14752, 0.45671, 0.54167, 0.451, 0.67357, 0.48035, 0.78349, 0.55616, 0.78977, 0.60182, 0.7034, 0.61486, 0.5715, 0.59692, 0.46158, 0.55535, 0.40348, 0.49421, 0.43175, 0.50862, 0.49076, 0.48886, 0.56184, 0.48726, 0.65209, 0.50731, 0.69863, 0.53673, 0.7081, 0.55654, 0.66253, 0.56864, 0.57214, 0.56204, 0.51521, 0.54144, 0.47553], "triangles": [13, 14, 15, 10, 11, 12, 15, 0, 13, 23, 1, 2, 24, 0, 1, 24, 1, 23, 22, 23, 2, 33, 24, 23, 33, 23, 22, 25, 24, 33, 32, 33, 22, 16, 0, 24, 16, 24, 25, 26, 16, 25, 3, 22, 2, 4, 22, 3, 4, 21, 22, 32, 22, 21, 31, 32, 21, 21, 4, 5, 27, 16, 26, 25, 33, 32, 26, 25, 32, 31, 30, 26, 31, 26, 32, 27, 26, 30, 17, 16, 27, 28, 27, 30, 20, 31, 21, 30, 31, 20, 29, 28, 30, 28, 18, 17, 28, 17, 27, 20, 19, 29, 20, 29, 30, 13, 0, 16, 17, 9, 16, 9, 17, 18, 16, 12, 13, 9, 12, 16, 10, 12, 9, 6, 21, 5, 20, 21, 6, 7, 19, 20, 7, 20, 6, 8, 9, 18, 18, 28, 29, 18, 29, 19, 7, 8, 18, 7, 18, 19], "vertices": [2, 40, 99.55, 26.98, 0.44571, 8, 75.36, 29.31, 0.55429, 2, 40, 63.8, -33.03, 0.87429, 8, 39.61, -30.7, 0.12571, 2, 40, 57.22, -67.57, 0.88, 8, 33.03, -65.24, 0.12, 2, 40, 76.64, -128.18, 0.44571, 8, 52.44, -125.85, 0.55429, 2, 40, 32.23, -183.32, 0.44571, 8, 8.04, -180.99, 0.55429, 2, 40, -43.32, -173.18, 0.44571, 8, -67.51, -170.85, 0.55429, 2, 40, -87.12, -107.29, 0.34571, 8, -111.31, -104.96, 0.65429, 2, 40, -87.98, -33.74, 0.48286, 8, -112.17, -31.41, 0.51714, 2, 40, -88.6, 19.55, 0.48857, 8, -112.79, 21.87, 0.51143, 2, 40, -55.22, 88.14, 0.71429, 8, -79.41, 90.47, 0.28571, 2, 40, 14.4, 176.06, 0.34857, 8, -9.8, 178.38, 0.65143, 2, 40, 43.24, 182.57, 0.44571, 8, 19.05, 184.89, 0.55429, 2, 40, 55.31, 141.76, 0.44571, 8, 31.11, 144.09, 0.55429, 2, 40, 68.47, 133.85, 0.44571, 8, 44.28, 136.18, 0.55429, 2, 40, 110.86, 127.99, 0.44571, 8, 86.66, 130.32, 0.55429, 2, 40, 117.79, 119.64, 0.44571, 8, 93.6, 121.96, 0.55429, 2, 40, 5.8, 6.72, 0.93143, 8, -18.39, 9.04, 0.06857, 2, 40, -25.47, 13.74, 0.93143, 8, -49.66, 16.07, 0.06857, 2, 40, -53.71, 3.83, 0.85429, 8, -77.9, 6.16, 0.14571, 2, 40, -60.07, -30.98, 0.83143, 8, -84.26, -28.65, 0.16857, 2, 40, -42.28, -54.94, 0.86286, 8, -66.47, -52.61, 0.13714, 2, 40, -11.48, -65.35, 0.93143, 8, -35.67, -63.02, 0.06857, 2, 40, 16.03, -60.72, 0.93143, 8, -8.16, -58.39, 0.06857, 2, 40, 32.62, -43.45, 0.93143, 8, 8.43, -41.12, 0.06857, 2, 40, 29.76, -14.26, 0.93143, 8, 5.57, -11.93, 0.06857, 2, 40, 14.68, -18.96, 0.93143, 8, -9.51, -16.63, 0.06857, 2, 40, -1.1, -7.47, 0.93143, 8, -25.29, -5.14, 0.06857, 2, 40, -22.64, -3.73, 0.89626, 8, -46.83, -1.4, 0.10374, 2, 40, -35.09, -11.44, 0.87786, 8, -59.28, -9.11, 0.12214, 2, 40, -39.24, -24.72, 0.86937, 8, -63.43, -22.39, 0.13063, 2, 40, -29.58, -35.38, 0.88853, 8, -53.77, -33.06, 0.11147, 2, 40, -8.68, -43.98, 0.92103, 8, -32.87, -41.65, 0.07897, 2, 40, 5.4, -42.82, 0.92702, 8, -18.79, -40.49, 0.07298, 2, 40, 16.24, -34.63, 0.92937, 8, -7.96, -32.3, 0.07063], "hull": 16}}, "monkey_mouth smile closed": {"monkey new/monkey_mouth smile closed": {"type": "mesh", "uvs": [0.45133, 0.16787, 0.57185, 0.25197, 0.67867, 0.23467, 0.81225, 0.07686, 0.9199, 0.19338, 0.98904, 0.38302, 1, 0.55662, 0.92828, 0.79203, 0.69032, 0.98884, 0.48894, 0.99997, 0.28255, 0.94477, 0.05911, 0.73363, 0.02885, 0.46098, 0.04139, 0.26862, 0.15322, 0.13745, 0.32418, 0.04839, 0.18112, 0.2031, 0.15813, 0.33095, 0.26498, 0.19861, 0.30691, 0.33992, 0.39888, 0.45207, 0.50709, 0.49917, 0.6207, 0.48347, 0.70591, 0.40721, 0.77624, 0.26141, 0.84522, 0.29282, 0.85469, 0.43188, 0.76542, 0.51711, 0.6491, 0.62926, 0.5125, 0.65842, 0.36236, 0.60459, 0.25145, 0.46553], "triangles": [13, 14, 16, 29, 28, 8, 9, 30, 29, 7, 28, 27, 10, 31, 30, 27, 26, 7, 31, 12, 17, 17, 13, 16, 9, 29, 8, 10, 30, 9, 7, 8, 28, 11, 31, 10, 7, 26, 6, 31, 11, 12, 26, 5, 6, 1, 21, 0, 22, 1, 2, 12, 13, 17, 0, 18, 15, 25, 4, 5, 26, 25, 5, 4, 25, 3, 22, 2, 23, 23, 2, 24, 18, 14, 15, 30, 21, 29, 30, 20, 21, 29, 22, 28, 29, 21, 22, 28, 23, 27, 28, 22, 23, 30, 31, 20, 26, 24, 25, 24, 26, 23, 26, 27, 23, 31, 17, 19, 31, 19, 20, 18, 17, 16, 17, 18, 19, 21, 20, 0, 20, 19, 0, 0, 19, 18, 16, 14, 18, 25, 24, 3, 22, 21, 1, 24, 2, 3], "vertices": [2, 40, 84.74, -10.36, 0.20857, 8, 60.55, -8.03, 0.79143, 2, 40, 63.15, -46.71, 0.31143, 8, 38.96, -44.38, 0.68857, 2, 40, 61.75, -81.39, 0.43429, 8, 37.56, -79.06, 0.56571, 2, 40, 86.3, -128.41, 0.47143, 8, 62.11, -126.08, 0.52857, 2, 40, 59.01, -159.77, 0.44286, 8, 34.82, -157.44, 0.55714, 2, 40, 19.31, -176.84, 0.50286, 8, -4.88, -174.51, 0.49714, 2, 40, -14.7, -175.7, 0.2, 8, -38.89, -173.37, 0.8, 2, 40, -56.99, -146.42, 0.13429, 8, -81.18, -144.1, 0.86571, 2, 40, -84.44, -64.93, 0.13429, 8, -108.63, -62.61, 0.86571, 2, 40, -77.65, -0.13, 0.13429, 8, -101.84, 2.2, 0.86571, 2, 40, -57.82, 64.5, 0.13429, 8, -82.02, 66.83, 0.86571, 2, 40, -7.12, 130.42, 0.13429, 8, -31.31, 132.75, 0.86571, 2, 40, 46.88, 132.81, 0.13429, 8, 22.69, 135.14, 0.86571, 2, 40, 83.48, 123.65, 0.13429, 8, 59.29, 125.98, 0.86571, 2, 40, 103.85, 84.32, 0.69143, 8, 79.66, 86.64, 0.30857, 2, 40, 113.46, 27.17, 0.37714, 8, 89.27, 29.5, 0.62286, 2, 40, 89.93, 77.13, 0.66857, 8, 65.74, 79.46, 0.33143, 2, 40, 66.26, 87.92, 0.66857, 8, 42.07, 90.25, 0.33143, 2, 40, 87.08, 50.15, 0.66857, 8, 62.89, 52.48, 0.33143, 2, 40, 57.92, 40.51, 0.66857, 8, 33.73, 42.83, 0.33143, 2, 40, 32.18, 14.05, 0.66857, 8, 7.99, 16.38, 0.33143, 2, 40, 18.28, -19.35, 0.66857, 8, -5.91, -17.02, 0.33143, 2, 40, 16.26, -56.16, 0.66857, 8, -7.93, -53.83, 0.33143, 2, 40, 27.21, -85.5, 0.66857, 8, 3.02, -83.17, 0.33143, 2, 40, 52.25, -111.93, 0.66857, 8, 28.06, -109.6, 0.33143, 2, 40, 43.12, -133.19, 0.66857, 8, 18.93, -130.86, 0.33143, 2, 40, 15.84, -132.5, 0.66857, 8, -8.35, -130.17, 0.33143, 2, 40, 3.34, -101.62, 0.66857, 8, -20.85, -99.29, 0.33143, 2, 40, -13.16, -61.36, 0.66857, 8, -37.35, -59.03, 0.33143, 2, 40, -12.72, -16.82, 0.66857, 8, -36.92, -14.49, 0.33143, 2, 40, 4.34, 29.83, 0.66857, 8, -19.85, 32.16, 0.33143, 2, 40, 36.12, 61.63, 0.66857, 8, 11.93, 63.96, 0.33143], "hull": 16}}, "monkey_mouth smile open": {"monkey new/monkey_mouth smile open": {"type": "mesh", "uvs": [0.96235, 0.00442, 0.99084, 0.09436, 1, 0.20085, 1, 0.34284, 0.94924, 0.61171, 0.8221, 0.84078, 0.56466, 1, 0.51491, 1, 0.31421, 0.9537, 0.14191, 0.78148, 0.04166, 0.63596, 0, 0.46341, 0, 0.24946, 0.03965, 0.12473, 0.08119, 0.06589, 0.1373, 0, 0.64033, 0.13674, 0.70327, 0.12685, 0.85746, 0.03799, 0.56413, 0.37564, 0.47853, 0.39925, 0.35475, 0.36276, 0.2494, 0.29407, 0.1717, 0.22109, 0.20067, 0.36061, 0.24545, 0.50228, 0.31129, 0.63966, 0.38767, 0.7384, 0.48512, 0.78777, 0.55228, 0.77489, 0.63129, 0.71264, 0.69845, 0.60961, 0.74059, 0.50228, 0.76035, 0.37993, 0.7024, 0.4014, 0.63129, 0.40569], "triangles": [7, 29, 6, 6, 30, 5, 7, 8, 28, 9, 26, 8, 5, 32, 4, 3, 4, 2, 9, 10, 25, 24, 10, 11, 4, 33, 2, 12, 24, 11, 33, 18, 2, 23, 12, 13, 13, 14, 23, 34, 16, 17, 33, 17, 18, 16, 21, 15, 14, 15, 23, 6, 29, 30, 8, 27, 28, 7, 28, 29, 8, 26, 27, 30, 31, 5, 31, 32, 5, 9, 25, 26, 32, 33, 4, 29, 28, 20, 28, 27, 20, 30, 19, 35, 30, 29, 19, 29, 20, 19, 27, 26, 20, 20, 26, 21, 30, 35, 31, 26, 25, 21, 31, 34, 32, 31, 35, 34, 25, 22, 21, 25, 24, 22, 32, 34, 33, 24, 23, 22, 10, 24, 25, 23, 24, 12, 34, 35, 16, 35, 19, 16, 34, 17, 33, 19, 20, 16, 16, 20, 21, 15, 21, 22, 22, 23, 15, 1, 18, 0, 2, 18, 1], "vertices": [2, 40, 66.9, -147.41, 0.54286, 8, 42.71, -145.08, 0.45714, 2, 40, 51.39, -152.88, 0.54286, 8, 27.2, -150.55, 0.45714, 2, 40, 33.91, -152.91, 0.54286, 8, 9.72, -150.58, 0.45714, 2, 40, 11.06, -149.74, 0.43429, 8, -13.13, -147.41, 0.56571, 2, 40, -30.37, -130.42, 0.27429, 8, -54.56, -128.1, 0.72571, 2, 40, -62.62, -91.96, 0.13429, 8, -86.81, -89.63, 0.86571, 2, 40, -78.89, -20.86, 0.13429, 8, -103.08, -18.53, 0.86571, 2, 40, -77.08, -7.81, 0.13429, 8, -101.27, -5.48, 0.86571, 2, 40, -62.33, 43.82, 0.13429, 8, -86.52, 46.15, 0.86571, 2, 40, -28.34, 85.18, 0.13714, 8, -52.53, 87.51, 0.86286, 2, 40, -1.27, 108.24, 0.32286, 8, -25.46, 110.56, 0.67714, 2, 40, 28.01, 115.32, 0.33429, 8, 3.82, 117.64, 0.66571, 2, 40, 62.45, 110.54, 0.54286, 8, 38.26, 112.87, 0.45714, 2, 40, 81.09, 97.36, 0.54286, 8, 56.9, 99.69, 0.45714, 2, 40, 89.05, 85.15, 0.88286, 8, 64.86, 87.48, 0.11714, 1, 40, 97.61, 68.96, 1, 2, 40, 57.31, -59.97, 0.98571, 8, 33.12, -57.64, 0.01429, 2, 40, 56.62, -76.71, 0.94286, 8, 32.42, -74.38, 0.05714, 2, 40, 65.31, -119.14, 0.74, 8, 41.12, -116.82, 0.26, 2, 40, 21.63, -34.65, 0.95143, 8, -2.56, -32.32, 0.04857, 2, 40, 20.94, -11.67, 0.95143, 8, -3.25, -9.34, 0.04857, 2, 40, 31.32, 20, 0.95143, 8, 7.13, 22.33, 0.04857, 2, 40, 46.2, 46.1, 0.95143, 8, 22.01, 48.43, 0.04857, 2, 40, 60.78, 64.86, 0.95143, 8, 36.58, 67.19, 0.04857, 2, 40, 37.26, 60.37, 0.95143, 8, 13.07, 62.7, 0.04857, 2, 40, 12.83, 51.79, 0.95143, 8, -11.36, 54.11, 0.04857, 2, 40, -11.67, 37.58, 0.95143, 8, -35.86, 39.9, 0.04857, 2, 40, -30.34, 19.74, 0.95143, 8, -54.54, 22.07, 0.04857, 2, 40, -41.83, -4.73, 0.95143, 8, -66.03, -2.4, 0.04857, 2, 40, -42.2, -22.64, 0.95143, 8, -66.39, -20.31, 0.04857, 2, 40, -35.06, -44.75, 0.95143, 8, -59.25, -42.43, 0.04857, 2, 40, -20.92, -64.67, 0.95143, 8, -45.11, -62.34, 0.04857, 2, 40, -5.17, -78.12, 0.95143, 8, -29.36, -75.8, 0.04857, 2, 40, 13.8, -86.04, 0.95143, 8, -10.39, -83.71, 0.04857, 2, 40, 12.46, -70.35, 0.95143, 8, -11.74, -68.03, 0.04857, 2, 40, 14.35, -51.6, 0.95143, 8, -9.84, -49.27, 0.04857], "hull": 19}}, "monkey_nose": {"monkey new/monkey_nose": {"type": "mesh", "uvs": [0.83927, 0.19199, 0.99554, 0.45311, 0.99533, 0.64282, 0.92893, 0.80514, 0.71244, 0.96523, 0.18616, 0.96539, 0, 0.58819, 0, 0.3614, 0.02896, 0.26048, 0.23351, 0.035, 0.60301, 0.03453], "triangles": [6, 7, 8, 1, 3, 0, 2, 3, 1, 4, 10, 0, 4, 0, 3, 9, 6, 8, 5, 6, 9, 5, 9, 10, 4, 5, 10], "vertices": [1, 15, -1.49, -80, 1, 1, 15, -19.16, -93.19, 1, 1, 15, -30.46, -91.6, 1, 1, 15, -39.22, -83.74, 1, 1, 15, -45.81, -61.17, 1, 1, 15, -38.66, -9.49, 1, 1, 15, -13.66, 5.67, 1, 1, 15, -0.15, 3.8, 1, 1, 15, 5.46, 0.13, 1, 1, 15, 16.11, -21.82, 1, 1, 15, 11.11, -58.1, 1], "hull": 11}}, "monkey_right_arm": {"monkey new/monkey_right_arm": {"type": "mesh", "uvs": [0.40163, 0.00962, 0.50477, 0.06997, 0.60792, 0.13032, 0.68857, 0.19723, 0.7737, 0.28972, 0.88685, 0.46322, 1, 0.63673, 1, 0.72528, 0.92707, 0.87463, 0.88347, 0.94773, 0.68371, 0.99394, 0.62513, 0.98486, 0.47318, 0.85328, 0.43249, 0.76457, 0.39179, 0.67586, 0.35786, 0.6019, 0.32555, 0.56332, 0.29324, 0.52474, 0.20569, 0.46816, 0.04626, 0.36846, 1e-05, 0.27259, 0.06247, 0.11081, 0.19498, 0.00865], "triangles": [9, 10, 8, 8, 10, 11, 5, 8, 11, 7, 8, 5, 12, 5, 11, 12, 13, 5, 13, 14, 5, 5, 6, 7, 14, 4, 5, 4, 15, 3, 4, 14, 15, 3, 16, 2, 3, 15, 16, 2, 17, 1, 2, 16, 17, 17, 18, 1, 19, 21, 18, 21, 22, 18, 18, 0, 1, 18, 22, 0, 19, 20, 21], "vertices": [2, 22, 69.64, 58.9, 0.76286, 23, -42.33, 44.99, 0.23714, 2, 22, 98.97, 55.92, 0.42522, 23, -15.19, 54.69, 0.57478, 2, 22, 134.57, 51.33, 0.12463, 23, 12.42, 61.77, 0.87537, 1, 23, 36.7, 65.16, 1, 2, 23, 65.5, 65.21, 0.91197, 24, -32.35, 59.17, 0.08803, 2, 23, 99.86, 69.67, 0.43595, 24, 25.89, 66.2, 0.56405, 2, 23, 156.8, 49.04, 0.01136, 24, 63.63, 66.24, 0.98864, 2, 23, 169.69, 34.76, 0.00078, 24, 80.06, 55.6, 0.99922, 1, 24, 97.59, 21.94, 1, 1, 24, 105.06, 3.76, 1, 1, 24, 85.74, -44.83, 1, 2, 23, 139.91, -72.93, 0.0004, 24, 75.87, -56.37, 0.9996, 2, 23, 93.37, -78.41, 0.11443, 24, 30.25, -73.3, 0.88557, 3, 22, 163.97, -95.59, 0.01013, 23, 95.22, -67.74, 0.32776, 24, -2.21, -68.39, 0.66211, 3, 22, 120.98, -80.82, 0.02025, 23, 52.87, -64.12, 0.5411, 24, -14.03, -69.52, 0.43865, 3, 22, 105.92, -68.3, 0.09579, 23, 35.99, -58.16, 0.68877, 24, -32.49, -67.94, 0.21545, 3, 22, 94.15, -63.01, 0.20716, 23, 24.55, -57.62, 0.67936, 24, -44.16, -70.27, 0.11348, 2, 22, 82.39, -57.72, 0.39982, 23, 13.12, -57.08, 0.60018, 2, 22, 54.4, -53.39, 0.76559, 23, -10.9, -63.34, 0.23441, 2, 22, 3.71, -46.2, 0.99116, 23, -54.15, -75.28, 0.00884, 1, 22, -16.42, -30.16, 1, 1, 22, -12.83, 8.89, 1, 2, 22, 14.32, 41.49, 0.81143, 23, -79.72, 8.84, 0.18857], "hull": 23}}, "monkey_right_arm2": {"monkey new/monkey_right_arm2": {"type": "mesh", "uvs": [0.25712, 0, 0.38827, 0.05876, 0.49848, 0.17739, 0.55447, 0.25732, 0.58761, 0.30462, 0.61802, 0.31937, 0.87094, 0.39337, 0.95616, 0.45992, 0.99999, 0.63242, 0.97661, 0.75664, 0.88667, 0.92004, 0.72243, 0.9894, 0.60535, 1, 0.40479, 0.93757, 0.3154, 0.87333, 0.23592, 0.77789, 0.1811, 0.56889, 0.11033, 0.44252, 0.01751, 0.32929, 0, 0.23102, 0.03256, 0.12473, 0.138, 0.02546], "triangles": [18, 19, 20, 17, 20, 21, 18, 20, 17, 0, 17, 21, 1, 17, 0, 2, 17, 1, 16, 2, 3, 17, 2, 16, 8, 6, 7, 4, 15, 16, 4, 16, 3, 14, 15, 4, 9, 10, 6, 8, 9, 6, 13, 14, 4, 13, 4, 5, 11, 5, 6, 11, 6, 10, 12, 13, 5, 11, 12, 5], "vertices": [148.11, 15.86, 180.5, 4.21, 207.72, -19.31, 221.55, -35.16, 229.74, -44.53, 237.25, -47.46, 299.72, -62.13, 320.77, -75.32, 331.6, -109.52, 325.82, -134.15, 303.61, -166.54, 263.04, -180.29, 234.12, -182.39, 184.58, -170.02, 162.5, -157.28, 142.87, -138.36, 129.33, -96.93, 111.85, -71.87, 88.92, -49.42, 84.6, -29.94, 92.64, -8.87, 118.69, 10.81], "hull": 22}}, "monkey_right_foot": {"monkey new/monkey_right_foot": {"type": "mesh", "uvs": [0.80985, 0.13118, 1, 0.38127, 1, 0.49081, 0.57751, 0.87737, 0.38174, 0.98124, 0.15156, 0.98102, 0, 0.85107, 0, 0.75902, 0.03896, 0.52514, 0.27979, 0.07951, 0.34677, 0.02182, 0.48201, 0.00648], "triangles": [3, 4, 7, 7, 4, 5, 3, 7, 8, 3, 8, 0, 7, 5, 6, 0, 8, 9, 11, 0, 9, 2, 0, 1, 2, 3, 0, 11, 9, 10], "vertices": [1, 35, 98.08, -11.95, 1, 1, 35, 70.45, -62.83, 1, 2, 35, 51.8, -72.5, 0.99775, 32, 139.12, -68.69, 0.00225, 2, 35, -47.19, -42.64, 0.07973, 32, 38.51, -44.86, 0.92027, 1, 32, 4.27, -26.41, 1, 1, 32, -15.84, 7.32, 1, 2, 35, -88.08, 47.15, 5e-05, 32, -7.73, 42.3, 0.99995, 2, 35, -72.41, 55.28, 0.00704, 32, 7.42, 51.36, 0.99296, 2, 35, -29.54, 70.03, 0.19705, 32, 49.32, 68.67, 0.80295, 2, 35, 65.23, 72.89, 0.97871, 32, 143.75, 77.25, 0.02129, 2, 35, 80.31, 67.84, 0.99422, 32, 159.11, 73.12, 0.00578, 2, 35, 93.55, 48.71, 0.99997, 32, 173.48, 54.82, 3e-05], "hull": 12}}, "monkey_right_hand": {"monkey new/monkey_right_hand": {"type": "mesh", "uvs": [0.82112, 0.11606, 0.96336, 0.21164, 0.99162, 0.70501, 0.93412, 0.80135, 0.7737, 0.80123, 0.58234, 1, 0.47516, 1, 0.01828, 0.64339, 0.0074, 0.39825, 0.09352, 0.17492, 0.29643, 0.05986, 0.52024, 0.03995], "triangles": [4, 7, 8, 4, 11, 0, 4, 0, 1, 4, 1, 2, 3, 4, 2, 9, 10, 8, 11, 8, 10, 4, 8, 11, 6, 7, 4, 5, 6, 4], "vertices": [44.75, 63.32, 69.95, 69.98, 132.33, 23.36, 138.48, 7.03, 123.09, -11.36, 128.79, -53.41, 118.52, -65.7, 31.59, -82.08, 0.89, -58.56, -17.87, -26.11, -12.34, 8.8, 6.7, 36.49], "hull": 12}}, "monkey_right_leg": {"monkey new/monkey_right_leg": {"type": "mesh", "uvs": [0.92187, 0.14792, 1, 0.33907, 1, 0.39792, 0.95553, 0.64304, 0.70424, 0.93969, 0.40361, 1, 0.31635, 1, 0.09458, 0.88606, 0, 0.67797, 0, 0.56476, 0.04351, 0.35218, 0.241, 0.10846, 0.47836, 0.00897, 0.73847, 0.00964], "triangles": [9, 0, 1, 9, 1, 2, 7, 8, 9, 10, 0, 9, 7, 9, 2, 0, 11, 12, 0, 12, 13, 11, 0, 10, 3, 7, 2, 4, 7, 3, 5, 6, 7, 4, 5, 7], "vertices": [1, 31, 92.13, 135.14, 1, 1, 31, 112.86, 95.9, 1, 1, 31, 115.13, 83.1, 1, 1, 31, 116.95, 28.41, 1, 1, 31, 85.34, -43.76, 1, 1, 31, 36.19, -65.99, 1, 1, 31, 21.25, -68.63, 1, 1, 31, -21.1, -50.56, 1, 1, 31, -45.31, -8.14, 1, 1, 31, -49.67, 16.5, 1, 1, 31, -50.4, 64.08, 1, 1, 31, -25.97, 123.1, 1, 1, 31, 10.84, 151.94, 1, 1, 31, 55.4, 159.67, 1], "hull": 14}}, "R_hand_spline": {"R_hand_spline": {"type": "path", "lengths": [268.04, 554.06], "vertexCount": 6, "vertices": [1, 37, 75.39, 14.09, 1, 1, 37, 0, 1.32, 1, 1, 37, -80.13, -12.26, 1, 1, 38, -5.47, 35.21, 1, 1, 38, -43.84, -45.53, 1, 1, 37, -235.83, -276.37, 1]}}, "shadow": {"monkey new/shadow": {"type": "mesh", "uvs": [0.21753, 0.47052, 0.41044, 0.40419, 0.6053, 0.39357, 0.76313, 0.42011, 0.88312, 0.52508, 0.90101, 0.64422, 0.85252, 0.75821, 0.79505, 0.8258, 0.55133, 0.86548, 0.28153, 0.85964, 0.18392, 0.80855, 0.147, 0.76186, 0.11627, 0.67179, 0.14362, 0.55358], "triangles": [6, 3, 4, 6, 4, 5, 10, 11, 13, 12, 13, 11, 0, 10, 13, 7, 3, 6, 9, 0, 1, 10, 0, 9, 8, 1, 2, 9, 1, 8, 7, 8, 2, 7, 2, 3], "vertices": [-188.09, -289.34, -52.36, -272.2, 84.75, -269.46, 195.81, -276.31, 280.23, -303.44, 292.82, -334.22, 258.71, -363.67, 218.26, -381.13, 46.78, -391.39, -143.06, -389.88, -211.74, -376.68, -237.72, -364.61, -259.34, -341.34, -240.1, -310.8], "hull": 14}}, "tail": {"monkey new/monkey_tail": {"type": "mesh", "uvs": [0.59757, 0.03093, 0.68202, 0.07822, 0.74994, 0.16997, 0.7505, 0.31524, 0.70175, 0.41018, 0.59787, 0.47283, 0.43959, 0.47335, 0.40061, 0.37107, 0.5308, 0.28466, 0.50937, 0.20587, 0.42393, 0.18868, 0.3385, 0.20806, 0.26645, 0.27788, 0.21469, 0.34769, 0.22227, 0.48733, 0.25686, 0.59025, 0.31824, 0.69318, 0.4888, 0.77473, 0.63741, 0.79106, 0.73549, 0.7909, 0.85735, 0.76178, 0.91457, 0.81137, 0.98819, 0.89486, 0.98806, 0.95594, 0.94751, 0.9746, 0.88537, 0.98371, 0.58602, 0.98361, 0.38168, 0.94455, 0.22787, 0.86217, 0.07061, 0.71516, 0.01209, 0.53531, 0.01197, 0.35039, 0.05047, 0.2206, 0.12495, 0.11285, 0.28993, 0.01092, 0.40598, 0.00606, 0.52203, 0.01093, 0.55047, 0.38823, 0.66791, 0.27826, 0.59422, 0.1185, 0.41232, 0.08322, 0.23041, 0.14755, 0.12449, 0.30524, 0.11528, 0.48368, 0.16363, 0.67457, 0.28107, 0.80114, 0.46528, 0.86546, 0.6633, 0.89658, 0.82679, 0.87583], "triangles": [7, 8, 37, 37, 8, 38, 6, 7, 37, 6, 37, 5, 8, 9, 38, 38, 2, 3, 4, 38, 3, 37, 38, 4, 5, 37, 4, 39, 0, 1, 2, 38, 39, 2, 39, 1, 9, 39, 38, 39, 36, 0, 40, 36, 39, 39, 9, 40, 40, 35, 36, 9, 10, 40, 40, 34, 35, 11, 40, 10, 11, 41, 40, 34, 40, 41, 41, 33, 34, 12, 41, 11, 41, 32, 33, 42, 41, 12, 13, 42, 12, 31, 32, 42, 43, 31, 42, 43, 42, 13, 41, 42, 32, 15, 43, 14, 43, 13, 14, 30, 31, 43, 15, 44, 43, 30, 43, 44, 29, 30, 44, 44, 15, 16, 29, 44, 28, 45, 44, 16, 45, 16, 17, 28, 44, 45, 28, 45, 27, 27, 45, 46, 46, 45, 17, 18, 46, 17, 27, 46, 26, 46, 18, 47, 26, 46, 47, 48, 19, 20, 47, 18, 19, 47, 19, 48, 26, 47, 25, 48, 20, 21, 48, 21, 22, 22, 24, 48, 23, 24, 22, 25, 48, 24, 47, 48, 25], "vertices": [2, 57, 21.13, 17.74, 0.47128, 58, -20.31, -0.77, 0.52872, 2, 57, 36.86, 13.45, 0.13263, 58, -8.41, 10.39, 0.86737, 2, 57, 52.32, 0.64, 0.15329, 58, 10.6, 16.83, 0.84671, 2, 58, 36.16, 10.09, 0.07541, 59, -0.62, 15.1, 0.92459, 1, 59, 18.35, 16.7, 1, 2, 59, 36.69, 7.53, 0.4985, 60, 13.59, 17.46, 0.5015, 2, 59, 49.61, -14.98, 0.00278, 60, 35.4, 3.38, 0.99722, 2, 58, 31.15, -47.97, 0, 60, 30.6, -15.71, 1, 4, 56, 33.19, -37.56, 0.00039, 57, 23.85, -29.64, 0.10558, 58, 21.47, -23.28, 0.17537, 60, 4.13, -17.25, 0.71866, 4, 56, 32.98, -22.79, 0.06227, 57, 16.38, -16.91, 0.64276, 58, 6.71, -22.97, 0.1428, 60, -0.75, -31.18, 0.15217, 5, 55, 36.25, -24.45, 0.0358, 56, 20.03, -16.6, 0.57882, 57, 2.06, -17.92, 0.37154, 58, 0.07, -35.7, 0.0013, 60, 9.29, -41.45, 0.01254, 5, 54, 43.9, -27.22, 0.02309, 55, 23.64, -17.4, 0.45032, 56, 5.59, -16.89, 0.50665, 57, -10.36, -25.31, 0.01983, 60, 22.96, -46.13, 0.00012, 3, 54, 28.57, -19.08, 0.43993, 55, 6.32, -18.52, 0.53249, 56, -8.78, -26.62, 0.02758, 3, 53, 40.1, -18.14, 0.08965, 54, 14.1, -14.16, 0.86002, 55, -8.57, -21.94, 0.05034, 3, 52, 40.01, -20.77, 0.00808, 53, 14.68, -17.14, 0.95997, 54, -10.13, -21.93, 0.03195, 3, 51, 45.4, -27.43, 0.08745, 52, 20.5, -19.3, 0.54352, 53, -4.48, -21.13, 0.36904, 4, 50, 55.57, -24.14, 0.00792, 51, 27.24, -16.36, 0.78741, 52, -0.61, -21.93, 0.19678, 53, -24.03, -29.51, 0.00788, 2, 50, 25.49, -14.25, 0.97507, 51, -4.39, -17.7, 0.02493, 2, 49, 24.47, -14.9, 0.16916, 50, 0.96, -15.45, 0.83084, 2, 49, 8.8, -11.29, 0.92053, 50, -14.89, -18.2, 0.07947, 2, 47, 6.51, 16.86, 0.11984, 49, -11.87, -11.93, 0.88016, 2, 47, 16.55, 8.56, 0.66116, 49, -18.97, -1.02, 0.33884, 2, 47, 29.72, -5.69, 0.99838, 49, -27.3, 16.51, 0.00162, 1, 47, 30.52, -16.78, 1, 1, 47, 24.14, -20.66, 1, 2, 47, 14.11, -23.08, 0.99746, 49, -7.22, 28.45, 0.00254, 4, 47, -34.85, -26.72, 0.02749, 49, 40.6, 17.34, 0.11593, 50, 3.33, 20.52, 0.7905, 51, -37.38, 7.02, 0.06609, 2, 50, 37.56, 19.19, 0.00628, 51, -4.88, 17.84, 0.99372, 1, 51, 24.47, 17.67, 1, 2, 51, 60.25, 7.64, 0.00023, 52, 10.31, 17.39, 0.99977, 2, 52, 44.3, 14.53, 0.69048, 53, 9.02, 17.97, 0.30952, 3, 52, 75.7, 2.41, 0.00254, 53, 42.54, 15.02, 0.16913, 54, 5.04, 17.83, 0.82833, 2, 54, 29.49, 17.83, 0.81925, 55, -12.28, 13.37, 0.18075, 2, 54, 51.59, 11.09, 0.01722, 55, 10.07, 19.25, 0.98278, 2, 55, 42.48, 14.16, 0.11992, 56, 5.88, 19.86, 0.88008, 3, 56, 24.63, 16.45, 0.80678, 57, -10.27, 13.09, 0.19047, 58, -32.8, -29.95, 0.00275, 3, 56, 42.97, 11.31, 0.04341, 57, 8.22, 17.68, 0.81279, 58, -27.03, -11.8, 0.1438, 2, 59, 27.15, -6.84, 0.00547, 60, 11.7, 0.31, 0.99453, 3, 57, 45.06, -22.09, 0, 59, 0.23, 0, 0.99982, 60, -15.35, -5.96, 0.00018, 2, 57, 25.17, 2.31, 0.84287, 58, -5.05, -5.42, 0.15713, 2, 56, 22.49, 2.53, 0.99988, 58, -18.97, -32.58, 0.00012, 1, 55, 18.31, 2.78, 1, 1, 54, 17.75, 2.12, 1, 2, 52, 46.96, -4.64, 0.00247, 53, 16.88, 0.29, 0.99753, 1, 52, 11.7, 0.49, 1, 1, 51, 22.56, 3.68, 1, 2, 50, 26.49, 2.68, 0.85562, 51, -9.42, -1.51, 0.14438, 3, 47, -23.39, -9.98, 0.04466, 49, 24.68, 4.77, 0.95449, 51, -40.3, -13.05, 0.00085, 1, 47, 3.06, -4.21, 1], "hull": 37}}, "under_eye1": {"monkey new/under_eye1": {"type": "mesh", "uvs": [0.77651, 0.05967, 0.99744, 0.28884, 0.99714, 0.66888, 0.86692, 0.87315, 0.66343, 0.99773, 0.27965, 0.99628, 0.00362, 0.71286, 0.00214, 0.28743, 0.28137, 0.00308, 0.61736, 0.0033], "triangles": [3, 4, 9, 2, 3, 9, 6, 9, 4, 8, 9, 7, 6, 4, 5, 2, 9, 0, 9, 6, 7, 2, 0, 1], "vertices": [2, 8, 204.14, 33.56, 0.26, 15, 122.3, 32.22, 0.74, 2, 8, 168.08, 6.66, 0.26, 15, 86.24, 5.32, 0.74, 2, 8, 115.48, 13.99, 0.26, 15, 33.63, 12.66, 0.74, 2, 8, 89.76, 36.36, 0.26, 15, 7.91, 35.02, 0.74, 2, 8, 76.51, 67.57, 0.26, 15, -5.34, 66.23, 0.74, 2, 8, 84.24, 121.91, 0.26, 15, 2.4, 120.57, 0.74, 2, 8, 128.9, 155.57, 0.26, 15, 47.05, 154.23, 0.74, 2, 8, 187.82, 147.61, 0.26, 15, 105.97, 146.28, 0.74, 2, 8, 221.7, 102.61, 0.26, 15, 139.85, 101.27, 0.74, 2, 8, 215.07, 55.02, 0.26, 15, 133.22, 53.68, 0.74], "hull": 10}}, "under_eye2": {"monkey new/under_eye2": {"type": "mesh", "uvs": [0.74565, 0.00256, 0.99625, 0.301, 0.99712, 0.66872, 0.88, 0.88853, 0.66583, 0.99659, 0.30095, 0.99661, 0.06305, 0.79625, 0, 0.56253, 0, 0.3748, 0.09639, 0.1457, 0.3364, 0.00303], "triangles": [2, 7, 8, 2, 6, 7, 2, 10, 1, 1, 10, 0, 9, 10, 8, 3, 4, 2, 2, 8, 10, 4, 6, 2, 5, 6, 4], "vertices": [2, 8, 176.91, -197.4, 0.28857, 15, 95.07, -198.74, 0.71143, 2, 8, 131.78, -222.8, 0.28857, 15, 49.93, -224.14, 0.71143, 2, 8, 81.45, -215.94, 0.28857, 15, -0.4, -217.27, 0.71143, 2, 8, 53.39, -197.25, 0.28857, 15, -28.46, -198.59, 0.71143, 2, 8, 42.28, -168.66, 0.28857, 15, -39.56, -170, 0.71143, 2, 8, 48.55, -123.43, 0.28857, 15, -33.3, -124.77, 0.71143, 2, 8, 80.05, -97.75, 0.28857, 15, -1.8, -99.09, 0.71143, 2, 8, 113.11, -94.37, 0.28857, 15, 31.26, -95.7, 0.71143, 2, 8, 138.79, -97.93, 0.28857, 15, 56.95, -99.26, 0.71143, 2, 8, 168.48, -114.22, 0.28857, 15, 86.64, -115.55, 0.71143, 2, 8, 183.88, -146.67, 0.28857, 15, 102.03, -148.01, 0.71143], "hull": 11}}}}, {"name": "1_banana", "attachments": {"monkeyOLD_banana_1": {"banana_1": {"name": "monkey_banana", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [-359.27, 17.01, -290.02, 272.3, 393.8, 86.79, 324.54, -168.5], "hull": 4}}}}, {"name": "2_banana", "attachments": {"banana_2_1": {"banana_2_1": {"name": "monkey_banana", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [-344.47, 85.58, -260.16, 314.22, 352.26, 88.38, 267.95, -140.26], "hull": 4}}, "banana_2_2": {"banana_2_2": {"name": "monkey_banana", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [-190.13, -117.31, -196.03, 84.95, 345.75, 100.74, 351.64, -101.53], "hull": 4}}}}, {"name": "3_banana", "attachments": {"banana_3_2": {"banana_3_2": {"name": "monkey_banana", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [-258.84, -50.19, -239.43, 152.84, 304.4, 100.86, 285, -102.17], "hull": 4}}, "banana_3_3": {"banana_3_3": {"name": "monkey_banana", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [-82.42, -163.41, -123.4, -9.53, 288.77, 100.23, 329.74, -53.64], "hull": 4}}, "monkeyOLD_banana_3_1": {"banana_3_1": {"name": "monkey_banana", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [-322.16, 68.6, -257.16, 276.99, 301.03, 102.9, 236.04, -105.49], "hull": 4}}}}], "animations": {"Appear": {"slots": {"banana_2_1": {"rgba": [{"color": "ffffff00", "curve": [0.025, 1, 0.047, 1, 0.025, 1, 0.047, 1, 0.025, 1, 0.047, 1, 0.025, 0, 0.047, 1]}, {"time": 0.1, "color": "ffffffff"}]}, "banana_2_2": {"rgba": [{"color": "ffffff00", "curve": [0.025, 1, 0.047, 1, 0.025, 1, 0.047, 1, 0.025, 1, 0.047, 1, 0.025, 0, 0.047, 1]}, {"time": 0.1, "color": "ffffffff"}]}, "banana_3_2": {"rgba": [{"color": "ffffff00", "curve": [0.025, 1, 0.047, 1, 0.025, 1, 0.047, 1, 0.025, 1, 0.047, 1, 0.025, 0, 0.047, 1]}, {"time": 0.1, "color": "ffffffff"}]}, "banana_3_3": {"rgba": [{"color": "ffffff00", "curve": [0.025, 1, 0.047, 1, 0.025, 1, 0.047, 1, 0.025, 1, 0.047, 1, 0.025, 0, 0.047, 1]}, {"time": 0.1, "color": "ffffffff"}]}, "closed_eye_left": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}], "attachment": [{"name": "monkey new/closed_eye_left"}, {"time": 0.3333}]}, "closed_eye_right": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}], "attachment": [{"name": "monkey new/closed_eye_right"}, {"time": 0.3333}]}, "head": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "L_hand_spline": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkeyOLD_banana_1": {"rgba": [{"color": "ffffff00", "curve": [0.025, 1, 0.047, 1, 0.025, 1, 0.047, 1, 0.025, 1, 0.047, 1, 0.025, 0, 0.047, 1]}, {"time": 0.1, "color": "ffffffff"}]}, "monkeyOLD_banana_3_1": {"rgba": [{"color": "ffffff00", "curve": [0.025, 1, 0.047, 1, 0.025, 1, 0.047, 1, 0.025, 1, 0.047, 1, 0.025, 0, 0.047, 1]}, {"time": 0.1, "color": "ffffffff"}]}, "monkey_body": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_ear left": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_ear right": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_eyebrow left": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_eyebrow right": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_eyebrow_angry": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_eye_left_iris": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_eye_rioht_iris": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_hand left": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_hand right": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_left_arm": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}], "attachment": [{}]}, "monkey_left_foot": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_left_hand": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}], "attachment": [{}]}, "monkey_left_hand closed": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}], "attachment": [{}]}, "monkey_left_leg": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_mouth angry": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}], "attachment": [{}]}, "monkey_mouth o": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}], "attachment": [{}]}, "monkey_mouth smile closed": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}], "attachment": [{"time": 0.2}, {"time": 0.8, "name": "monkey new/monkey_mouth smile closed"}]}, "monkey_mouth smile open": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}], "attachment": [{}, {"time": 0.2, "name": "monkey new/monkey_mouth smile open"}, {"time": 0.8}]}, "monkey_nose": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_right_arm": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_right_arm2": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_right_foot": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "monkey_right_hand": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}], "attachment": [{}]}, "monkey_right_leg": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "R_hand_spline": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "shadow": {"alpha": [{"value": 0, "curve": "stepped"}, {"time": 0.1, "value": 0, "curve": "stepped"}, {"time": 0.2, "value": 0, "curve": [0.244, 0, 0.289, 1]}, {"time": 0.3333, "value": 1}]}, "tail": {"alpha": [{"value": 0, "curve": "stepped"}, {"time": 0.2, "value": 0, "curve": [0.233, 0, 0.267, 1]}, {"time": 0.3, "value": 1}]}, "under_eye1": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}, "under_eye2": {"alpha": [{"value": 0, "curve": [0.033, 0, 0.067, 1]}, {"time": 0.1, "value": 1}]}}, "bones": {"Banana": {"rotate": [{"value": 0.03, "curve": "stepped"}, {"time": 0.1667, "value": 0.03, "curve": "stepped"}, {"time": 0.4, "value": 0.03, "curve": "stepped"}, {"time": 0.6667, "value": 0.03, "curve": "stepped"}, {"time": 0.8667, "value": 0.03}], "translate": [{"x": 6.4, "y": -7.09, "curve": [0.042, 6.4, 0.125, -53.1, 0.042, -7.09, 0.125, 6.41]}, {"time": 0.1667, "x": -53.1, "y": 6.41, "curve": [0.2, -53.1, 0.267, 33.72, 0.2, 6.41, 0.267, -9.02]}, {"time": 0.3, "x": 33.72, "y": -9.02, "curve": [0.35, 33.72, 0.45, -24.72, 0.35, -9.02, 0.45, -1.85]}, {"time": 0.5, "x": -24.72, "y": -1.85, "curve": [0.542, -24.72, 0.625, 6.4, 0.542, -1.85, 0.625, -7.09]}, {"time": 0.6667, "x": 6.4, "y": -7.09}], "scale": [{"x": 0.168, "y": 0.168, "curve": [0.042, 0.168, 0.125, 1, 0.042, 0.168, 0.125, 1]}, {"time": 0.1667}]}, "root": {"translate": [{}]}, "main_C": {"rotate": [{}], "translate": [{"y": -538.84, "curve": [0.037, 0, 0.112, 0, 0.037, -328.66, 0.112, 147.39]}, {"time": 0.2, "y": 147.39, "curve": [0.271, 0, 0.33, 0, 0.271, 147.39, 0.33, -31.09]}, {"time": 0.4, "y": -31.09, "curve": [0.421, 0, 0.481, 0, 0.421, -31.09, 0.481, 37.2]}, {"time": 0.5667, "y": 37.2, "curve": [0.646, 0, 0.721, 0, 0.646, 37.2, 0.721, -0.19]}, {"time": 0.7333}], "scale": [{"x": 0.836, "curve": [0.017, 0.836, 0.045, 0.797, 0.017, 1, 0.045, 1]}, {"time": 0.0667, "x": 0.797, "curve": [0.117, 0.797, 0.217, 1.099, 0.117, 1, 0.217, 0.967]}, {"time": 0.2667, "x": 1.099, "y": 0.967, "curve": [0.308, 1.099, 0.392, 1, 0.308, 0.967, 0.392, 1]}, {"time": 0.4333}]}, "R_leg2": {"rotate": [{"value": 5.75, "curve": [0.033, 5.75, 0.1, 44.33]}, {"time": 0.1333, "value": 44.33, "curve": [0.183, 44.33, 0.283, -24.95]}, {"time": 0.3333, "value": -24.95, "curve": [0.375, -24.95, 0.458, 26.64]}, {"time": 0.5, "value": 26.64, "curve": [0.542, 26.64, 0.625, 5.75]}, {"time": 0.6667, "value": 5.75}], "translate": [{"x": -0.6, "y": 0.6}]}, "L_fingeL_6": {"rotate": [{}], "translate": [{}]}, "mouth": {"rotate": [{}], "translate": [{}]}, "SPINE": {"rotate": [{"curve": [0.036, 0, 0.059, 4.43]}, {"time": 0.1, "value": 4.43, "curve": [0.172, 4.43, 0.217, 0]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7}], "translate": [{}], "scale": [{"x": 0.653, "y": 0.972, "curve": [0.042, 0.653, 0.125, 1, 0.042, 0.972, 0.125, 1]}, {"time": 0.1667}]}, "L_fingeL_5": {"rotate": [{}], "translate": [{}]}, "R_finger_3": {"rotate": [{}], "translate": [{}]}, "R_foot_finger_5": {"rotate": [{"value": 9.43, "curve": [0.05, 9.43, 0.15, 0.52]}, {"time": 0.2, "value": 0.52, "curve": [0.25, 0.52, 0.35, 21.91]}, {"time": 0.4, "value": 21.91, "curve": [0.442, 21.91, 0.525, 9.43]}, {"time": 0.5667, "value": 9.43, "curve": "stepped"}, {"time": 0.7333, "value": 9.43}], "translate": [{}]}, "L_hand_5": {"rotate": [{"value": -62.17}], "translate": [{"x": -0.79, "y": -11.59}]}, "R_hand_1": {"rotate": [{"value": 7.97, "curve": [0.033, 7.97, 0.1, -0.05]}, {"time": 0.1333, "value": -0.05, "curve": [0.192, -0.05, 0.308, 28.67]}, {"time": 0.3667, "value": 28.67, "curve": [0.408, 28.67, 0.492, 0.45]}, {"time": 0.5333, "value": 0.45, "curve": [0.575, 0.45, 0.658, 7.97]}, {"time": 0.7, "value": 7.97}], "translate": [{"x": 3.53, "y": 1.05, "curve": [0.033, 3.53, 0.1, -49.39, 0.033, 1.05, 0.1, -12.32]}, {"time": 0.1333, "x": -49.39, "y": -12.32, "curve": [0.192, -49.39, 0.308, 3.53, 0.192, -12.32, 0.308, 1.05]}, {"time": 0.3667, "x": 3.53, "y": 1.05, "curve": [0.408, 3.53, 0.492, -23.2, 0.408, 1.05, 0.492, -8.02]}, {"time": 0.5333, "x": -23.2, "y": -8.02, "curve": [0.575, -23.2, 0.658, 3.53, 0.575, -8.02, 0.658, 1.05]}, {"time": 0.7, "x": 3.53, "y": 1.05}]}, "L_hand_6": {"rotate": [{"value": -55.93}], "translate": [{"x": -31.68, "y": -25}]}, "R_foot2": {"rotate": [{"value": -2.29, "curve": [0.042, -2.29, 0.125, -35.36]}, {"time": 0.1667, "value": -35.36, "curve": [0.217, -35.36, 0.317, 12.15]}, {"time": 0.3667, "value": 12.15, "curve": [0.408, 12.15, 0.492, -6.05]}, {"time": 0.5333, "value": -6.05, "curve": [0.575, -6.05, 0.658, -2.29]}, {"time": 0.7, "value": -2.29}], "translate": [{}]}, "HEAD": {"rotate": [{"value": -2.12, "curve": [0.048, -2.12, 0.078, -1.45]}, {"time": 0.1333, "value": -1.45, "curve": [0.193, -1.45, 0.231, -6.27]}, {"time": 0.3, "value": -6.27, "curve": [0.432, -6.27, 0.515, 2.18]}, {"time": 0.6667, "value": 2.18, "curve": [0.738, 2.18, 0.784, -2.12]}, {"time": 0.8667, "value": -2.12}], "translate": [{"x": -127.99, "y": 22.63, "curve": [0.05, -127.99, 0.15, -34.7, 0.05, 22.63, 0.15, 8.25]}, {"time": 0.2, "x": -34.7, "y": 8.25, "curve": [0.242, -34.7, 0.325, 6.05, 0.242, 8.25, 0.325, 9.14]}, {"time": 0.3667, "x": 6.05, "y": 9.14, "curve": [0.425, 6.05, 0.542, -63.63, 0.425, 9.14, 0.542, 19.47]}, {"time": 0.6, "x": -63.63, "y": 19.47, "curve": [0.667, -63.63, 0.709, 0, 0.667, 19.47, 0.709, 0]}, {"time": 0.8667}], "scale": [{"x": 0.653, "y": 0.972, "curve": [0.042, 0.653, 0.125, 1, 0.042, 0.972, 0.125, 1]}, {"time": 0.1667}]}, "R_foot": {"rotate": [{"value": 3.59, "curve": [0.042, 3.59, 0.125, -19.94]}, {"time": 0.1667, "value": -19.94, "curve": [0.217, -19.94, 0.317, 19.82]}, {"time": 0.3667, "value": 19.82, "curve": [0.408, 19.82, 0.492, -0.8]}, {"time": 0.5333, "value": -0.8, "curve": [0.575, -0.8, 0.658, 3.59]}, {"time": 0.7, "value": 3.59}], "translate": [{}]}, "CHEST": {"rotate": [{"value": 1.95, "curve": [0.048, 1.95, 0.078, 1.21]}, {"time": 0.1333, "value": 1.21, "curve": [0.205, 1.21, 0.251, -10.08]}, {"time": 0.3333, "value": -10.08, "curve": [0.405, -10.08, 0.451, -0.24]}, {"time": 0.5333, "value": -0.24, "curve": [0.605, -0.24, 0.651, 1.95]}, {"time": 0.7333, "value": 1.95}], "translate": [{"x": -133.69, "y": 24.33, "curve": [0.088, -133.69, 0.077, -56.97, 0.088, 24.33, 0.077, 13.73]}, {"time": 0.1667, "x": -56.97, "y": 13.73, "curve": [0.2, -56.97, 0.267, 57.66, 0.2, 13.73, 0.267, -10.49]}, {"time": 0.3, "x": 57.66, "y": -10.49, "curve": [0.405, 57.66, 0.393, -22.56, 0.405, -10.49, 0.393, 4.11]}, {"time": 0.5, "x": -22.56, "y": 4.11, "curve": [0.588, -22.56, 0.577, 0, 0.588, 4.11, 0.577, 0]}, {"time": 0.6667}], "scale": [{"x": 0.653, "y": 0.972, "curve": [0.042, 0.653, 0.125, 1, 0.042, 0.972, 0.125, 1]}, {"time": 0.1667}]}, "face_h": {"rotate": [{}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.3, 0, 0.433, 20.1, 0.3, 0, 0.433, 50.94]}, {"time": 0.5, "x": 20.1, "y": 50.94, "curve": [0.583, 20.1, 0.75, 0, 0.583, 50.94, 0.75, 0]}, {"time": 0.8333}]}, "R_brow": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8333}], "translate": [{"x": 7.25, "y": -0.95, "curve": [0.042, 7.25, 0.125, -25.3, 0.042, -0.95, 0.125, 22.41]}, {"time": 0.1667, "x": -25.3, "y": 22.41, "curve": [0.233, -25.3, 0.367, 119.93, 0.233, 22.41, 0.367, 53.3]}, {"time": 0.4333, "x": 119.93, "y": 53.3, "curve": [0.492, 119.93, 0.608, -18.7, 0.492, 53.3, 0.608, 29.15]}, {"time": 0.6667, "x": -18.7, "y": 29.15, "curve": [0.708, -18.7, 0.792, 7.25, 0.708, 29.15, 0.792, -0.95]}, {"time": 0.8333, "x": 7.25, "y": -0.95}]}, "L_hand_4": {"rotate": [{"value": -33.11, "curve": [0.033, -33.11, 0.1, -48.21]}, {"time": 0.1333, "value": -48.21, "curve": [0.192, -48.21, 0.308, -17.74]}, {"time": 0.3667, "value": -17.74, "curve": [0.408, -17.74, 0.492, -41.09]}, {"time": 0.5333, "value": -41.09, "curve": [0.575, -41.09, 0.658, -33.11]}, {"time": 0.7, "value": -33.11}], "translate": [{"x": 14.95, "y": -54.65, "curve": "stepped"}, {"time": 0.1333, "x": 14.95, "y": -54.65, "curve": "stepped"}, {"time": 0.3667, "x": 14.95, "y": -54.65, "curve": "stepped"}, {"time": 0.5333, "x": 14.95, "y": -54.65, "curve": "stepped"}, {"time": 0.7, "x": 14.95, "y": -54.65}]}, "R_finger_2": {"rotate": [{}], "translate": [{}]}, "R_finger_1": {"rotate": [{}], "translate": [{}]}, "L_brow": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8333}], "translate": [{"x": 7.25, "y": -0.95, "curve": [0.042, 7.25, 0.125, -25.3, 0.042, -0.95, 0.125, 22.41]}, {"time": 0.1667, "x": -25.3, "y": 22.41, "curve": [0.233, -25.3, 0.367, 119.93, 0.233, 22.41, 0.367, 53.3]}, {"time": 0.4333, "x": 119.93, "y": 53.3, "curve": [0.492, 119.93, 0.608, -18.7, 0.492, 53.3, 0.608, 29.15]}, {"time": 0.6667, "x": -18.7, "y": 29.15, "curve": [0.708, -18.7, 0.792, 7.25, 0.708, 29.15, 0.792, -0.95]}, {"time": 0.8333, "x": 7.25, "y": -0.95}]}, "L_fingeL_4": {"rotate": [{}], "translate": [{}]}, "face_v": {"rotate": [{}], "translate": [{"x": 32.5, "curve": [0.065, 32.5, 0.095, -63.08, 0.065, 0, 0.095, 35.54]}, {"time": 0.1667, "x": -63.08, "y": 35.54, "curve": [0.258, -63.08, 0.299, 116.11, 0.258, 35.54, 0.299, 34.02]}, {"time": 0.4, "x": 116.11, "y": 34.02, "curve": [0.491, 116.11, 0.532, -43.01, 0.491, 34.02, 0.532, 12.12]}, {"time": 0.6333, "x": -43.01, "y": 12.12, "curve": [0.711, -43.01, 0.747, 32.5, 0.711, 12.12, 0.747, 0]}, {"time": 0.8333, "x": 32.5}]}, "R_foot_finger_2": {"rotate": [{"value": 10.54, "curve": [0.05, 10.54, 0.15, 3.09]}, {"time": 0.2, "value": 3.09, "curve": [0.25, 3.09, 0.35, 25.45]}, {"time": 0.4, "value": 25.45, "curve": [0.442, 25.45, 0.525, 10.54]}, {"time": 0.5667, "value": 10.54, "curve": "stepped"}, {"time": 0.7333, "value": 10.54}], "translate": [{}]}, "R_hand_3": {"rotate": [{"value": -2.05}], "translate": [{"x": -5.01, "y": 2.53}]}, "L_eye": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"x": -1.96, "y": 0.41, "curve": [0.042, -1.96, 0.125, -28.28, 0.042, 0.41, 0.125, 4.13]}, {"time": 0.1667, "x": -28.28, "y": 4.13, "curve": [0.208, -28.28, 0.321, -18.83, 0.208, 4.13, 0.321, -17]}, {"time": 0.3333, "x": -24.13, "y": -19.24, "curve": [0.344, -28.83, 0.388, -33.7, 0.344, -21.22, 0.388, -23.24]}, {"time": 0.4, "x": -35.15, "y": -23.71, "curve": [0.441, -40.13, 0.487, -16.14, 0.441, -25.33, 0.487, -22.23]}, {"time": 0.5333, "x": -24.64, "y": -24.99, "curve": "stepped"}, {"time": 0.7333, "x": -27.28, "y": -21.53, "curve": [0.783, -3.56, 0.822, -2.23, 0.783, -11.11, 0.822, 1.95]}, {"time": 0.8667, "x": -2.23, "y": 1.95}]}, "R_hand_2": {"rotate": [{"value": -7.76}], "translate": [{}]}, "R_leg": {"rotate": [{"value": -3.22, "curve": [0.033, -3.22, 0.1, -37.51]}, {"time": 0.1333, "value": -37.51, "curve": [0.183, -37.51, 0.283, 37.94]}, {"time": 0.3333, "value": 37.94, "curve": [0.375, 37.94, 0.458, -20.34]}, {"time": 0.5, "value": -20.34, "curve": [0.542, -20.34, 0.625, -3.22]}, {"time": 0.6667, "value": -3.22}], "translate": [{"x": 4.77, "y": -0.6}]}, "R_arm_1": {"rotate": [{"value": 6.14}], "translate": [{}]}, "R_eye": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"x": -1.96, "y": 0.41, "curve": [0.042, -1.96, 0.125, -28.28, 0.042, 0.41, 0.125, 4.13]}, {"time": 0.1667, "x": -28.28, "y": 4.13, "curve": [0.208, -28.28, 0.321, -18.11, 0.208, 4.13, 0.321, -14.72]}, {"time": 0.3333, "x": -22.72, "y": -14.79, "curve": [0.344, -26.81, 0.388, -31.09, 0.344, -14.85, 0.388, -15.04]}, {"time": 0.4, "x": -32.55, "y": -15.51, "curve": [0.441, -37.53, 0.487, -16.14, 0.441, -17.12, 0.487, -22.23]}, {"time": 0.5333, "x": -24.64, "y": -24.99, "curve": "stepped"}, {"time": 0.7333, "x": -27.28, "y": -21.53, "curve": [0.783, -3.56, 0.822, -0.16, 0.783, -11.11, 0.822, 8.09]}, {"time": 0.8667, "x": -0.16, "y": 8.09}]}, "L_arm_2": {"rotate": [{"value": 132.11}], "translate": [{"x": -52.74, "y": 8.72}]}, "tail base": {"rotate": [{"value": -134.21, "curve": "stepped"}, {"time": 0.1333, "value": -134.21}, {"time": 0.4}]}}, "transform": {"L_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}], "R_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}]}, "path": {"L_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0}]}, "R_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0}]}}, "attachments": {"default": {"head": {"monkey new/head": {"deform": [{"offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": "stepped"}, {"time": 0.3333, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": [0.337, 0, 0.384, 1]}, {"time": 0.4}]}}, "monkey_mouth smile open": {"monkey new/monkey_mouth smile open": {"deform": [{"time": 0.2, "offset": 20, "vertices": [4.8644, 0.27689, 4.86434, 0.27689, 7.26072, -7.45899, 7.26054, -7.45899, 5.83938, 11.35134, 5.83907, 11.35126, 1.33769, -2.62267, 1.33765, -2.62266, -6.9326, 1.09289, -6.93254, 1.09293, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.23757, -13.65027, 8.23747, -13.65026, 0.94061, -20.47297, 5.72784, -14.17318, 5.72754, -14.17319, 8.272, -7.53651, 8.27185, -7.53648, 3.41141, 4.56448, 3.41127, 4.56452, -5.34415, 0.36221, -5.34421, 0.36221, -0.62111, -0.24179, -0.62134, -0.24177, 2.31476, -2.31107, 2.31456, -2.31105, 1.54562, -1.58092, 1.54558, -1.58093, 0, 0, 0, 0, 7.66131, -8.66982, 7.66113, -8.66983, 18.87772, -14.49138, 18.87747, -14.49139, 31.34947, -13.47853, 31.34924, -13.47854, 38.30726, -12.17573, 38.30691, -12.17578, 42.76085, -10.08271, 42.76066, -10.08271, 39.33993, -6.93225, 39.33959, -6.93225, 32.01344, -2.28533, 32.01304, -2.28535, 20.97192, 2.91691, 20.97177, 2.91693, 11.43893, 3.98585, 11.43869, 3.98588, 2.11884, 0.49203, 2.11881, 0.49204, 4.07748, 1.15525, 4.07738, 1.15522, 0.20969, 0.9987, 0.20964, 0.99868], "curve": [0.256, 0, 0.311, 1]}, {"time": 0.3667, "vertices": [6.35907, -11.05709, 6.35883, -11.05712, 7.76804, -9.96597, 7.76775, -9.96595, 5.27553, -10.50494, 5.27536, -10.50483, 0.88996, -8.41034, 0.88966, -8.41028, -2.22936, -0.42924, -2.22962, -0.42913, -10.57374, -8.1484, -10.57376, -8.14831, -10.57374, -8.1484, -10.57376, -8.14831, -7.09026, -5.46395, -7.09029, -5.46386, -2.15085, -4.26374, -2.15091, -4.26374, -3.92697, -7.78463, -3.92716, -7.78464, -4.22917, -7.84306, -4.23006, -7.84298, -5.45512, -7.84498, -5.45572, -7.84496, -7.09372, -7.54249, -7.09454, -7.54249, -7.81975, -6.76591, -7.82072, -6.76599, -7.43089, -7.771, -7.43163, -7.77103, -6.39178, -7.00024, -0.82423, -3.76331, -0.82508, -3.76335, -0.07536, -8.72432, -0.07582, -8.72429, 2.46701, -16.82304, 2.46642, -16.823, -3.43678, -5.92915, -3.43777, -5.92913, -3.13763, 1.10468, -3.13866, 1.10466, -2.73123, 1.15395, -2.73225, 1.15394, -6.599, -2.07798, -6.59998, -2.07789, -8.83203, -7.24593, -8.83283, -7.24597, -7.69891, -2.2849, -7.69955, -2.28486, -7.00674, 6.1866, -7.00777, 6.18664, -11.93363, 6.04715, -11.93452, 6.04725, -18.15676, 5.41187, -18.15766, 5.41193, -23.18118, -6.64676, -23.18203, -6.6468, -24.11134, -19.69867, -24.11201, -19.69848, -20.07222, -33.97836, -20.07287, -33.97795, -10.74653, -37.21215, -10.7471, -37.21196, -4.76852, -38.24274, -4.76924, -38.24254, -2.02578, -33.10817, -2.0262, -33.10806, -2.76824, -28.76043, -2.76904, -28.76046, -2.92989, -12.30921, -2.93089, -12.30907]}, {"time": 0.4333, "vertices": [13.35878, 6.95254, 13.35903, 6.95299, 9.74911, 7.10952, 9.74911, 7.10975, 9.49741, 2.66744, 9.49722, 2.66789, -10.14443, 8.296, -10.14405, 8.29645, 11.16169, 15.0545, 11.16132, 15.05509, 26.26073, 30.97816, 26.26021, 30.97893, 30.02833, -0.69492, 30.02805, -0.69402, 29.47028, -6.1882, 29.46961, -6.18731, 19.35739, -16.33118, 19.35723, -16.33079, 7.08861, -12.97564, 7.08864, -12.97541, -5.03253, -9.10335, -5.03256, -9.10325, -4.11214, -8.48022, -4.11188, -8.47997, -29.61469, -14.1906, -29.61532, -14.19026, -4.96309, -2.01165, -4.96331, -2.0112, -38.35869, -19.82205, -38.35914, -19.82177, -46.11723, -20.68967, -4.84233, 11.58739, -4.84224, 11.58791, 6.62886, 7.4138, 6.62895, 7.41402, 7.65248, 5.00478, 7.65276, 5.00502, -1.76709, 12.48986, -1.76713, 12.49013, -4.73884, 5.95966, -4.73899, 5.96012, -8.39522, -3.64869, -8.39548, -3.64815, -18.19017, -12.54313, -18.19028, -12.54282, -24.29376, -22.28577, -24.29396, -22.28531, -30.48434, -14.40038, -30.48484, -14.39989, -33.19568, -8.31288, -33.19606, -8.31231, -33.27273, -1.76232, -33.27278, -1.76184, -36.25501, -3.79106, -36.25481, -3.79041, -33.94434, -9.52678, -33.94454, -9.52622, -32.4187, -22.67149, -32.41848, -22.67078, -26.75922, -32.5476, -26.7591, -32.54657, -19.55802, -31.22888, -19.55829, -31.22807, -11.39542, -28.51492, -11.39565, -28.51432, -6.00793, -19.14592, -6.00787, -19.1454, -6.58663, -15.75194, -6.58693, -15.75154, -4.00004, 2.44016, -4.00011, 2.44051]}, {"time": 0.6, "vertices": [1.27366, -8.4037, 1.27372, -8.40372, 1.42681, -9.41399, 1.42682, -9.41406, 0.38901, -11.56192, 0.38905, -11.56184, -2.11205, -12.9585, -2.11206, -12.95853, -1.81517, -11.79829, -1.81517, -11.79823, -8.83512, -19.61969, -8.83508, -19.61959, -1.41316, -20.98985, -1.41312, -20.98982, -2.94741, 5.84085, -2.94739, 5.84082, -4.22145, -0.70703, -4.22139, -0.70712, -4.68667, 4.04261, -4.68665, 4.04261, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.45452, -16.19488, 2.4546, -16.1948, 2.76078, -18.21539, 2.76083, -18.21538, 2.18688, -14.4289, 2.18695, -14.42897, 2.34873, -15.49673, 2.34882, -15.49672, 2.07952, -10.75339, 2.07956, -10.7534, 3.45804, -3.00057, 3.4581, -3.00061, -0.87112, -4.69399, -0.87109, -4.69398, -4.07256, -9.52905, -4.07254, -9.52906, -0.47111, -3.47855, -0.47111, -3.47853, 4.93061, -2.81214, 4.93061, -2.81205, 11.34906, -2.75546, 11.34911, -2.75539, 12.69528, -10.50897, 12.69538, -10.50874, 12.36179, -14.19489, 12.36183, -14.19483, 8.339, -24.86013, 8.33897, -24.85985, 1.19613, -27.14281, 1.19614, -27.14234, -0.92925, -33.05733, -0.92935, -33.05719, -0.96747, -37.34008, -0.96758, -37.33979, 2.01062, -38.3865, 2.01063, -38.38633, 1.91701, -33.62633, 1.91695, -33.62629, 2.79537, -20.03094, 2.79534, -20.03091]}, {"time": 0.8, "offset": 8, "vertices": [-0.21392, -0.08778, -0.21392, -0.08777, -0.63958, -0.2624, -0.63958, -0.26239, -0.62699, 0.17438, -0.627, 0.17439, -1.76229, -1.35807, -1.76229, -1.35805, -0.53451, -1.52562, -0.53451, -1.52561, -0.70334, 2.37291, -0.70334, 2.37292, -0.80374, 0.54303, -0.80373, 0.54303, -0.78111, 0.67377, -0.78111, 0.67377, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.70314, 2.87196, -8.7031, 2.87197, -4.99254, -0.04305, -4.99253, -0.04305, -6.57468, 2.16899, -6.57466, 2.16901, -4.62712, 2.0567, -4.62711, 2.0567, -0.67876, -1.58817, -0.67876, -1.58818, 3.0587, -4.01632, 3.05875, -4.01632, 11.65882, -7.29609, 11.65894, -7.29608, 22.08069, -10.38159, 22.0808, -10.38157, 30.94403, -8.4, 30.94419, -8.39997, 33.0019, -7.19376, 33.00201, -7.19372, 32.715, -8.32758, 32.71515, -8.32753, 24.76917, -1.24765, 24.7694, -1.24757, 14.12128, 1.90376, 14.12141, 1.90379, 4.68392, 0.54456, 4.68395, 0.54459, -0.38069, -1.675, -0.3807, -1.67498, -0.23892, -1.06255, -0.23894, -1.06254, -4.71042, 1.50992, -4.71041, 1.50993]}]}}}}, "drawOrder": [{}, {"time": 0.3333}, {"time": 0.8667}]}, "Destroy": {"slots": {"banana_2_1": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff", "curve": [1.517, 1, 1.514, 1, 1.517, 1, 1.514, 1, 1.517, 1, 1.514, 1, 1.517, 1, 1.514, 0]}, {"time": 1.5667, "color": "ffffff00"}], "attachment": [{}]}, "banana_2_2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff", "curve": [1.517, 1, 1.514, 1, 1.517, 1, 1.514, 1, 1.517, 1, 1.514, 1, 1.517, 1, 1.514, 0]}, {"time": 1.5667, "color": "ffffff00"}], "attachment": [{}]}, "banana_3_2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff", "curve": [1.517, 1, 1.514, 1, 1.517, 1, 1.514, 1, 1.517, 1, 1.514, 1, 1.517, 1, 1.514, 0]}, {"time": 1.5667, "color": "ffffff00"}], "attachment": [{}]}, "banana_3_3": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff", "curve": [1.517, 1, 1.514, 1, 1.517, 1, 1.514, 1, 1.517, 1, 1.514, 1, 1.517, 1, 1.514, 0]}, {"time": 1.5667, "color": "ffffff00"}], "attachment": [{}]}, "closed_eye_left": {"attachment": [{"time": 0.1, "name": "monkey new/closed_eye_left"}, {"time": 0.2667}]}, "closed_eye_right": {"attachment": [{"time": 0.1, "name": "monkey new/closed_eye_right"}, {"time": 0.2667}]}, "monkeyOLD_banana_1": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff", "curve": [1.517, 1, 1.514, 1, 1.517, 1, 1.514, 1, 1.517, 1, 1.514, 1, 1.517, 1, 1.514, 0]}, {"time": 1.5667, "color": "ffffff00"}], "attachment": [{}]}, "monkeyOLD_banana_3_1": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff", "curve": [1.517, 1, 1.514, 1, 1.517, 1, 1.514, 1, 1.517, 1, 1.514, 1, 1.517, 1, 1.514, 0]}, {"time": 1.5667, "color": "ffffff00"}], "attachment": [{}]}, "monkey_hand left": {"attachment": [{"time": 0.0333}]}, "monkey_hand right": {"attachment": [{"time": 0.0333}]}, "monkey_left_arm": {"attachment": [{}, {"time": 0.0333, "name": "monkey new/monkey_left_arm"}]}, "monkey_left_hand": {"attachment": [{}, {"time": 0.0333, "name": "monkey new/monkey_left_hand"}]}, "monkey_left_hand closed": {"attachment": [{}]}, "monkey_mouth angry": {"attachment": [{}]}, "monkey_mouth o": {"attachment": [{}]}, "monkey_mouth smile closed": {"attachment": [{"time": 0.2333}, {"time": 1, "name": "monkey new/monkey_mouth smile closed"}]}, "monkey_mouth smile open": {"attachment": [{}, {"time": 0.2333, "name": "monkey new/monkey_mouth smile open"}, {"time": 1}]}, "monkey_right_hand": {"attachment": [{}, {"time": 0.0333, "name": "monkey new/monkey_right_hand"}]}, "shadow": {"alpha": [{"time": 0.2667, "value": 1, "curve": [0.289, 1, 0.311, 0]}, {"time": 0.3333, "value": 0}]}}, "bones": {"Banana": {"rotate": [{"value": 0.03, "curve": "stepped"}, {"time": 0.8333, "value": 0.03}], "translate": [{"x": 6.4, "y": -7.09, "curve": "stepped"}, {"time": 0.8333, "x": 6.4, "y": -7.09}]}, "root": {"translate": [{"curve": "stepped"}, {"time": 0.2, "curve": [0.425, 0, 0.875, 696.7, 0.425, 0, 0.875, 0]}, {"time": 1.1, "x": 696.7}]}, "L_fingeL_5": {"rotate": [{"value": -0.11}], "translate": [{}]}, "main_C": {"rotate": [{}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.276, 0, 0.395, 0, 0.276, 0, 0.395, 1258.48]}, {"time": 0.5667, "y": 1274.97, "curve": [0.994, 0, 1.008, 0, 0.994, 1308.35, 1.008, -1404.7]}, {"time": 1.2333, "y": -5037.9, "curve": [1.3, 0, 1.448, 0, 1.3, -6524.72, 1.448, -9250.55]}, {"time": 1.5667, "y": -11551.58}]}, "R_hand_1": {"rotate": [{"value": 7.97, "curve": [0.063, 35.4, 0.121, 54.11]}, {"time": 0.1667, "value": 54.11, "curve": [0.256, 54.11, 0.227, -12.47]}, {"time": 0.3, "value": -12.47}], "translate": [{"x": 3.53, "y": 1.05, "curve": [0.063, -18.45, 0.121, -33.43, 0.063, 5.58, 0.121, 8.67]}, {"time": 0.1667, "x": -33.43, "y": 8.67, "curve": [0.256, -33.43, 0.227, -5.23, 0.256, 8.67, 0.227, 1.92]}, {"time": 0.3, "x": -5.23, "y": 1.92}]}, "R_foot": {"rotate": [{"value": 3.59, "curve": [0.044, -1.3, 0.089, -11.07]}, {"time": 0.1333, "value": -11.07, "curve": [0.189, -11.07, 0.244, 54.53]}, {"time": 0.3, "value": 84.84, "curve": [0.344, 109.09, 0.389, 152.62]}, {"time": 0.4333, "value": 152.62, "curve": [0.478, 152.62, 0.523, 92.58]}, {"time": 0.5667, "value": 59.79, "curve": [0.645, 3.16, 0.723, -42.45]}, {"time": 0.8, "value": -63.32, "curve": [0.889, -87.29, 0.978, -9.84]}, {"time": 1.0667, "value": -18.13}], "translate": [{"curve": [0.044, 4.11, 0.089, 0, 0.044, 2.68, 0.089, 0.11]}, {"time": 0.1333, "x": 12.33, "y": 8.04, "curve": [0.189, 27.74, 0.244, 166.97, 0.189, 17.96, 0.244, 31.05]}, {"time": 0.3, "x": 166.97, "y": 53.55, "curve": [0.344, 166.97, 0.389, 116.62, 0.344, 71.55, 0.389, 129.56]}, {"time": 0.4333, "x": 101.78, "y": 129.56, "curve": [0.556, 60.96, 0.678, 6.87, 0.556, 129.56, 0.678, 49.55]}, {"time": 0.8, "x": -27.06, "y": 6.37}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": [0.189, 1, 0.245, 1.161, 0.189, 1, 0.245, 1.161]}, {"time": 0.3, "x": 1.161, "y": 1.161, "curve": [0.389, 1.161, 0.478, 1, 0.389, 1.161, 0.478, 1]}, {"time": 0.5667}]}, "R_brow": {"rotate": [{"curve": "stepped"}, {"time": 0.1667}], "translate": [{"x": 7.25, "y": -0.95, "curve": [0.043, 27.33, 0.128, 62.39, 0.043, 0.16, 0.128, 2.09]}, {"time": 0.1667, "x": 76.1, "y": 2.84, "curve": [0.225, 76.1, 0.342, -34.72, 0.225, 2.84, 0.342, 2.51]}, {"time": 0.4, "x": -34.72, "y": 2.51, "curve": [0.458, -34.72, 0.575, -1.23, 0.458, 2.51, 0.575, 22.6]}, {"time": 0.6333, "x": -1.23, "y": 22.6, "curve": [0.7, -1.23, 0.833, -14.94, 0.7, 22.6, 0.833, -10.63]}, {"time": 0.9, "x": -14.94, "y": -10.63, "curve": [0.942, -14.94, 1.025, 39.5, 0.942, -10.63, 1.025, -23.41]}, {"time": 1.0667, "x": 39.5, "y": -23.41}]}, "R_eye": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6}], "translate": [{"x": -1.96, "y": 0.41, "curve": [0.033, -1.96, 0.1, -17.1, 0.033, 0.41, 0.1, 7.78]}, {"time": 0.1333, "x": -17.1, "y": 7.78, "curve": [0.183, -17.1, 0.283, 16.03, 0.183, 7.78, 0.283, 4.83]}, {"time": 0.3333, "x": 16.03, "y": 4.83, "curve": [0.373, 16.03, 0.405, 32.25, 0.373, 4.83, 0.405, 5.86]}, {"time": 0.4333, "x": 24.15, "y": 3.69, "curve": [0.486, 9.31, 0.53, -34.17, 0.486, -0.28, 0.53, -8.61]}, {"time": 0.6, "x": -34.17, "y": -8.61, "curve": [0.658, -34.17, 0.775, -17.02, 0.658, -8.61, 0.775, 9.08]}, {"time": 0.8333, "x": -17.02, "y": 9.08, "curve": [0.875, -17.02, 0.958, 31.62, 0.875, 9.08, 0.958, 3.32]}, {"time": 1, "x": 31.62, "y": 3.32}]}, "R_finger_2": {"rotate": [{"value": -0.11, "curve": [0.117, -0.11, 0.35, 15.81]}, {"time": 0.4667, "value": 15.81, "curve": [0.508, 15.81, 0.592, -20.59]}, {"time": 0.6333, "value": -20.59, "curve": [0.658, -20.59, 0.708, 0]}, {"time": 0.7333}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.7333}]}, "HEAD": {"rotate": [{"value": 3.59, "curve": [0.015, 3.59, 0.116, 7.49]}, {"time": 0.2667, "value": 7.49, "curve": [0.332, 7.49, 0.373, 4.52]}, {"time": 0.4333, "value": 4.52, "curve": [0.507, 4.52, 0.569, -0.73]}, {"time": 0.6333, "value": -0.73}], "translate": [{"curve": [0.015, 0, 0.116, -68.12, 0.015, 0, 0.116, 0.69]}, {"time": 0.2667, "x": -68.12, "y": 0.69, "curve": [0.332, -68.12, 0.373, 25.82, 0.332, 0.69, 0.373, -5.4]}, {"time": 0.4333, "x": 25.82, "y": -5.4, "curve": [0.507, 25.82, 0.569, -19.59, 0.507, -5.4, 0.569, -16.28]}, {"time": 0.6333, "x": -19.59, "y": -16.28, "curve": [0.692, -19.59, 0.808, 4.52, 0.692, -16.28, 0.808, -17.3]}, {"time": 0.8667, "x": 4.52, "y": -17.3}], "scale": [{}]}, "R_leg": {"rotate": [{"value": -3.22, "curve": [0.075, -3.22, 0.078, 62.17]}, {"time": 0.1333, "value": 62.17, "curve": [0.227, 62.17, 0.23, -80.32]}, {"time": 0.3, "value": -80.32, "curve": [0.322, -80.32, 0.371, -91.12]}, {"time": 0.4333, "value": -91.12, "curve": [0.534, -91.12, 0.606, 73.47]}, {"time": 0.8, "value": 73.47, "curve": [0.982, 73.47, 1.046, 104.94]}, {"time": 1.0667, "value": 104.94}], "translate": [{"x": 4.77, "y": -0.6, "curve": [0.075, 4.77, 0.078, 39.31, 0.075, -0.6, 0.078, 6.71]}, {"time": 0.1333, "x": 39.31, "y": 6.71, "curve": [0.227, 39.31, 0.23, 3.04, 0.227, 6.71, 0.23, -19.62]}, {"time": 0.3, "x": 3.04, "y": -19.62, "curve": [0.322, 3.04, 0.371, -8.91, 0.322, -19.62, 0.371, 7.79]}, {"time": 0.4333, "x": -8.91, "y": 7.79, "curve": [0.534, -8.91, 0.606, -7.21, 0.534, 7.79, 0.606, 5.39]}, {"time": 0.8, "x": -7.21, "y": 5.39}], "scale": [{"curve": [0.033, 1, 0.1, 1.306, 0.033, 1, 0.1, 1]}, {"time": 0.1333, "x": 1.306, "curve": [0.156, 1.306, 0.281, 1, 0.156, 1, 0.281, 1]}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4333}]}, "R_foot2": {"rotate": [{"value": -2.29, "curve": [0.044, -5.21, 0.089, -11.05]}, {"time": 0.1333, "value": -11.05, "curve": [0.189, -11.05, 0.244, 79.2]}, {"time": 0.3, "value": 107.51, "curve": [0.344, 130.15, 0.389, 141.81]}, {"time": 0.4333, "value": 141.81, "curve": [0.556, 141.81, 0.678, -15.57]}, {"time": 0.8, "value": -15.57, "curve": [0.889, -15.57, 0.978, 19.12]}, {"time": 1.0667, "value": 24.69}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": [0.189, 0, 0.244, 170.8, 0.189, 0, 0.244, 134.09]}, {"time": 0.3, "x": 170.8, "y": 134.09, "curve": "stepped"}, {"time": 0.4333, "x": 170.8, "y": 134.09, "curve": [0.478, 170.8, 0.678, 2.62, 0.478, 134.09, 0.678, 3.16]}, {"time": 0.8}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.5667}]}, "R_finger_3": {"rotate": [{"value": -0.11, "curve": [0.117, -0.11, 0.35, 15.81]}, {"time": 0.4667, "value": 15.81, "curve": [0.508, 15.81, 0.592, -20.59]}, {"time": 0.6333, "value": -20.59, "curve": [0.658, -20.59, 0.708, 0]}, {"time": 0.7333}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.7333}]}, "L_fingeL_6": {"rotate": [{"value": -0.11}], "translate": [{}]}, "R_hand_3": {"rotate": [{"value": -2.05, "curve": [0.042, -2.05, 0.059, 6.27]}, {"time": 0.1667, "value": 6.27, "curve": [0.256, 6.27, 0.227, -32.05]}, {"time": 0.3, "value": -32.05}], "translate": [{"x": -5.01, "y": 2.53, "curve": [0.042, -5.01, 0.059, 38.3, 0.042, 2.53, 0.059, -8.18]}, {"time": 0.1667, "x": 38.3, "y": -8.18, "curve": [0.256, 38.3, 0.227, 51.72, 0.256, -8.18, 0.227, -12.88]}, {"time": 0.3, "x": 51.72, "y": -12.88}]}, "L_fingeL_4": {"rotate": [{"value": -0.11}], "translate": [{}]}, "mouth": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.8333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.8333}]}, "L_brow": {"rotate": [{"curve": "stepped"}, {"time": 0.1667}], "translate": [{"x": 7.25, "y": -0.95, "curve": [0.043, 27.33, 0.128, 62.39, 0.043, 0.16, 0.128, 2.09]}, {"time": 0.1667, "x": 76.1, "y": 2.84, "curve": [0.225, 76.1, 0.342, -34.72, 0.225, 2.84, 0.342, 2.51]}, {"time": 0.4, "x": -34.72, "y": 2.51, "curve": [0.458, -34.72, 0.575, -1.23, 0.458, 2.51, 0.575, 22.6]}, {"time": 0.6333, "x": -1.23, "y": 22.6, "curve": [0.7, -1.23, 0.833, -14.94, 0.7, 22.6, 0.833, -10.63]}, {"time": 0.9, "x": -14.94, "y": -10.63, "curve": [0.942, -14.94, 1.025, 39.5, 0.942, -10.63, 1.025, -23.41]}, {"time": 1.0667, "x": 39.5, "y": -23.41}]}, "R_hand_2": {"rotate": [{"value": -7.76, "curve": [0.042, -7.76, 0.059, 27.01]}, {"time": 0.1667, "value": 27.01, "curve": [0.256, 27.01, 0.227, 17.97]}, {"time": 0.3, "value": 17.97}], "translate": [{"curve": [0.042, 0, 0.059, 41, 0.042, 0, 0.059, 28.51]}, {"time": 0.1667, "x": 41, "y": 28.51, "curve": [0.256, 41, 0.227, 6.79, 0.256, 28.51, 0.227, -5.45]}, {"time": 0.3, "x": 6.79, "y": -5.45}]}, "R_finger_1": {"rotate": [{"value": -0.11, "curve": [0.117, -0.11, 0.35, 15.81]}, {"time": 0.4667, "value": 15.81, "curve": [0.508, 15.81, 0.592, -20.59]}, {"time": 0.6333, "value": -20.59, "curve": [0.658, -20.59, 0.708, 0]}, {"time": 0.7333}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.7333}]}, "face_h": {"rotate": [{}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4, "curve": [0.45, 0, 0.55, 1.28, 0.45, 0, 0.55, 47.9]}, {"time": 0.6, "x": 1.28, "y": 47.9, "curve": [0.65, 1.28, 0.75, 0.33, 0.65, 47.9, 0.75, 11.98]}, {"time": 0.8, "x": 0.33, "y": 11.98, "curve": [0.858, 0.33, 0.975, -0.54, 0.858, 11.98, 0.975, -20.96]}, {"time": 1.0333, "x": -0.54, "y": -20.96}]}, "L_arm_2": {"rotate": [{"value": 132.11, "curve": "stepped"}, {"time": 0.0667, "value": 226.04, "curve": "stepped"}, {"time": 0.2333, "value": 142.17}], "translate": [{"x": -52.74, "y": 8.72, "curve": "stepped"}, {"time": 0.0667, "x": -58.65, "y": -44.27, "curve": "stepped"}, {"time": 0.2333, "x": -81.84, "y": 38.18}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "y": -0.957, "curve": "stepped"}, {"time": 0.2333, "x": 1.037, "y": 1.025}]}, "L_hand_4": {"rotate": [{"value": -33.11, "curve": [0.064, -33.11, 0.059, -57.86]}, {"time": 0.1667, "value": -57.86, "curve": [0.256, -57.86, 0.227, -3.75]}, {"time": 0.3, "value": -3.75}], "translate": [{"x": 14.95, "y": -54.65, "curve": [0.064, 14.95, 0.059, -46.33, 0.064, -54.65, 0.059, -20.19]}, {"time": 0.1667, "x": -46.33, "y": -20.19, "curve": [0.256, -46.33, 0.227, -49.92, 0.256, -20.19, 0.227, -14.83]}, {"time": 0.3, "x": -49.92, "y": -14.83}]}, "L_hand_5": {"rotate": [{"value": -62.17, "curve": [0.042, -62.17, 0.059, 17.82]}, {"time": 0.1667, "value": 17.82, "curve": [0.256, 17.82, 0.227, -29.95]}, {"time": 0.3, "value": -29.95}], "translate": [{"x": -0.79, "y": -11.59, "curve": [0.042, -0.79, 0.059, 39.6, 0.042, -11.59, 0.059, 40.71]}, {"time": 0.1667, "x": 39.6, "y": 40.71, "curve": [0.256, 39.6, 0.227, 36.83, 0.256, 40.71, 0.227, 3.65]}, {"time": 0.3, "x": 36.83, "y": 3.65}]}, "SPINE": {"rotate": [{"curve": [0.01, 0, 0.073, -1.44]}, {"time": 0.1667, "value": -1.44, "curve": [0.232, -1.44, 0.273, -4.12]}, {"time": 0.3333, "value": -4.12, "curve": [0.395, -4.12, 0.447, -4.94]}, {"time": 0.5, "value": -4.94}], "translate": [{"curve": [0.01, 0, 0.073, -16.29, 0.01, 0, 0.073, 0]}, {"time": 0.1667, "x": -16.29, "curve": [0.232, -16.29, 0.273, 0, 0.232, 0, 0.273, 0]}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5}]}, "L_eye": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6}], "translate": [{"x": -1.96, "y": 0.41, "curve": [0.033, -1.96, 0.1, -17.1, 0.033, 0.41, 0.1, 7.78]}, {"time": 0.1333, "x": -17.1, "y": 7.78, "curve": [0.183, -17.1, 0.283, 16.03, 0.183, 7.78, 0.283, 4.83]}, {"time": 0.3333, "x": 16.03, "y": 4.83, "curve": [0.373, 16.03, 0.405, 32.26, 0.373, 4.83, 0.405, 5.86]}, {"time": 0.4333, "x": 24.15, "y": 3.69, "curve": [0.486, 9.31, 0.53, -34.17, 0.486, -0.28, 0.53, -8.61]}, {"time": 0.6, "x": -34.17, "y": -8.61, "curve": [0.658, -34.17, 0.775, -17.02, 0.658, -8.61, 0.775, 9.08]}, {"time": 0.8333, "x": -17.02, "y": 9.08, "curve": [0.875, -17.02, 0.958, 31.62, 0.875, 9.08, 0.958, 3.32]}, {"time": 1, "x": 31.62, "y": 3.32}]}, "L_hand_6": {"rotate": [{"value": -55.93, "curve": [0.042, -55.93, 0.059, 12.13]}, {"time": 0.1667, "value": 12.13, "curve": [0.256, 12.13, 0.227, -15.77]}, {"time": 0.3, "value": -15.77}], "translate": [{"x": -31.68, "y": -25, "curve": [0.042, -31.68, 0.059, 33.26, 0.042, -25, 0.059, 8.22]}, {"time": 0.1667, "x": 33.26, "y": 8.22, "curve": [0.256, 33.26, 0.227, 33.07, 0.256, 8.22, 0.227, -2.03]}, {"time": 0.3, "x": 33.07, "y": -2.03}]}, "R_leg2": {"rotate": [{"value": 5.75, "curve": [0.075, 5.75, 0.078, -59.31]}, {"time": 0.1333, "value": -59.31, "curve": [0.227, -59.31, 0.23, 91.21]}, {"time": 0.3, "value": 91.21, "curve": [0.322, 91.21, 0.371, 93.95]}, {"time": 0.4333, "value": 93.95, "curve": [0.534, 93.95, 0.606, -61.81]}, {"time": 0.8, "value": -61.81, "curve": [0.982, -61.81, 1.046, -73.54]}, {"time": 1.0667, "value": -73.54}], "translate": [{"x": -0.6, "y": 0.6, "curve": [0.075, -0.6, 0.078, -14.79, 0.075, 0.6, 0.078, 15.94]}, {"time": 0.1333, "x": -14.79, "y": 15.94, "curve": [0.227, -14.79, 0.23, 68.96, 0.227, 15.94, 0.23, -17.94]}, {"time": 0.3, "x": 68.96, "y": -17.94, "curve": [0.322, 68.96, 0.371, 78.47, 0.322, -17.94, 0.371, 3.5]}, {"time": 0.4333, "x": 78.47, "y": 3.5, "curve": [0.534, 78.47, 0.606, 38.34, 0.534, 3.5, 0.606, 18.57]}, {"time": 0.8, "x": 38.34, "y": 18.57}], "scale": [{"curve": [0.033, 1, 0.1, 1.288, 0.033, 1, 0.1, 1]}, {"time": 0.1333, "x": 1.288, "curve": [0.175, 1.288, 0.258, 1.367, 0.175, 1, 0.258, 1]}, {"time": 0.3, "x": 1.367, "curve": [0.333, 1.367, 0.4, 1, 0.333, 1, 0.4, 1]}, {"time": 0.4333}]}, "face_v": {"rotate": [{}], "translate": [{"x": 32.5, "curve": [0.033, 69.62, 0.112, 149.25, 0.033, 1.39, 0.112, 4.37]}, {"time": 0.2, "x": 149.25, "y": 4.37, "curve": [0.258, 149.25, 0.375, -51.1, 0.258, 4.37, 0.375, 5.64]}, {"time": 0.4333, "x": -51.1, "y": 5.64, "curve": [0.501, -51.1, 0.57, 28.85, 0.501, 5.64, 0.57, 3.68]}, {"time": 0.6333, "x": 28.85, "y": 3.68, "curve": [0.701, 28.85, 0.77, -69.16, 0.701, 3.68, 0.77, 6.2]}, {"time": 0.8333, "x": -69.16, "y": 6.2, "curve": [0.912, -69.16, 0.992, 76.75, 0.912, 6.2, 0.992, 2.35]}, {"time": 1.0667, "x": 76.75, "y": 2.35}]}, "R_foot_finger_5": {"rotate": [{"value": 9.43, "curve": [0.075, 9.43, 0.078, 14.47]}, {"time": 0.1333, "value": 14.47, "curve": [0.227, 14.47, 0.23, 11.61]}, {"time": 0.3, "value": 11.61, "curve": [0.333, 11.61, 0.4, -14.62]}, {"time": 0.4333, "value": -14.62, "curve": [0.534, -14.62, 0.606, 9.43]}, {"time": 0.8, "value": 9.43}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.8}]}, "CHEST": {"rotate": [{"value": 1.95, "curve": [0.012, 1.95, 0.087, -2.87]}, {"time": 0.2, "value": -2.87, "curve": [0.265, -2.87, 0.306, -2.82]}, {"time": 0.3667, "value": -2.82, "curve": [0.428, -2.82, 0.48, -0.72]}, {"time": 0.5333, "value": -0.72}], "translate": [{"curve": [0.012, 0, 0.087, -77.07, 0.012, 0, 0.087, -10.79]}, {"time": 0.2, "x": -77.07, "y": -10.79, "curve": [0.265, -77.07, 0.306, 64.43, 0.265, -10.79, 0.306, -14.67]}, {"time": 0.3667, "x": 64.43, "y": -14.67, "curve": [0.428, 64.43, 0.48, -3.55, 0.428, -14.67, 0.48, -8.95]}, {"time": 0.5333, "x": -3.55, "y": -8.95, "curve": [0.617, -3.55, 0.783, 56.26, 0.617, -8.95, 0.783, -14.58]}, {"time": 0.8667, "x": 56.26, "y": -14.58}], "scale": [{}]}, "R_foot_finger_2": {"rotate": [{"value": 10.54, "curve": [0.075, 10.54, 0.078, 10.37]}, {"time": 0.1333, "value": 10.37, "curve": [0.227, 10.37, 0.23, 23.89]}, {"time": 0.3, "value": 23.89, "curve": [0.333, 23.89, 0.4, 1.36]}, {"time": 0.4333, "value": 1.36, "curve": [0.534, 1.36, 0.606, 10.54]}, {"time": 0.8, "value": 10.54}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": [0.227, 0, 0.23, -6.54, 0.227, 0, 0.23, -2.9]}, {"time": 0.3, "x": -6.54, "y": -2.9, "curve": "stepped"}, {"time": 0.4333, "x": -6.54, "y": -2.9, "curve": [0.534, -6.54, 0.606, 0, 0.534, -2.9, 0.606, 0]}, {"time": 0.8}]}, "R_arm_1": {"rotate": [{"value": 6.14, "curve": "stepped"}, {"time": 0.0667, "value": -135.75, "curve": "stepped"}, {"time": 0.2333, "value": -230.22}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "x": -67.42, "y": -30.22, "curve": "stepped"}, {"time": 0.2333, "x": -73.6, "y": 46.05}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "y": -1.155, "curve": "stepped"}, {"time": 0.2333, "x": 1.031, "y": 1.149}]}, "R_hand_c_2": {"rotate": [{"curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.4333, "value": -92.11, "curve": [0.496, -84.59, 0.56, -77.91]}, {"time": 0.6333, "value": -68.71, "curve": [0.707, -65.53, 0.782, -62.7]}, {"time": 0.8667, "value": -58.8, "curve": [0.94, -61.9, 1.015, -64.66]}, {"time": 1.1, "value": -68.46, "curve": "stepped"}, {"time": 1.3333, "value": -68.46, "curve": "stepped"}, {"time": 1.5667, "value": -68.46}], "translate": [{"y": -1.56, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.4333, "x": 16.8, "y": -63.52, "curve": [0.444, 20.41, 0.455, 23.82, 0.444, -66.23, 0.455, -68.78]}, {"time": 0.4667, "x": 27.43, "y": -71.49, "curve": [0.477, -11.24, 0.488, -52.08, 0.477, -61.65, 0.488, -51.25]}, {"time": 0.5, "x": -99.38, "y": -39.21, "curve": [0.511, -123.71, 0.521, -145.35, 0.511, -16.4, 0.521, 3.89]}, {"time": 0.5333, "x": -175.12, "y": 31.81, "curve": [0.554, -178.95, 0.576, -182.36, 0.554, 79.09, 0.576, 121.13]}, {"time": 0.6, "x": -187.05, "y": 178.99, "curve": [0.611, -185.8, 0.621, -184.69, 0.611, 201.12, 0.621, 220.8]}, {"time": 0.6333, "x": -183.16, "y": 247.88, "curve": [0.654, -138.16, 0.676, -98.14, 0.654, 232.26, 0.676, 218.38]}, {"time": 0.7, "x": -43.07, "y": 199.27, "curve": [0.721, 0.24, 0.742, 38.76, 0.721, 158.08, 0.742, 121.44]}, {"time": 0.7667, "x": 91.76, "y": 71.03, "curve": [0.788, 86.69, 0.809, 82.18, 0.788, 43.52, 0.809, 19.06]}, {"time": 0.8333, "x": 75.98, "y": -14.61, "curve": [0.844, 64.51, 0.855, 54.31, 0.844, -14.61, 0.855, -14.62]}, {"time": 0.8667, "x": 40.28, "y": -14.62, "curve": [0.888, 14.85, 0.909, -7.76, 0.888, -13.77, 0.909, -13.02]}, {"time": 0.9333, "x": -38.88, "y": -11.99, "curve": [0.954, -68.99, 0.976, -95.77, 0.954, 30.92, 0.976, 69.08]}, {"time": 1, "x": -132.62, "y": 121.6, "curve": [1.011, -125.18, 1.021, -118.55, 1.011, 158.4, 1.021, 191.12]}, {"time": 1.0333, "x": -109.44, "y": 236.15, "curve": [1.054, -92.41, 1.076, -77.26, 1.054, 245.17, 1.076, 253.18]}, {"time": 1.1, "x": -56.41, "y": 264.22, "curve": [1.127, -56.41, 1.164, 53.1, 1.127, 264.22, 1.164, 188.92]}, {"time": 1.2, "x": 124.03, "y": 140.15, "curve": [1.211, 121.98, 1.221, 120.16, 1.211, 109.65, 1.221, 82.52]}, {"time": 1.2333, "x": 117.66, "y": 45.19, "curve": [1.244, 113.17, 1.255, 109.18, 1.244, 24.34, 1.255, 5.81]}, {"time": 1.2667, "x": 103.68, "y": -19.7, "curve": [1.288, 85.73, 1.309, 69.77, 1.288, -25.42, 1.309, -30.51]}, {"time": 1.3333, "x": 47.81, "y": -37.51, "curve": [1.354, 13.87, 1.376, -16.31, 1.354, -14.36, 1.376, 6.21]}, {"time": 1.4, "x": -57.83, "y": 34.53, "curve": [1.421, -84.22, 1.442, -107.67, 1.421, 67.29, 1.442, 96.42]}, {"time": 1.4667, "x": -139.96, "y": 136.51, "curve": [1.488, -140.45, 1.509, -140.9, 1.488, 172.94, 1.509, 205.32]}, {"time": 1.5333, "x": -141.5, "y": 249.89, "curve": [1.544, -120.12, 1.555, -101.1, 1.544, 255.88, 1.555, 261.2]}, {"time": 1.5667, "x": -74.92, "y": 268.53}]}, "L_hand_c_2": {"rotate": [{"curve": [0.1, 0, 0.3, -81.72]}, {"time": 0.4, "value": -81.72, "curve": [0.408, -81.72, 0.425, 75.43]}, {"time": 0.4333, "value": 75.43, "curve": [0.483, 75.43, 0.583, 66.96]}, {"time": 0.6333, "value": 66.96, "curve": [0.666, 66.96, 0.718, 55.93]}, {"time": 0.7667, "value": 46.48, "curve": [0.805, 51.97, 0.841, 56.78]}, {"time": 0.8667, "value": 56.78, "curve": [0.925, 56.78, 1.042, 63.77]}, {"time": 1.1, "value": 63.77, "curve": [1.126, 63.77, 1.162, 52.94]}, {"time": 1.2, "value": 40.57, "curve": [1.249, 53.66, 1.301, 68.93]}, {"time": 1.3333, "value": 68.93, "curve": [1.392, 68.93, 1.508, 57.63]}, {"time": 1.5667, "value": 57.63}], "translate": [{"curve": [0.1, 0, 0.3, -33.66, 0.1, 0, 0.3, -95.79]}, {"time": 0.4, "x": -33.66, "y": -95.79, "curve": [0.408, -33.66, 0.425, -30.83, 0.408, -95.79, 0.425, -133.78]}, {"time": 0.4333, "x": -30.83, "y": -133.78, "curve": [0.483, -30.83, 0.583, 53.24, 0.483, -133.78, 0.583, -97.57]}, {"time": 0.6333, "x": 53.24, "y": -97.57, "curve": [0.651, 53.24, 0.675, 75.25, 0.651, -97.57, 0.675, -98.51]}, {"time": 0.7, "x": 106.7, "y": -99.87, "curve": [0.721, 129.77, 0.744, 159.76, 0.721, -71.54, 0.744, -34.72]}, {"time": 0.7667, "x": 187.44, "y": -0.73, "curve": [0.79, 196.51, 0.813, 204.83, 0.79, 115.63, 0.813, 222.29]}, {"time": 0.8333, "x": 209.36, "y": 280.47, "curve": [0.846, 195.65, 0.857, 187.64, 0.846, 302, 0.857, 314.56]}, {"time": 0.8667, "x": 187.64, "y": 314.56, "curve": [0.885, 187.64, 0.908, 146.41, 0.885, 314.56, 0.908, 297.76]}, {"time": 0.9333, "x": 87.52, "y": 273.76, "curve": [0.944, 72.29, 0.955, 54.77, 0.944, 259.15, 0.955, 242.34]}, {"time": 0.9667, "x": 36.5, "y": 224.81, "curve": [0.989, 10.47, 1.012, -17.72, 0.989, 162.33, 1.012, 94.63]}, {"time": 1.0333, "x": -39.41, "y": 42.56, "curve": [1.059, 3.65, 1.082, 33.79, 1.059, -5.96, 1.082, -39.93]}, {"time": 1.1, "x": 33.79, "y": -39.93, "curve": [1.118, 33.79, 1.141, 75.92, 1.118, -39.93, 1.141, -20.18]}, {"time": 1.1667, "x": 136.1, "y": 8.04, "curve": [1.177, 156.71, 1.189, 180.41, 1.177, 26.72, 1.189, 48.2]}, {"time": 1.2, "x": 205.15, "y": 70.62, "curve": [1.222, 203.49, 1.245, 201.69, 1.222, 131.19, 1.245, 196.8]}, {"time": 1.2667, "x": 200.3, "y": 247.27, "curve": [1.292, 161.62, 1.315, 134.54, 1.292, 286.07, 1.315, 313.22]}, {"time": 1.3333, "x": 134.54, "y": 313.22, "curve": [1.359, 134.54, 1.395, 82.27, 1.359, 313.22, 1.395, 268.32]}, {"time": 1.4333, "x": 22.54, "y": 217, "curve": [1.456, -0.46, 1.479, -25.37, 1.456, 159.37, 1.479, 96.95]}, {"time": 1.5, "x": -44.54, "y": 48.93, "curve": [1.512, -19.02, 1.523, 2.67, 1.512, 19.3, 1.523, -5.89]}, {"time": 1.5333, "x": 17.98, "y": -23.66, "curve": [1.546, 34.61, 1.557, 44.31, 1.546, -21.45, 1.557, -20.16]}, {"time": 1.5667, "x": 44.31, "y": -20.16}]}, "R_hand_c_1": {"rotate": [{"curve": "stepped"}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333, "curve": [0.675, 0, 0.958, -24.92, 0.675, 0, 0.958, -34.84]}, {"time": 1.1, "x": -24.92, "y": -34.84}]}, "CHEST4": {"rotate": [{"curve": [0.011, 0, 0.022, -59.7]}, {"time": 0.0333, "value": -59.7}], "translate": [{"curve": [0.011, 0, 0.022, -146.58, 0.011, 0, 0.022, 90]}, {"time": 0.0333, "x": -146.58, "y": 90}]}, "tail base": {"rotate": [{"time": 0.2667}, {"time": 0.3667, "value": 79.87}], "translate": [{"time": 0.2}, {"time": 0.3667, "x": 162.32, "y": -17.4}, {"time": 0.6333, "x": 210.79, "y": -112.36}, {"time": 0.9, "x": 149.88, "y": -120.68}]}, "tail": {"translate": [{"x": 56.31, "y": 0.44}]}}, "transform": {"L_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0, "curve": "stepped"}, {"time": 0.4, "mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0, "curve": [0.408, 0, 0.425, 1, 0.408, 0, 0.425, 1, 0.408, 0, 0.425, 1, 0.408, 0, 0.425, 1, 0.408, 0, 0.425, 1, 0.408, 0, 0.425, 1]}, {"time": 0.4333}], "R_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0, "curve": "stepped"}, {"time": 0.4, "mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0, "curve": "stepped"}, {"time": 0.4333}]}, "path": {"L_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0, "curve": "stepped"}, {"time": 0.4333, "mixRotate": 0, "mixX": 0, "curve": [0.475, 0, 0.558, 1, 0.475, 0, 0.558, 1, 0.475, 0, 0.558, 1]}, {"time": 0.6}]}, "R_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0, "curve": "stepped"}, {"time": 0.4333, "mixRotate": 0, "mixX": 0, "curve": [0.475, 0, 0.558, 1, 0.475, 0, 0.558, 1, 0.475, 0, 0.558, 1]}, {"time": 0.6}]}}, "physics": {"tail11": {"damping": [{"value": 0.4222}]}, "tail12": {"inertia": [{"value": 0.0593}], "strength": [{"value": 174.8}], "damping": [{"value": 0.4296}]}, "tail13": {"damping": [{"value": 0.3407}]}, "tail14": {"inertia": [{"value": 0.0657}], "strength": [{"value": 51.1}], "mass": [{"value": 3.57}]}}, "attachments": {"default": {"head": {"monkey new/head": {"deform": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.056, 0, 0.082, 1]}, {"time": 0.1, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": "stepped"}, {"time": 0.2333, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": [0.291, 0, 0.278, 1]}, {"time": 0.3}]}}, "monkey_left_leg": {"monkey new/monkey_left_leg": {"deform": [{}, {"time": 0.1, "vertices": [-13.40687, -52.36029, -0.50662, -49.22044, 6.3984, -24.85311, 5.33003, -14.03821, 3.71574, -9.76614, 2.98617, -1.78102, 2.58978, 5.3951, -6.25747, -6.69809, -15.87528, -21.5849, -24.54563, -39.32415, -32.76144, -60.10643, -19.74691, -54.58646]}, {"time": 0.2333, "vertices": [-1.4496, -18.00388, 3.84256, -18.47542, 1.4734, -7.08415, 0.03148, -0.15125, 0, 0, 2.29037, 8.13844, 7.91014, 28.10814, 3.38986, 12.04535, -1.30252, -3.08656, -5.54512, -13.13844, -9.96735, -23.61607, -4.40623, -19.06728]}, {"time": 0.2667, "offset": 4, "vertices": [0.68105, 14.90239, 1.625, 36.92479, 2.27966, 52.48322, -0.81857, 53.20296, -8.10341, 66.25676, -4.67496, 35.91021, -0.73227, 6.13014, -0.21422, 1.7938], "curve": "stepped"}, {"time": 0.4, "offset": 4, "vertices": [0.68105, 14.90239, 1.625, 36.92479, 2.27966, 52.48322, -0.81857, 53.20296, -8.10341, 66.25676, -4.67496, 35.91021, -0.73227, 6.13014, -0.21422, 1.7938]}, {"time": 0.5333, "vertices": [-9.28165, -21.16048, -0.57788, -20.60906, 10.50199, -3.70072, 13.06048, 22.61693, 13.74415, 40.97543, 6.93404, 47.42292, -10.51802, 52.15215, -21.45366, 29.47612, -32.69991, 5.06342, -27.55743, -9.58116, -22.56758, -20.82003, -13.61938, -21.23687]}, {"time": 0.7, "vertices": [3.78595, -69.82477, 15.37387, -70.77399, 29.56361, -54.84156, 31.42569, -27.76601, 32.42975, -10.61725, 27.46597, -13.32567, 22.86089, -19.79195, 13.28629, -33.15687, 4.41196, -52.55797, -4.55357, -61.99392, -11.134, -72.2439, -1.0275, -70.87457]}]}}, "monkey_mouth smile open": {"monkey new/monkey_mouth smile open": {"deform": [{"time": 0.2333, "vertices": [-5.55189, -7.07478, -5.55185, -7.07478, -4.35552, -5.55023, -4.35548, -5.55024, -3.46231, -4.42029, -3.46218, -4.42033, 0.90805, -0.30172, 0.90813, -0.30176, 2.54797, -0.79051, 2.5479, -0.79054, 9.04847, -1.0213, 9.04851, -1.02133, 12.71387, -2.85299, 12.7138, -2.85303, 10.14859, -2.60541, 10.14843, -2.60549, 6.87895, -2.13435, 6.87895, -2.13438, 5.17223, -1.6048, 5.17223, -1.60484, 3.44617, -1.06927, 3.44627, -1.06929, 2.51061, -0.77896, 2.51071, -0.77899, -2.66301, 0.98673, -2.66295, 0.9867, -7.22143, 10.2304, -7.22132, 10.23033, -24.95152, -0.25155, -24.95142, -0.25156, -32.89366, -1.32526, -35.77647, -2.23217, -35.77638, -2.23221, -31.11521, -7.77677, -31.11515, -7.7768, -15.87508, -10.52334, -15.87505, -10.52336, -50.30355, -1.85506, -50.3035, -1.85511, -46.57721, 6.41006, -46.57722, 6.41001, -32.32117, 7.73134, -32.32124, 7.73127, -32.43951, 5.67157, -32.43942, 5.67148, -29.58077, 3.37934, -29.58068, 3.37929, -25.17789, 3.33142, -25.17785, 3.33136, -14.72821, -0.49309, -14.72821, -0.49316, 0.10563, -2.70719, 0.10558, -2.70728, 2.88609, 0.44156, 2.8859, 0.44147, 2.58172, -0.76257, 2.58166, -0.76261, 0.18557, -3.06002, 0.18537, -3.06007, -7.49387, -5.53229, -7.4941, -5.53237, -16.92147, -9.47656, -16.92155, -9.4766, -25.023, -15.17275, -25.02312, -15.17283, -30.49664, -20.35851, -30.49655, -20.35857, -35.31985, -13.41046, -35.31981, -13.41054, -45.51764, -7.46352, -45.51763, -7.46357], "curve": [0.244, 0, 0.256, 1]}, {"time": 0.2667, "vertices": [15.91335, -0.34447, 15.91332, -0.34443, 18.22592, -0.1557, 18.22594, -0.15565, 16.57591, -2.90336, 16.57592, -2.90325, 10.77671, 0.57695, 10.7766, 0.57704, -3.76192, 1.04626, -3.76199, 1.04635, -10.57374, -8.1484, -10.57376, -8.14831, 8.20661, -14.29271, 8.2066, -14.29259, 5.75364, -6.06075, 5.75362, -6.06064, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.74092, 10.54966, 1.74084, 10.54967, -11.94601, 7.10454, -11.94605, 7.10456, -11.7518, 1.36887, 0, 0, 0, 0, 0, 0, 0, 0, 9.15875, 0.19973, 9.15871, 0.19975, 0, 0, 0, 0, 0.44277, 0.04588, 0.44277, 0.04589, 2.88429, 0.78514, 2.88429, 0.7852, 0.7377, 1.49104, 0.73769, 1.49109, 0, 0, 0, 0, -12.18209, 11.42344, -12.1822, 11.42351, -15.46594, 17.1783, -15.46601, 17.17838, -21.98383, 18.76367, -21.98392, 18.7638, -31.71386, 17.60721, -31.71394, 17.60735, -38.91485, 12.22678, -38.91492, 12.22695, -42.88834, -7.36633, -42.8885, -7.36609, -38.58099, -20.30819, -38.58106, -20.30774, -29.23725, -24.03554, -29.23753, -24.03527, -15.42872, -23.46846, -15.429, -23.46821, -6.51517, -25.32203, -6.51546, -25.32186, -1.84745, -8.78892, -1.84754, -8.78885, -0.12933, -0.73415, -0.12939, -0.73405]}, {"time": 0.4, "vertices": [-4.91741, 0.15522, -4.91718, 0.15523, -11.29498, -0.58349, -11.2946, -0.58347, -10.30362, 1.6155, -10.30373, 1.61557, -11.73773, 5.72114, -11.73752, 5.72114, -3.76192, 1.04626, -3.76199, 1.04635, -11.37238, 5.02374, -11.3718, 5.02367, -4.51728, -12.80059, -4.51742, -12.80048, -4.39954, -3.52985, -4.3998, -3.52978, -7.19183, -4.35085, -7.19159, -4.35086, -10.4953, -3.59897, -10.49457, -3.59901, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.49216, 6.82455, -0.49204, 6.82456, 6.05471, -5.03791, 6.05554, -5.03799, -3.16608, -10.0949, 5.31104, -1.79663, 5.31122, -1.79666, 0, 0, 0, 0, -3.75349, 3.74315, -3.75346, 3.74315, 8.44879, -8.44461, 8.44934, -8.44464, 7.7206, -7.43725, 7.72097, -7.43728, 6.3141, -2.93557, 6.31441, -2.93554, 0.7377, 1.49104, 0.73769, 1.49109, 0, 0, 0, 0, -11.16959, 5.47064, -11.16949, 5.4707, -9.41956, 3.3593, -9.41858, 3.35938, -2.82971, 2.63268, -2.82856, 2.63275, 3.85038, 4.53879, 3.8513, 4.53854, 4.55092, 8.87539, 4.55098, 8.87552, -4.40866, -4.30678, -4.40869, -4.3066, -6.33725, -11.43473, -6.33676, -11.43435, -8.04655, -16.88143, -8.04522, -16.88125, 0.32996, -25.65286, 0.33148, -25.65265, 17.83701, -33.54081, 17.83783, -33.54073, 10.86459, -8.36724, 10.86492, -8.36722, 7.90137, -9.13702, 7.90199, -9.13695]}, {"time": 0.8, "vertices": [-4.91741, 0.15522, -4.91718, 0.15523, -9.28442, -8.56616, -9.28434, -8.56626, -15.02395, 3.0079, -15.02389, 3.00803, -11.73773, 5.72114, -11.73752, 5.72114, -3.76192, 1.04626, -3.76199, 1.04635, -13.52332, 7.61694, -13.52377, 7.61712, -4.51728, -12.80059, -4.51742, -12.80048, -4.39954, -3.52985, -4.3998, -3.52978, -11.54517, -1.06496, -11.54517, -1.06473, -19.01886, -2.10635, -19.01849, -2.10609, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.49216, 6.82455, -0.49204, 6.82456, 6.05471, -5.03791, 6.05554, -5.03799, 7.73571, -17.67724, 9.07336, -3.48236, 9.07269, -3.48227, 14.02118, -9.56757, 14.01978, -9.56763, -6.59743, 4.13476, -6.59739, 4.1353, 8.44879, -8.44461, 8.44934, -8.44464, 7.7206, -7.43725, 7.72097, -7.43728, 6.3141, -2.93557, 6.31441, -2.93554, 0.7377, 1.49104, 0.73769, 1.49109, 0, 0, 0, 0, -7.14096, 1.87585, -7.14142, 1.87585, -4.11469, 0.04796, -4.11438, 0.04791, 3.70935, -1.97993, 3.70995, -1.9801, 13.2504, 0.16794, 13.25059, 0.16745, 17.72084, 3.13662, 17.72017, 3.13657, 15.79513, -8.37166, 15.79486, -8.37209, 11.69583, -13.17887, 11.69461, -13.17849, 4.94893, -17.22426, 4.94873, -17.22408, 6.70288, -24.55239, 6.70343, -24.55219, 17.83701, -33.54081, 17.83783, -33.54073, 11.79818, -21.07769, 11.79882, -21.07822, 9.31904, -15.51733, 9.31971, -15.5175]}, {"time": 1}]}}, "monkey_right_leg": {"monkey new/monkey_right_leg": {"deform": [{"time": 0.2333, "vertices": [-2.43999, -1.71704, -1.58516, -1.11551, -1.37755, -0.96941, -0.32104, -0.22594, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.30786, -0.21669, -1.68904, -1.1886, -3.14632, -2.21407]}, {"time": 0.2667, "vertices": [31.51755, -36.81594, 19.29347, -37.85529, 14.36983, -36.47663, -3.70587, -33.35455, -17.78639, 9.3475, -12.92915, 13.86443, -11.5575, 13.43642, -6.52536, 0.74619, -6.77695, -9.63097, -7.94814, -14.80956, -10.27196, -24.69295, 12.47838, -34.3573, 29.70331, -31.60104, 45.49908, -41.92441]}, {"time": 0.4, "vertices": [48.0676, -71.70571, 32.78468, -77.76864, 26.70924, -72.98161, 3.32715, -54.1617, -15.36964, 2.19827, -12.92915, 13.86443, -11.5575, 13.43642, -6.52536, 0.74619, -6.77695, -9.63097, -7.94814, -14.80956, -8.33946, -26.57986, 26.64989, -48.19525, 57.96216, -64.37473, 65.63007, -75.20671]}, {"time": 0.6, "vertices": [11.78189, -62.56119, -2.90013, -55.57398, -9.40227, -48.26672, -21.34912, -39.5004, -31.23643, 2.56723, -21.31636, 4.97112, -18.20923, 2.63653, -11.49841, -12.53946, -12.13422, -27.53562, -13.92879, -35.36185, -15.99339, -34.93912, 17.93047, -52.80893, 35.24805, -62.36166, 29.37387, -72.00914]}, {"time": 0.8, "vertices": [21.08298, -119.85564, 3.57509, -129.8432, -3.41527, -126.65659, -22.96106, -107.57579, -40.34145, -39.01498, -27.69563, -5.04536, -23.95056, 2.12508, -15.4512, 6.19345, -15.785, 7.09953, -17.86263, 7.10381, -22.18358, 1.76535, 18.23759, -37.89758, 43.70007, -86.12946, 42.62042, -120.71318]}, {"time": 1, "vertices": [13.40726, -115.47301, -12.6884, -120.55725, -21.68401, -116.2255, -48.67365, -92.89452, -56.86275, -29.582, -37.33754, 0.45964, -31.41565, 6.38686, -15.88181, 6.43881, -15.785, 7.09953, -17.86263, 7.10381, -22.18358, 1.76535, 18.23759, -37.89758, 43.70007, -86.12946, 39.02576, -118.66118]}]}}}}, "drawOrder": [{}, {"time": 0.6333}]}, "Get_banana": {"slots": {"banana_2_1": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_2_1"}]}, "banana_2_2": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_2_2"}]}, "banana_3_2": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_3_2"}]}, "banana_3_3": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_3_3"}]}, "closed_eye_left": {"attachment": [{"time": 0.0667, "name": "monkey new/closed_eye_left"}, {"time": 0.2333}, {"time": 1.3333, "name": "monkey new/closed_eye_left"}, {"time": 1.8667}]}, "closed_eye_right": {"attachment": [{"time": 0.0667, "name": "monkey new/closed_eye_right"}, {"time": 0.2333}, {"time": 1.3333, "name": "monkey new/closed_eye_right"}, {"time": 1.8667}]}, "monkeyOLD_banana_1": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_1"}]}, "monkeyOLD_banana_3_1": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_3_1"}]}, "monkey_eyebrow left": {"attachment": [{}, {"time": 0.0667, "name": "monkey new/monkey_eyebrow left"}]}, "monkey_eyebrow right": {"attachment": [{}, {"time": 0.0667, "name": "monkey new/monkey_eyebrow right"}]}, "monkey_eyebrow_angry": {"attachment": [{"name": "monkey new/monkey_eyebrow_angry"}, {"time": 0.0667}]}, "monkey_hand left": {"attachment": [{}, {"time": 0.4333, "name": "monkey new/monkey_hand left"}]}, "monkey_hand right": {"attachment": [{}, {"time": 0.2667, "name": "monkey new/monkey_hand right"}]}, "monkey_left_arm": {"attachment": [{"time": 0.4333}]}, "monkey_left_hand": {"attachment": [{}, {"time": 0.4333}]}, "monkey_left_hand closed": {"attachment": [{"name": "monkey new/monkey_left_hand closed"}, {"time": 0.4333}]}, "monkey_mouth angry": {"attachment": [{"time": 0.0667}]}, "monkey_mouth o": {"attachment": [{}]}, "monkey_mouth smile closed": {"attachment": [{}, {"time": 0.0667, "name": "monkey new/monkey_mouth smile closed"}, {"time": 0.4}, {"time": 1.4, "name": "monkey new/monkey_mouth smile closed"}]}, "monkey_mouth smile open": {"attachment": [{}, {"time": 0.4, "name": "monkey new/monkey_mouth smile open"}, {"time": 1.4}]}, "monkey_right_hand": {"attachment": [{}]}}, "bones": {"Banana": {"rotate": [{"value": -9.88, "curve": [0.049, -3.04, 0.112, 12.42]}, {"time": 0.1667, "value": 12.42, "curve": [0.351, 12.42, 0.503, 5.72]}, {"time": 0.5667, "value": 3.22, "curve": [0.59, 2.31, 0.696, -4.24]}, {"time": 0.8, "value": -4.24, "curve": [1.154, -4.24, 1.224, 4.68]}, {"time": 1.5667, "value": 4.68, "curve": [1.753, 4.68, 1.945, 2.03]}, {"time": 2.0667, "value": 0.03}], "translate": [{"x": -117.6, "y": 364.94, "curve": [0.098, -53.73, 0.223, 90.73, 0.098, 244.13, 0.223, -29.14]}, {"time": 0.3333, "x": 90.73, "y": -29.14, "curve": [0.487, 90.73, 0.518, -11.73, 0.487, -29.14, 0.518, 2.09]}, {"time": 0.6667, "x": -11.73, "y": 2.09, "curve": [1.082, -11.73, 1.164, 30.64, 1.082, 2.09, 1.164, -30.22]}, {"time": 1.5667, "x": 30.64, "y": -30.22, "curve": [1.753, 30.64, 1.945, 16.84, 1.753, -30.22, 1.945, -17.05]}, {"time": 2.0667, "x": 6.4, "y": -7.09}], "scale": [{"x": 0.135, "y": 0.135, "curve": [0.05, 0.135, 0.15, 1, 0.05, 0.135, 0.15, 1]}, {"time": 0.2}]}, "bone": {"rotate": [{"curve": [0.133, 0, 0.267, -69.41]}, {"time": 0.4, "value": -69.41}]}, "L_hand_5": {"rotate": [{"value": -45.28, "curve": [0.05, -45.28, 0.15, -15.8]}, {"time": 0.2, "value": -15.8, "curve": [0.257, -15.8, 0.304, -75.81]}, {"time": 0.4, "value": -89.82, "curve": [0.444, -96.11, 0.498, -63.27]}, {"time": 0.5667, "value": -63.27, "curve": [0.6, -63.27, 0.667, -59.56]}, {"time": 0.7, "value": -59.56, "curve": [0.775, -59.56, 0.925, -63.27]}, {"time": 1, "value": -63.27, "curve": [1.076, -63.27, 1.14, -63.13]}, {"time": 1.2, "value": -62.94, "curve": [1.283, -62.94, 1.45, -64.68]}, {"time": 1.5333, "value": -64.68, "curve": [1.802, -64.68, 1.735, -62.17]}, {"time": 2.0667, "value": -62.17}], "translate": [{"x": -7.36, "y": -2.79, "curve": [0.05, -7.36, 0.15, -17.21, 0.05, -2.79, 0.15, -6.36]}, {"time": 0.2, "x": -17.21, "y": -6.36, "curve": [0.257, -17.21, 0.304, 4.94, 0.257, -6.36, 0.304, 5.11]}, {"time": 0.4, "x": 8.05, "y": 1.29, "curve": [0.444, 9.44, 0.498, -6.68, 0.444, -0.43, 0.498, -19.31]}, {"time": 0.5667, "x": -6.68, "y": -19.31, "curve": "stepped"}, {"time": 1, "x": -6.68, "y": -19.31, "curve": [1.076, -6.68, 1.14, -5.92, 1.076, -19.31, 1.14, -18.31]}, {"time": 1.2, "x": -4.91, "y": -16.99, "curve": [1.283, -4.91, 1.45, -3.18, 1.283, -16.99, 1.45, -14.72]}, {"time": 1.5333, "x": -3.18, "y": -14.72, "curve": [1.802, -3.18, 1.735, -0.79, 1.802, -14.72, 1.735, -11.59]}, {"time": 2.0667, "x": -0.79, "y": -11.59}]}, "L_hand_6": {"rotate": [{"value": -32.04, "curve": [0.058, -32.04, 0.143, -26.4]}, {"time": 0.2333, "value": -26.4, "curve": [0.308, -26.4, 0.365, -74.42]}, {"time": 0.5667, "value": -74.42, "curve": [0.6, -74.42, 0.667, -63.32]}, {"time": 0.7, "value": -63.32, "curve": [0.775, -63.32, 0.925, -60.55]}, {"time": 1, "value": -60.55, "curve": [1.076, -60.55, 1.14, -59.95]}, {"time": 1.2, "value": -59.16, "curve": [1.283, -59.16, 1.45, -41.34]}, {"time": 1.5333, "value": -41.34, "curve": [1.802, -41.34, 1.735, -55.93]}, {"time": 2.0667, "value": -55.93}], "translate": [{"x": 7.36, "y": 5.37, "curve": [0.058, 7.36, 0.143, -3, 0.058, 5.37, 0.143, -6.27]}, {"time": 0.2333, "x": -3, "y": -6.27, "curve": [0.308, -3, 0.365, 2.31, 0.308, -6.27, 0.365, -34.91]}, {"time": 0.5667, "x": 2.31, "y": -34.91, "curve": [0.675, 2.31, 0.892, -29.42, 0.675, -34.91, 0.892, -33.66]}, {"time": 1, "x": -29.42, "y": -33.66, "curve": [1.076, -29.42, 1.14, -29.71, 1.076, -33.66, 1.14, -32.53]}, {"time": 1.2, "x": -30.1, "y": -31.06, "curve": [1.283, -30.1, 1.45, -30.77, 1.283, -31.06, 1.45, -28.51]}, {"time": 1.5333, "x": -30.77, "y": -28.51, "curve": [1.802, -30.77, 1.735, -31.68, 1.802, -28.51, 1.735, -25]}, {"time": 2.0667, "x": -31.68, "y": -25}]}, "L_hand_4": {"rotate": [{"value": -29.17, "curve": [0.058, -29.17, 0.143, -35.53]}, {"time": 0.2333, "value": -35.53, "curve": [0.282, -35.53, 0.323, -17.53]}, {"time": 0.4, "value": -11.56, "curve": [0.443, -8.34, 0.496, -15.34]}, {"time": 0.5667, "value": -15.34, "curve": [0.6, -15.34, 0.667, -32.53]}, {"time": 0.7, "value": -32.53, "curve": [0.775, -32.53, 0.925, -30.72]}, {"time": 1, "value": -30.72, "curve": [1.076, -30.72, 1.14, -31.03]}, {"time": 1.2, "value": -31.44, "curve": [1.283, -31.44, 1.45, 0.71]}, {"time": 1.5333, "value": 0.71, "curve": [1.802, 0.71, 1.735, -33.11]}, {"time": 2.0667, "value": -33.11}], "translate": [{"x": -11.86, "y": -18.15, "curve": [0.058, -11.86, 0.143, -7.9, 0.058, -18.15, 0.143, -15.7]}, {"time": 0.2333, "x": -7.9, "y": -15.7, "curve": [0.282, -7.9, 0.323, -27.84, 0.282, -15.7, 0.323, -33.73]}, {"time": 0.4, "x": -21.29, "y": -43.87, "curve": [0.443, -17.77, 0.496, 14.24, 0.443, -49.33, 0.496, -49.98]}, {"time": 0.5667, "x": 14.24, "y": -49.98, "curve": "stepped"}, {"time": 1, "x": 14.24, "y": -49.98, "curve": [1.076, 14.24, 1.14, 14.35, 1.076, -49.98, 1.14, -49.75]}, {"time": 1.2, "x": 14.49, "y": -49.44, "curve": [1.283, 14.49, 1.45, 14.69, 1.283, -49.44, 1.45, -51.67]}, {"time": 1.5333, "x": 14.69, "y": -51.67, "curve": [1.802, 14.69, 1.735, 14.95, 1.802, -51.67, 1.735, -54.65]}, {"time": 2.0667, "x": 14.95, "y": -54.65}]}, "L_arm_2": {"rotate": [{"value": 159.21, "curve": [0.033, 159.21, 0.081, 28.81]}, {"time": 0.1333, "value": 28.81, "curve": [0.232, 28.81, 0.227, 138.83]}, {"time": 0.4, "value": 138.83, "curve": [0.432, 138.83, 0.497, 150.93]}, {"time": 0.5333, "value": 150.93, "curve": "stepped"}, {"time": 0.5667, "value": 140.49, "curve": [0.647, 140.49, 0.892, 134.98]}, {"time": 1, "value": 134.98, "curve": [1.076, 134.98, 1.14, 134.61]}, {"time": 1.2, "value": 134.12, "curve": [1.283, 134.12, 1.45, 133.26]}, {"time": 1.5333, "value": 133.26, "curve": [1.802, 133.26, 1.735, 132.11]}, {"time": 2.0667, "value": 132.11}], "translate": [{"x": -47.91, "y": 8.61, "curve": [0.033, -47.91, 0.081, -8.34, 0.033, 8.61, 0.081, 37.84]}, {"time": 0.1333, "x": -8.34, "y": 37.84, "curve": [0.232, -8.34, 0.227, -69.17, 0.232, 37.84, 0.227, 33.99]}, {"time": 0.4, "x": -69.17, "y": 33.99, "curve": [0.442, -69.17, 0.497, -68.79, 0.442, 33.99, 0.497, 33.41]}, {"time": 0.5333, "x": -68.79, "y": 33.41, "curve": "stepped"}, {"time": 0.5667, "x": -68.55, "y": 33.04, "curve": [0.684, -68.55, 0.953, -62.41, 0.684, 33.04, 0.953, 23.6]}, {"time": 1.2, "x": -57.78, "y": 16.47, "curve": [1.283, -57.78, 1.45, -55.07, 1.283, 16.47, 1.45, 12.3]}, {"time": 1.5333, "x": -55.07, "y": 12.3, "curve": [1.802, -55.07, 1.735, -52.74, 1.802, 12.3, 1.735, 8.72]}, {"time": 2.0667, "x": -52.74, "y": 8.72}]}, "R_leg2": {"rotate": [{"value": 6.23, "curve": [0.04, 6.23, 0.095, -0.03]}, {"time": 0.2333, "curve": [0.359, 0, 0.481, 5.65]}, {"time": 0.6, "value": 13.99, "curve": [0.916, 13.99, 1.181, -4.6]}, {"time": 1.5, "value": -4.6, "curve": [1.699, -4.6, 1.941, 5.75]}, {"time": 2.0667, "value": 5.75}], "translate": [{"x": -14.79, "y": 0.39, "curve": [0.04, -14.79, 0.095, 0.07, 0.04, 0.39, 0.095, 0]}, {"time": 0.2333, "curve": [0.675, 0, 1.053, -1.72, 0.675, 0, 1.053, 1.72]}, {"time": 1.5, "x": -1.72, "y": 1.72, "curve": [1.699, -1.72, 1.941, -0.6, 1.699, 1.72, 1.941, 0.6]}, {"time": 2.0667, "x": -0.6, "y": 0.6}]}, "R_hand_3": {"rotate": [{"value": -21.91, "curve": [0.078, -25.47, 0.156, -29.23]}, {"time": 0.2333, "value": -32.58, "curve": [0.344, -37.38, 0.456, -17.35]}, {"time": 0.5667, "value": -17.35, "curve": [0.656, -17.35, 0.744, -2.05]}, {"time": 0.8333, "value": -2.05, "curve": "stepped"}, {"time": 2.0667, "value": -2.05}], "translate": [{"x": -2.72, "y": -0.35, "curve": [0.078, -7.75, 0.156, -17.81, 0.078, -0.42, 0.156, -0.55]}, {"time": 0.2333, "x": -17.81, "y": -0.55, "curve": [0.344, -17.81, 0.456, -5.01, 0.344, -0.55, 0.456, 2.53]}, {"time": 0.5667, "x": -5.01, "y": 2.53, "curve": "stepped"}, {"time": 0.8333, "x": -5.01, "y": 2.53, "curve": "stepped"}, {"time": 2.0667, "x": -5.01, "y": 2.53}]}, "face_v": {"rotate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 1.5667}], "translate": [{"x": -16.92, "curve": [0.05, -16.92, 0.15, 61.94, 0.05, 0, 0.15, 3.64]}, {"time": 0.2, "x": 61.94, "y": 3.64, "curve": [0.231, 61.94, 0.293, -10.46, 0.231, 3.64, 0.293, -5.55]}, {"time": 0.3667, "x": -10.46, "y": -5.55, "curve": [0.551, -10.46, 0.569, 40.96, 0.551, -5.55, 0.569, -5.55]}, {"time": 0.7333, "x": 40.96, "y": -5.55, "curve": [0.985, 40.96, 1.009, 54.58, 0.985, -5.55, 1.009, 0]}, {"time": 1.2333, "x": 54.58, "curve": [1.345, 54.58, 1.506, 89.41, 1.345, 0, 1.506, 0]}, {"time": 1.6, "x": 89.41, "curve": [1.763, 89.41, 1.997, 23.65, 1.763, 0, 1.997, 0]}, {"time": 2.1333, "x": 23.65}]}, "CHEST": {"rotate": [{"value": -10.79, "curve": [0.068, -10.79, 0.117, -16.34]}, {"time": 0.2667, "value": -16.34, "curve": [0.425, -16.34, 0.434, 2.21]}, {"time": 0.6667, "value": 2.21, "curve": [0.891, 2.21, 1.004, -5.71]}, {"time": 1.2333, "value": -5.71, "curve": [1.563, -5.71, 1.729, 1.95]}, {"time": 2.0667, "value": 1.95}], "translate": [{"x": 31.9, "y": -15.22, "curve": [0.05, 31.9, 0.15, -44.29, 0.05, -15.22, 0.15, 6.51]}, {"time": 0.2, "x": -44.29, "y": 6.51, "curve": [0.29, -44.29, 0.324, 34.85, 0.29, 6.51, 0.324, -5.58]}, {"time": 0.5667, "x": 34.85, "y": -5.58, "curve": [1.256, 34.85, 1.249, -4.82, 1.256, -5.58, 1.249, 0.88]}, {"time": 1.5, "x": -4.82, "y": 0.88, "curve": [1.799, -4.82, 1.763, 0, 1.799, 0.88, 1.763, 0]}, {"time": 2.0667}]}, "SPINE": {"rotate": [{"value": -2.99}], "translate": [{}]}, "R_leg": {"rotate": [{"value": 5.18, "curve": [0.069, 5.18, 0.101, 0]}, {"time": 0.2333, "curve": [0.35, 0, 0.473, -7.24]}, {"time": 0.6, "value": -17.92, "curve": [0.916, -17.92, 1.211, 1.25]}, {"time": 1.5, "value": 1.25, "curve": [1.699, 1.25, 1.941, -3.22]}, {"time": 2.0667, "value": -3.22}], "translate": [{"x": 43.98, "y": 0.39, "curve": [0.069, 43.98, 0.101, 0, 0.069, 0.39, 0.101, 0]}, {"time": 0.2333, "curve": [0.675, 0, 1.053, 13.79, 0.675, 0, 1.053, -1.72]}, {"time": 1.5, "x": 13.79, "y": -1.72, "curve": [1.699, 13.79, 1.941, 4.77, 1.699, -1.72, 1.941, -0.6]}, {"time": 2.0667, "x": 4.77, "y": -0.6}]}, "R_brow": {"rotate": [{"value": 26.19, "curve": [0.278, 17.46, 0.556, 0]}, {"time": 0.8333, "curve": "stepped"}, {"time": 2.0667}], "translate": [{"x": -141.01, "y": 30.25, "curve": [0.022, -107.91, 0.044, -56.75, 0.022, 32.15, 0.044, 35.95]}, {"time": 0.0667, "x": -41.73, "y": 35.95, "curve": [0.111, -11.67, 0.156, -17.26, 0.111, 35.95, 0.156, 32.13]}, {"time": 0.2, "x": -5.76, "y": 22.44, "curve": [0.256, 8.62, 0.311, 30.92, 0.256, 10.31, 0.311, -29.5]}, {"time": 0.3667, "x": 35.89, "y": -29.5, "curve": [0.522, 49.8, 0.678, 46.32, 0.522, -29.5, 0.678, -18.34]}, {"time": 0.8333, "x": 50.87, "y": -11.27, "curve": [0.967, 54.76, 1.1, 61.22, 0.967, -5.22, 1.1, 9.86]}, {"time": 1.2333, "x": 61.22, "y": 9.86, "curve": [1.311, 61.22, 1.389, 39.59, 1.311, 9.86, 1.389, -9.38]}, {"time": 1.4667, "x": 39.59, "y": -14.24, "curve": [1.544, 39.59, 1.622, 52.14, 1.544, -19.11, 1.622, -19.31]}, {"time": 1.7, "x": 52.14, "y": -19.31, "curve": [1.822, 52.14, 1.944, 22.21, 1.822, -19.31, 1.944, -7.07]}, {"time": 2.0667, "x": 7.25, "y": -0.95}]}, "R_hand_1": {"rotate": [{"value": 36.21, "curve": [0.078, 44.98, 0.156, 62.51]}, {"time": 0.2333, "value": 62.51, "curve": [0.344, 62.51, 0.456, 16.36]}, {"time": 0.5667, "value": 16.36, "curve": [0.656, 16.36, 0.744, 22.68]}, {"time": 0.8333, "value": 22.68, "curve": [1.244, 22.68, 1.656, 12.87]}, {"time": 2.0667, "value": 7.97}], "translate": [{"x": 34.02, "y": 38.94, "curve": [0.078, 20.34, 0.156, -7.03, 0.078, 32.57, 0.156, 19.84]}, {"time": 0.2333, "x": -7.03, "y": 19.84, "curve": [0.344, -7.03, 0.456, 20.17, 0.344, 19.84, 0.456, 42.38]}, {"time": 0.5667, "x": 20.17, "y": 42.38, "curve": [0.656, 20.17, 0.744, 20.17, 0.656, 42.38, 0.744, 42.38]}, {"time": 0.8333, "x": 19.94, "y": 40.31, "curve": [1.244, 18.89, 1.656, 9, 1.244, 30.79, 1.656, 14.14]}, {"time": 2.0667, "x": 3.53, "y": 1.05}]}, "HEAD": {"rotate": [{"value": -2.19, "curve": [0.094, -2.19, 0.161, 6.3]}, {"time": 0.3667, "value": 6.3, "curve": [0.507, 6.3, 0.567, 13.36]}, {"time": 0.7, "value": 13.36, "curve": [0.897, 13.36, 0.98, 6.36]}, {"time": 1.1667, "value": 6.36, "curve": [1.335, 6.36, 1.407, -18.03]}, {"time": 1.5667, "value": -18.03, "curve": [2.041, -18.03, 2.017, -2.12]}, {"time": 2.2333, "value": -2.12}], "translate": [{"x": -8.48, "y": -3.57, "curve": [0.068, -8.48, 0.117, -11.83, 0.068, -3.57, 0.117, -1.08]}, {"time": 0.2667, "x": -11.83, "y": -1.08, "curve": [0.358, -11.83, 0.497, 33.83, 0.358, -1.08, 0.497, 1.07]}, {"time": 0.6333, "x": 33.6, "y": 1.06, "curve": [0.742, 33.6, 0.958, 10.28, 0.742, 1.06, 0.958, -1.51]}, {"time": 1.0667, "x": 10.28, "y": -1.51, "curve": [1.192, 10.28, 1.442, -44.53, 1.192, -1.51, 1.442, -9.74]}, {"time": 1.5667, "x": -44.53, "y": -9.74, "curve": [2.041, -44.53, 2.017, 15.46, 2.041, -9.74, 2.017, -8.02]}, {"time": 2.2333, "x": 15.46, "y": -8.02}]}, "R_arm_1": {"rotate": [{"value": 59.48, "curve": [0.078, 48.6, 0.156, 26.86]}, {"time": 0.2333, "value": 26.86, "curve": [0.344, 26.86, 0.456, 16.93]}, {"time": 0.5667, "value": 16.93, "curve": [0.611, 16.93, 0.656, 17.68]}, {"time": 0.7, "value": 14.97, "curve": [0.745, 12.29, 0.789, 6.14]}, {"time": 0.8333, "value": 6.14, "curve": [1.244, 6.14, 1.656, 6.14]}, {"time": 2.0667, "value": 6.14}], "translate": [{"x": 31.23, "y": 84.36, "curve": [0.033, 29.63, 0.067, 31.23, 0.033, 86.57, 0.067, 90.99]}, {"time": 0.1, "x": 26.42, "y": 90.99, "curve": [0.144, 20.01, 0.189, -20.13, 0.144, 90.99, 0.189, 15.64]}, {"time": 0.2333, "x": -20.13, "y": 6.65, "curve": [0.244, -20.13, 0.256, 2.59, 0.244, 4.4, 0.256, 4.4]}, {"time": 0.2667, "x": 2.59, "y": 4.4, "curve": [0.367, 2.59, 0.467, 23.19, 0.367, 4.4, 0.467, 15.13]}, {"time": 0.5667, "x": 23.19, "y": 15.13, "curve": [0.611, 23.19, 0.656, 16.97, 0.611, 15.13, 0.656, 31.63]}, {"time": 0.7, "x": 30.98, "y": 4.26, "curve": [0.745, 44.81, 0.789, 34.98, 0.745, -22.74, 0.789, -20.89]}, {"time": 0.8333, "x": 34.98, "y": -20.89, "curve": [1.244, 34.98, 1.656, 4.27, 1.244, -20.89, 1.656, -5.18]}, {"time": 2.0667}]}, "L_brow": {"rotate": [{"value": -23.5, "curve": [0.278, -15.67, 0.556, 0]}, {"time": 0.8333, "curve": "stepped"}, {"time": 2.0667}], "translate": [{"x": -124.92, "y": -28.88, "curve": [0.022, -107.28, 0.044, -81.75, 0.022, -23.25, 0.044, -11.99]}, {"time": 0.0667, "x": -72.01, "y": -11.99, "curve": [0.167, -28.15, 0.267, 26.26, 0.167, -11.99, 0.267, -29.5]}, {"time": 0.3667, "x": 35.89, "y": -29.5, "curve": [0.522, 50.87, 0.678, 46.32, 0.522, -29.5, 0.678, -20.05]}, {"time": 0.8333, "x": 50.87, "y": -11.27, "curve": [0.967, 54.76, 1.1, 61.22, 0.967, -3.75, 1.1, 19.41]}, {"time": 1.2333, "x": 61.22, "y": 19.41, "curve": [1.311, 61.22, 1.389, 39.59, 1.311, 19.41, 1.389, -21.04]}, {"time": 1.4667, "x": 39.59, "y": -21.04, "curve": [1.544, 39.59, 1.622, 52.14, 1.544, -21.04, 1.622, -9.53]}, {"time": 1.7, "x": 52.14, "y": -6.92, "curve": [1.822, 52.14, 1.944, 22.21, 1.822, -2.83, 1.944, -2.94]}, {"time": 2.0667, "x": 7.25, "y": -0.95}]}, "face_h": {"rotate": [{"curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.5667}], "translate": [{"x": 0.37, "y": 7.36, "curve": [0.133, 0.37, 0.4, 0, 0.133, 7.36, 0.4, -42.55]}, {"time": 0.5333, "y": -42.55, "curve": [0.683, 0, 0.983, 0, 0.683, -42.55, 0.983, 16.68]}, {"time": 1.1333, "y": 16.68, "curve": [1.227, 0, 1.32, 0, 1.227, 16.68, 1.32, -27.98]}, {"time": 1.5333, "y": -27.98, "curve": [1.596, 0, 1.658, 0, 1.596, -27.98, 1.658, 0]}, {"time": 1.8}]}, "R_eye": {"rotate": [{"curve": "stepped"}, {"time": 1.3}], "translate": [{"x": -0.26, "y": -2.52, "curve": [0.044, 1.43, 0.089, 4.83, 0.044, -0.95, 0.089, 0.52]}, {"time": 0.1333, "x": 4.83, "y": 2.19, "curve": [0.222, 4.83, 0.311, -11.13, 0.222, 5.55, 0.311, 11.6]}, {"time": 0.4, "x": -17.17, "y": 12.57, "curve": [0.456, -20.94, 0.511, -24.59, 0.456, 13.18, 0.511, 13.18]}, {"time": 0.5667, "x": -24.59, "y": 13.18, "curve": [0.634, -24.59, 0.701, -36.96, 0.634, 13.18, 0.701, 8.22]}, {"time": 0.7667, "x": -38.29, "y": 6.53, "curve": [0.901, -40.92, 1.034, -32.96, 0.901, 3.19, 1.034, 1.93]}, {"time": 1.1667, "x": -28.87, "y": 1.93, "curve": [1.211, -27.5, 1.256, -7.29, 1.211, 1.93, 1.256, -0.84]}, {"time": 1.3, "x": -1.96, "y": 0.41}]}, "L_eye": {"rotate": [{"curve": "stepped"}, {"time": 1.3}], "translate": [{"x": -0.26, "y": -2.52, "curve": [0.044, 1.43, 0.089, 4.83, 0.044, -0.95, 0.089, 0.52]}, {"time": 0.1333, "x": 4.83, "y": 2.19, "curve": [0.222, 4.83, 0.311, -11.13, 0.222, 5.55, 0.311, 11.6]}, {"time": 0.4, "x": -17.17, "y": 12.57, "curve": [0.456, -20.94, 0.511, -24.59, 0.456, 13.18, 0.511, 13.18]}, {"time": 0.5667, "x": -24.59, "y": 13.18, "curve": [0.634, -24.59, 0.701, -36.96, 0.634, 13.18, 0.701, 8.22]}, {"time": 0.7667, "x": -38.29, "y": 6.53, "curve": [0.901, -40.92, 1.034, -32.96, 0.901, 3.19, 1.034, 1.93]}, {"time": 1.1667, "x": -28.87, "y": 1.93, "curve": [1.211, -27.5, 1.256, -7.29, 1.211, 1.93, 1.256, -0.84]}, {"time": 1.3, "x": -1.96, "y": 0.41}]}, "R_hand_2": {"rotate": [{"value": -38.99, "curve": [0.078, -35.89, 0.156, -32.66]}, {"time": 0.2333, "value": -29.7, "curve": [0.344, -25.47, 0.456, -21.47]}, {"time": 0.5667, "value": -17.4, "curve": [0.656, -14.15, 0.744, -7.76]}, {"time": 0.8333, "value": -7.76, "curve": "stepped"}, {"time": 2.0667, "value": -7.76}], "translate": [{"x": -16.67, "y": 4.21, "curve": [0.078, -20.38, 0.156, -27.81, 0.078, 2.16, 0.156, -1.94]}, {"time": 0.2333, "x": -27.81, "y": -1.94, "curve": [0.344, -27.81, 0.456, 0, 0.344, -1.94, 0.456, 0]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 2.0667}]}, "R_foot": {"rotate": [{"value": 8.67, "curve": [0.042, 8.67, 0.125, -2.69]}, {"time": 0.1667, "value": -2.69, "curve": [0.66, -2.69, 1.134, 20.62]}, {"time": 1.6, "value": 20.62, "curve": [1.759, 20.62, 1.914, 10.4]}, {"time": 2.0667, "value": 3.59}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1.6, "curve": "stepped"}, {"time": 2.0667}]}, "R_foot_finger_5": {"rotate": [{"value": -8.81, "curve": [0.23, -8.81, 0.443, -4.52]}, {"time": 0.6333, "value": -4.54, "curve": "stepped"}, {"time": 1.3, "value": -4.54, "curve": [1.358, -4.54, 1.475, 19.23]}, {"time": 1.5333, "value": 19.23, "curve": "stepped"}, {"time": 1.7667, "value": 19.23, "curve": [1.86, 16.06, 1.957, 9.43]}, {"time": 2.0667, "value": 9.43}], "translate": [{"curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 1.3, "curve": "stepped"}, {"time": 2.0667}]}, "L_fingeL_6": {"rotate": [{"value": -9.04, "curve": [0.085, -8.62, 0.176, -8.09]}, {"time": 0.2333, "value": -8.09, "curve": [0.325, -5.12, 0.415, -2.33]}, {"time": 0.5, "curve": [0.617, 0, 0.85, -21.58]}, {"time": 0.9667, "value": -21.58}], "translate": [{"x": 4.69, "y": -22.38, "curve": [0.058, 4.69, 0.175, 0, 0.058, -22.38, 0.175, 0]}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.5}]}, "R_finger_2": {"rotate": [{"value": 10.15, "curve": [0.058, 10.15, 0.175, -7.42]}, {"time": 0.2333, "value": -7.42, "curve": [0.321, -5.09, 0.41, -2.61]}, {"time": 0.5, "curve": [0.617, 0, 0.85, 8.89]}, {"time": 0.9667, "value": 8.89}], "translate": [{"x": -12.17, "y": -20.15, "curve": [0.058, -12.17, 0.175, 0, 0.058, -20.15, 0.175, 0]}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.5}]}, "R_foot2": {"rotate": [{"value": 11.26, "curve": [0.042, 11.26, 0.125, -9.05]}, {"time": 0.1667, "value": -9.05, "curve": [0.66, -9.05, 1.134, 16.05]}, {"time": 1.6, "value": 16.05, "curve": [1.759, 16.05, 1.914, 5.05]}, {"time": 2.0667, "value": -2.29}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1.6, "curve": "stepped"}, {"time": 2.0667}]}, "L_fingeL_5": {"rotate": [{"value": 10.18, "curve": [0.085, 3.48, 0.176, -4.9]}, {"time": 0.2333, "value": -4.9, "curve": [0.317, -3.58, 0.408, -1.85]}, {"time": 0.5, "curve": [0.617, 0, 0.85, -6.64]}, {"time": 0.9667, "value": -6.64}], "translate": [{"x": -3.87, "y": -22.21, "curve": [0.058, -3.87, 0.175, 0, 0.058, -22.21, 0.175, 0]}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.5}]}, "main_C": {"rotate": [{}], "translate": [{}]}, "R_finger_3": {"rotate": [{"value": -15.2, "curve": [0.058, -15.2, 0.175, 4.37]}, {"time": 0.2333, "value": 4.37, "curve": [0.321, 3.07, 0.409, 1.61]}, {"time": 0.5, "curve": [0.617, 0, 0.85, -26.08]}, {"time": 0.9667, "value": -26.08}], "translate": [{"x": 6.87, "y": -20, "curve": [0.058, 6.87, 0.175, 0, 0.058, -20, 0.175, 0]}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.5}]}, "R_finger_1": {"rotate": [{"value": 26.59, "curve": [0.058, 26.59, 0.175, -10.55]}, {"time": 0.2333, "value": -10.55, "curve": [0.3, -10.55, 0.433, 0]}, {"time": 0.5, "curve": [0.617, 0, 0.85, 13.08]}, {"time": 0.9667, "value": 13.08}], "translate": [{"x": -7.23, "y": -11.77, "curve": [0.058, -7.23, 0.175, 0, 0.058, -11.77, 0.175, 0]}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.5}]}, "R_foot_finger_2": {"rotate": [{"value": -6.3, "curve": [0.23, -6.3, 0.443, -12.01]}, {"time": 0.6333, "value": -11.98, "curve": "stepped"}, {"time": 1.3, "value": -11.98, "curve": [1.358, -11.98, 1.475, 17.77]}, {"time": 1.5333, "value": 17.77, "curve": "stepped"}, {"time": 1.7667, "value": 17.77, "curve": [1.86, 15.43, 1.957, 10.54]}, {"time": 2.0667, "value": 10.54}], "translate": [{"curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 1.3, "curve": "stepped"}, {"time": 2.0667}]}, "L_fingeL_4": {"rotate": [{"value": 10.02, "curve": [0.085, -3.91, 0.176, -21.33]}, {"time": 0.2333, "value": -21.33, "curve": [0.317, -15.56, 0.408, -8.05]}, {"time": 0.5, "curve": [0.617, 0, 0.85, 7.87]}, {"time": 0.9667, "value": 7.87}], "translate": [{"x": -12.06, "y": -3.19, "curve": [0.058, -12.06, 0.175, 0, 0.058, -3.19, 0.175, 0]}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.5}]}, "mouth": {"rotate": [{}], "translate": [{}]}, "tail base": {"rotate": [{}, {"time": 0.3, "value": 3.38}, {"time": 0.7333}, {"time": 1.1333, "value": 23.59}, {"time": 1.4, "value": -23.17, "curve": "stepped"}, {"time": 1.6667, "value": -23.17}, {"time": 2.2333}], "translate": [{}]}}, "transform": {"L_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}], "R_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}]}, "path": {"L_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0}]}, "R_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0}]}}, "attachments": {"default": {"head": {"monkey new/head": {"deform": [{}, {"time": 0.0667, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": "stepped"}, {"time": 0.2333, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": [0.236, 0, 0.256, 1]}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.2333, "curve": [1.267, 0, 1.307, 1]}, {"time": 1.3333, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": "stepped"}, {"time": 1.8667, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": [1.883, 0, 2, 1]}, {"time": 2.0667}]}}, "monkey_mouth smile open": {"monkey new/monkey_mouth smile open": {"deform": [{"time": 0.4, "offset": 20, "vertices": [4.8644, 0.27689, 4.86434, 0.27689, 6.2628, -0.85142, 6.2627, -0.85142, 3.26963, -0.47106, 3.26947, -0.4711, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34415, 0.36221, -5.34421, 0.36221, -0.62111, -0.24179, -0.62134, -0.24177, 2.31476, -2.31107, 2.31456, -2.31105, 1.54562, -1.58092, 1.54558, -1.58093, 0, 0, 0, 0, 3.15429, -2.45897, 3.15425, -2.45897, 13.57594, -8.54459, 13.57582, -8.54461, 29.8631, -12.59916, 29.8629, -12.59918, 37.34046, -10.11008, 37.34019, -10.11013, 39.27617, -5.37841, 39.27599, -5.37841, 38.10587, -2.64022, 38.10558, -2.64022, 32.29321, 0.99618, 32.2929, 0.99617, 20.97192, 2.91691, 20.97177, 2.91693, 8.5104, 1.81163, 8.51024, 1.81162, 2.11884, 0.49203, 2.11881, 0.49204, 4.07748, 1.15525, 4.07738, 1.15522, 0.20969, 0.9987, 0.20964, 0.99868], "curve": [0.444, 0, 0.489, 1]}, {"time": 0.5333, "offset": 8, "vertices": [-1.28351, -0.52665, -1.28351, -0.52659, -3.83746, -1.57442, -3.8375, -1.57436, -3.76192, 1.04626, -3.76199, 1.04635, -10.57374, -8.1484, -10.57376, -8.14831, -10.57374, -8.1484, -10.57376, -8.14831, -7.09026, -5.46395, -7.09029, -5.46386, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.44277, 0.04588, 0.44277, 0.04589, 2.88429, 0.78514, 2.88429, 0.7852, 0.7377, 1.49104, 0.73769, 1.49109, 0, 0, 0, 0, 0.22753, 3.67193, 0.2275, 3.67196, 0.17809, 11.74496, 0.17805, 11.74501, -5.59586, 10.92609, -5.59589, 10.92618, -12.27477, 10.97721, -12.27477, 10.97731, -16.42873, 5.47097, -16.42877, 5.47107, -18.42986, -5.86831, -18.42994, -5.86811, -15.80939, -16.45662, -15.80943, -16.45621, -9.02964, -16.01102, -9.02982, -16.0108, -4.58274, -13.48683, -4.58289, -13.48662, -2.28416, -10.05, -2.28421, -10.04985, -1.84745, -8.78892, -1.84754, -8.78885, -0.12933, -0.73415, -0.12939, -0.73405], "curve": "stepped"}, {"time": 1.2, "offset": 8, "vertices": [-1.28351, -0.52665, -1.28351, -0.52659, -3.83746, -1.57442, -3.8375, -1.57436, -3.76192, 1.04626, -3.76199, 1.04635, -10.57374, -8.1484, -10.57376, -8.14831, -10.57374, -8.1484, -10.57376, -8.14831, -7.09026, -5.46395, -7.09029, -5.46386, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.44277, 0.04588, 0.44277, 0.04589, 2.88429, 0.78514, 2.88429, 0.7852, 0.7377, 1.49104, 0.73769, 1.49109, 0, 0, 0, 0, 0.22753, 3.67193, 0.2275, 3.67196, 0.17809, 11.74496, 0.17805, 11.74501, -5.59586, 10.92609, -5.59589, 10.92618, -12.27477, 10.97721, -12.27477, 10.97731, -16.42873, 5.47097, -16.42877, 5.47107, -18.42986, -5.86831, -18.42994, -5.86811, -15.80939, -16.45662, -15.80943, -16.45621, -9.02964, -16.01102, -9.02982, -16.0108, -4.58274, -13.48683, -4.58289, -13.48662, -2.28416, -10.05, -2.28421, -10.04985, -1.84745, -8.78892, -1.84754, -8.78885, -0.12933, -0.73415, -0.12939, -0.73405]}, {"time": 1.4}]}}}}, "drawOrder": [{}, {"time": 0.1333}]}, "Idle": {"slots": {"banana_2_1": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_2_1"}]}, "banana_2_2": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_2_2"}]}, "banana_3_2": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_3_2"}]}, "banana_3_3": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_3_3"}]}, "closed_eye_left": {"attachment": [{"time": 3.0667, "name": "monkey new/closed_eye_left"}, {"time": 3.2667}, {"time": 4.1333, "name": "monkey new/closed_eye_left"}, {"time": 4.3333}, {"time": 5.6, "name": "monkey new/closed_eye_left"}, {"time": 5.8}, {"time": 6.9333, "name": "monkey new/closed_eye_left"}, {"time": 7.1333}]}, "closed_eye_right": {"attachment": [{"time": 3.0667, "name": "monkey new/closed_eye_right"}, {"time": 3.2667}, {"time": 4.1333, "name": "monkey new/closed_eye_right"}, {"time": 4.3333}, {"time": 5.6, "name": "monkey new/closed_eye_right"}, {"time": 5.8}, {"time": 6.9333, "name": "monkey new/closed_eye_right"}, {"time": 7.1333}]}, "monkeyOLD_banana_1": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_1"}]}, "monkeyOLD_banana_3_1": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_3_1"}]}, "monkey_left_arm": {"attachment": [{}]}, "monkey_left_hand": {"attachment": [{}]}, "monkey_left_hand closed": {"attachment": [{}]}, "monkey_mouth angry": {"attachment": [{}]}, "monkey_mouth o": {"attachment": [{}]}, "monkey_mouth smile closed": {"attachment": [{"name": "monkey new/monkey_mouth smile closed"}, {"time": 4.4667}, {"time": 5.8333, "name": "monkey new/monkey_mouth smile closed"}]}, "monkey_mouth smile open": {"attachment": [{}, {"time": 4.4667, "name": "monkey new/monkey_mouth smile open"}, {"time": 5.8333}]}, "monkey_right_hand": {"attachment": [{}]}}, "bones": {"Banana": {"rotate": [{"value": 0.03, "curve": [0.102, -0.9, 0.244, -4.24]}, {"time": 0.3667, "value": -4.24, "curve": [0.797, -4.24, 0.883, 4.68]}, {"time": 1.3, "value": 4.68, "curve": [1.561, 4.68, 1.83, 2.03]}, {"time": 2, "value": 0.03, "curve": [2.102, -0.9, 2.244, -4.24]}, {"time": 2.3667, "value": -4.24, "curve": [2.797, -4.24, 2.883, 4.68]}, {"time": 3.3, "value": 4.68, "curve": [3.561, 4.68, 3.83, 2.03]}, {"time": 4, "value": 0.03, "curve": [4.102, -0.9, 4.244, -4.24]}, {"time": 4.3667, "value": -4.24, "curve": [4.797, -4.24, 4.883, 4.68]}, {"time": 5.3, "value": 4.68, "curve": [5.561, 4.68, 5.83, 2.03]}, {"time": 6, "value": 0.03, "curve": [6.102, -0.9, 6.244, -4.24]}, {"time": 6.3667, "value": -4.24, "curve": [6.797, -4.24, 6.883, 4.68]}, {"time": 7.3, "value": 4.68, "curve": [7.561, 4.68, 7.83, 2.03]}, {"time": 8, "value": 0.03}], "translate": [{"x": 6.4, "y": -7.09, "curve": [0.102, 2.46, 0.244, -11.73, 0.102, -5.09, 0.244, 2.09]}, {"time": 0.3667, "x": -11.73, "y": 2.09, "curve": [0.797, -11.73, 0.883, 30.64, 0.797, 2.09, 0.883, -30.22]}, {"time": 1.3, "x": 30.64, "y": -30.22, "curve": [1.561, 30.64, 1.83, 16.84, 1.561, -30.22, 1.83, -17.05]}, {"time": 2, "x": 6.4, "y": -7.09, "curve": [2.102, 2.46, 2.244, -11.73, 2.102, -5.09, 2.244, 2.09]}, {"time": 2.3667, "x": -11.73, "y": 2.09, "curve": [2.797, -11.73, 2.883, 30.64, 2.797, 2.09, 2.883, -30.22]}, {"time": 3.3, "x": 30.64, "y": -30.22, "curve": [3.561, 30.64, 3.83, 16.84, 3.561, -30.22, 3.83, -17.05]}, {"time": 4, "x": 6.4, "y": -7.09, "curve": [4.102, 2.46, 4.244, -11.73, 4.102, -5.09, 4.244, 2.09]}, {"time": 4.3667, "x": -11.73, "y": 2.09, "curve": [4.797, -11.73, 4.883, 30.64, 4.797, 2.09, 4.883, -30.22]}, {"time": 5.3, "x": 30.64, "y": -30.22, "curve": [5.561, 30.64, 5.83, 16.84, 5.561, -30.22, 5.83, -17.05]}, {"time": 6, "x": 6.4, "y": -7.09, "curve": [6.102, 2.46, 6.244, -11.73, 6.102, -5.09, 6.244, 2.09]}, {"time": 6.3667, "x": -11.73, "y": 2.09, "curve": [6.797, -11.73, 6.883, 30.64, 6.797, 2.09, 6.883, -30.22]}, {"time": 7.3, "x": 30.64, "y": -30.22, "curve": [7.561, 30.64, 7.83, 16.84, 7.561, -30.22, 7.83, -17.05]}, {"time": 8, "x": 6.4, "y": -7.09}]}, "root": {"translate": [{}]}, "L_hand_5": {"rotate": [{"value": -62.17, "curve": [0.081, -62.04, 0.167, -61.96]}, {"time": 0.2667, "value": -61.96, "curve": [0.658, -61.96, 0.866, -63.27]}, {"time": 1.2667, "value": -63.27, "curve": [1.561, -63.27, 1.755, -62.55]}, {"time": 2, "value": -62.17, "curve": [2.081, -62.04, 2.167, -61.96]}, {"time": 2.2667, "value": -61.96, "curve": [2.658, -61.96, 2.866, -63.27]}, {"time": 3.2667, "value": -63.27, "curve": [3.561, -63.27, 3.755, -62.55]}, {"time": 4, "value": -62.17, "curve": [4.081, -62.04, 4.167, -61.96]}, {"time": 4.2667, "value": -61.96, "curve": [4.658, -61.96, 4.866, -63.27]}, {"time": 5.2667, "value": -63.27, "curve": [5.561, -63.27, 5.755, -62.55]}, {"time": 6, "value": -62.17, "curve": [6.081, -62.04, 6.167, -61.96]}, {"time": 6.2667, "value": -61.96, "curve": [6.658, -61.96, 6.866, -63.27]}, {"time": 7.2667, "value": -63.27, "curve": [7.561, -63.27, 7.755, -62.55]}, {"time": 8, "value": -62.17}], "translate": [{"x": -0.79, "y": -11.59, "curve": [0.081, -0.14, 0.167, 0.3, 0.081, -10.73, 0.167, -10.15]}, {"time": 0.2667, "x": 0.3, "y": -10.15, "curve": [0.658, 0.3, 0.866, -6.68, 0.658, -10.15, 0.866, -19.31]}, {"time": 1.2667, "x": -6.68, "y": -19.31, "curve": [1.561, -6.68, 1.755, -2.84, 1.561, -19.31, 1.755, -14.27]}, {"time": 2, "x": -0.79, "y": -11.59, "curve": [2.081, -0.14, 2.167, 0.3, 2.081, -10.73, 2.167, -10.15]}, {"time": 2.2667, "x": 0.3, "y": -10.15, "curve": [2.658, 0.3, 2.866, -6.68, 2.658, -10.15, 2.866, -19.31]}, {"time": 3.2667, "x": -6.68, "y": -19.31, "curve": [3.561, -6.68, 3.755, -2.84, 3.561, -19.31, 3.755, -14.27]}, {"time": 4, "x": -0.79, "y": -11.59, "curve": [4.081, -0.14, 4.167, 0.3, 4.081, -10.73, 4.167, -10.15]}, {"time": 4.2667, "x": 0.3, "y": -10.15, "curve": [4.658, 0.3, 4.866, -6.68, 4.658, -10.15, 4.866, -19.31]}, {"time": 5.2667, "x": -6.68, "y": -19.31, "curve": [5.561, -6.68, 5.755, -2.84, 5.561, -19.31, 5.755, -14.27]}, {"time": 6, "x": -0.79, "y": -11.59, "curve": [6.081, -0.14, 6.167, 0.3, 6.081, -10.73, 6.167, -10.15]}, {"time": 6.2667, "x": 0.3, "y": -10.15, "curve": [6.658, 0.3, 6.866, -6.68, 6.658, -10.15, 6.866, -19.31]}, {"time": 7.2667, "x": -6.68, "y": -19.31, "curve": [7.561, -6.68, 7.755, -2.84, 7.561, -19.31, 7.755, -14.27]}, {"time": 8, "x": -0.79, "y": -11.59}]}, "L_hand_6": {"rotate": [{"value": -55.93, "curve": [0.081, -55.42, 0.167, -55.07]}, {"time": 0.2667, "value": -55.07, "curve": [0.658, -55.07, 0.866, -60.55]}, {"time": 1.2667, "value": -60.55, "curve": [1.561, -60.55, 1.755, -57.53]}, {"time": 2, "value": -55.93, "curve": [2.081, -55.42, 2.167, -55.07]}, {"time": 2.2667, "value": -55.07, "curve": [2.658, -55.07, 2.866, -60.55]}, {"time": 3.2667, "value": -60.55, "curve": [3.561, -60.55, 3.755, -57.53]}, {"time": 4, "value": -55.93, "curve": [4.081, -55.42, 4.167, -55.07]}, {"time": 4.2667, "value": -55.07, "curve": [4.406, -55.07, 4.523, -55.73]}, {"time": 4.6333, "value": -56.59, "curve": [4.792, -56.59, 5.108, -80.82]}, {"time": 5.2667, "value": -80.82, "curve": [5.408, -80.82, 5.692, -49.19]}, {"time": 5.8333, "value": -49.19, "curve": [5.942, -49.19, 6.158, -55.07]}, {"time": 6.2667, "value": -55.07, "curve": [6.658, -55.07, 6.866, -60.55]}, {"time": 7.2667, "value": -60.55, "curve": [7.561, -60.55, 7.755, -57.53]}, {"time": 8, "value": -55.93}], "translate": [{"x": -31.68, "y": -25, "curve": [0.081, -31.93, 0.167, -32.1, 0.081, -24.04, 0.167, -23.39]}, {"time": 0.2667, "x": -32.1, "y": -23.39, "curve": [0.658, -32.1, 0.866, -29.42, 0.658, -23.39, 0.866, -33.66]}, {"time": 1.2667, "x": -29.42, "y": -33.66, "curve": [1.561, -29.42, 1.755, -30.9, 1.561, -33.66, 1.755, -28.01]}, {"time": 2, "x": -31.68, "y": -25, "curve": [2.081, -31.93, 2.167, -32.1, 2.081, -24.04, 2.167, -23.39]}, {"time": 2.2667, "x": -32.1, "y": -23.39, "curve": [2.658, -32.1, 2.866, -29.42, 2.658, -23.39, 2.866, -33.66]}, {"time": 3.2667, "x": -29.42, "y": -33.66, "curve": [3.561, -29.42, 3.755, -30.9, 3.561, -33.66, 3.755, -28.01]}, {"time": 4, "x": -31.68, "y": -25, "curve": [4.081, -31.93, 4.167, -32.1, 4.081, -24.04, 4.167, -23.39]}, {"time": 4.2667, "x": -32.1, "y": -23.39, "curve": [4.406, -32.1, 4.523, -31.78, 4.406, -23.39, 4.523, -24.63]}, {"time": 4.6333, "x": -31.36, "y": -26.24, "curve": [4.792, -31.36, 5.108, -48.91, 4.792, -26.24, 5.108, -30.42]}, {"time": 5.2667, "x": -48.91, "y": -30.42, "curve": [5.408, -48.91, 5.692, -25.03, 5.408, -30.42, 5.692, -29.2]}, {"time": 5.8333, "x": -25.03, "y": -29.2, "curve": [5.942, -25.03, 6.158, -32.1, 5.942, -29.2, 6.158, -23.39]}, {"time": 6.2667, "x": -32.1, "y": -23.39, "curve": [6.658, -32.1, 6.866, -29.42, 6.658, -23.39, 6.866, -33.66]}, {"time": 7.2667, "x": -29.42, "y": -33.66, "curve": [7.561, -29.42, 7.755, -30.9, 7.561, -33.66, 7.755, -28.01]}, {"time": 8, "x": -31.68, "y": -25}]}, "L_hand_4": {"rotate": [{"value": -33.11, "curve": [0.081, -33.37, 0.167, -33.55]}, {"time": 0.2667, "value": -33.55, "curve": [0.658, -33.55, 0.866, -30.72]}, {"time": 1.2667, "value": -30.72, "curve": [1.561, -30.72, 1.755, -32.28]}, {"time": 2, "value": -33.11, "curve": [2.081, -33.37, 2.167, -33.55]}, {"time": 2.2667, "value": -33.55, "curve": [2.658, -33.55, 2.866, -30.72]}, {"time": 3.2667, "value": -30.72, "curve": [3.561, -30.72, 3.755, -32.28]}, {"time": 4, "value": -33.11, "curve": [4.081, -33.37, 4.167, -33.55]}, {"time": 4.2667, "value": -33.55, "curve": [4.658, -33.55, 4.866, -30.72]}, {"time": 5.2667, "value": -30.72, "curve": [5.561, -30.72, 5.755, -32.28]}, {"time": 6, "value": -33.11, "curve": [6.081, -33.37, 6.167, -33.55]}, {"time": 6.2667, "value": -33.55, "curve": [6.658, -33.55, 6.866, -30.72]}, {"time": 7.2667, "value": -30.72, "curve": [7.561, -30.72, 7.755, -32.28]}, {"time": 8, "value": -33.11}], "translate": [{"x": 14.95, "y": -54.65, "curve": [0.081, 15.03, 0.167, 15.08, 0.081, -55.17, 0.167, -55.52]}, {"time": 0.2667, "x": 15.08, "y": -55.52, "curve": [0.658, 15.08, 0.866, 14.24, 0.658, -55.52, 0.866, -49.98]}, {"time": 1.2667, "x": 14.24, "y": -49.98, "curve": [1.561, 14.24, 1.755, 14.71, 1.561, -49.98, 1.755, -53.03]}, {"time": 2, "x": 14.95, "y": -54.65, "curve": [2.081, 15.03, 2.167, 15.08, 2.081, -55.17, 2.167, -55.52]}, {"time": 2.2667, "x": 15.08, "y": -55.52, "curve": [2.658, 15.08, 2.866, 14.24, 2.658, -55.52, 2.866, -49.98]}, {"time": 3.2667, "x": 14.24, "y": -49.98, "curve": [3.561, 14.24, 3.755, 14.71, 3.561, -49.98, 3.755, -53.03]}, {"time": 4, "x": 14.95, "y": -54.65, "curve": [4.081, 15.03, 4.167, 15.08, 4.081, -55.17, 4.167, -55.52]}, {"time": 4.2667, "x": 15.08, "y": -55.52, "curve": [4.658, 15.08, 4.866, 14.24, 4.658, -55.52, 4.866, -49.98]}, {"time": 5.2667, "x": 14.24, "y": -49.98, "curve": [5.561, 14.24, 5.755, 14.71, 5.561, -49.98, 5.755, -53.03]}, {"time": 6, "x": 14.95, "y": -54.65, "curve": [6.081, 15.03, 6.167, 15.08, 6.081, -55.17, 6.167, -55.52]}, {"time": 6.2667, "x": 15.08, "y": -55.52, "curve": [6.658, 15.08, 6.866, 14.24, 6.658, -55.52, 6.866, -49.98]}, {"time": 7.2667, "x": 14.24, "y": -49.98, "curve": [7.561, 14.24, 7.755, 14.71, 7.561, -49.98, 7.755, -53.03]}, {"time": 8, "x": 14.95, "y": -54.65}]}, "L_arm_2": {"rotate": [{"value": 132.11, "curve": [0.081, 131.79, 0.167, 131.57]}, {"time": 0.2667, "value": 131.57, "curve": [0.658, 131.57, 0.866, 134.98]}, {"time": 1.2667, "value": 134.98, "curve": [1.561, 134.98, 1.755, 133.11]}, {"time": 2, "value": 132.11, "curve": [2.081, 131.79, 2.167, 131.57]}, {"time": 2.2667, "value": 131.57, "curve": [2.658, 131.57, 2.866, 134.98]}, {"time": 3.2667, "value": 134.98, "curve": [3.561, 134.98, 3.755, 133.11]}, {"time": 4, "value": 132.11, "curve": [4.081, 131.79, 4.167, 131.57]}, {"time": 4.2667, "value": 131.57, "curve": [4.658, 131.57, 4.866, 148.73]}, {"time": 5.2667, "value": 148.73, "curve": [5.561, 148.73, 5.755, 137.89]}, {"time": 6, "value": 132.11, "curve": [6.081, 131.79, 6.167, 131.57]}, {"time": 6.2667, "value": 131.57, "curve": [6.658, 131.57, 6.866, 134.98]}, {"time": 7.2667, "value": 134.98, "curve": [7.561, 134.98, 7.755, 133.11]}, {"time": 8, "value": 132.11}], "translate": [{"x": -52.74, "y": 8.72, "curve": "stepped"}, {"time": 0.2667, "x": -52.74, "y": 8.72, "curve": "stepped"}, {"time": 2, "x": -52.74, "y": 8.72, "curve": "stepped"}, {"time": 2.2667, "x": -52.74, "y": 8.72, "curve": "stepped"}, {"time": 4, "x": -52.74, "y": 8.72, "curve": "stepped"}, {"time": 4.2667, "x": -52.74, "y": 8.72, "curve": "stepped"}, {"time": 6, "x": -52.74, "y": 8.72, "curve": "stepped"}, {"time": 6.2667, "x": -52.74, "y": 8.72, "curve": "stepped"}, {"time": 8, "x": -52.74, "y": 8.72}]}, "main_C": {"rotate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}], "translate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}]}, "R_leg": {"rotate": [{"value": -3.22, "curve": [0.129, -1.46, 0.26, 0]}, {"time": 0.4, "curve": [0.706, 0, 1.075, -9.31]}, {"time": 1.4, "value": -9.31, "curve": [1.611, -9.31, 1.803, -5.93]}, {"time": 2, "value": -3.22, "curve": [2.129, -1.46, 2.26, 0]}, {"time": 2.4, "curve": [2.706, 0, 3.075, -9.31]}, {"time": 3.4, "value": -9.31, "curve": [3.611, -9.31, 3.803, -5.93]}, {"time": 4, "value": -3.22, "curve": [4.129, -1.46, 4.26, 0]}, {"time": 4.4, "curve": [4.706, 0, 5.075, -9.31]}, {"time": 5.4, "value": -9.31, "curve": [5.611, -9.31, 5.803, -5.93]}, {"time": 6, "value": -3.22, "curve": [6.129, -1.46, 6.26, 0]}, {"time": 6.4, "curve": [6.706, 0, 7.075, -9.31]}, {"time": 7.4, "value": -9.31, "curve": [7.611, -9.31, 7.803, -5.93]}, {"time": 8, "value": -3.22}], "translate": [{"x": 4.77, "y": -0.6, "curve": [0.129, 2.17, 0.26, 0, 0.129, -0.27, 0.26, 0]}, {"time": 0.4, "curve": [0.749, 0, 1.047, 13.79, 0.749, 0, 1.047, -1.72]}, {"time": 1.4, "x": 13.79, "y": -1.72, "curve": [1.611, 13.79, 1.803, 8.78, 1.611, -1.72, 1.803, -1.1]}, {"time": 2, "x": 4.77, "y": -0.6, "curve": [2.129, 2.17, 2.26, 0, 2.129, -0.27, 2.26, 0]}, {"time": 2.4, "curve": [2.749, 0, 3.047, 13.79, 2.749, 0, 3.047, -1.72]}, {"time": 3.4, "x": 13.79, "y": -1.72, "curve": [3.611, 13.79, 3.803, 8.78, 3.611, -1.72, 3.803, -1.1]}, {"time": 4, "x": 4.77, "y": -0.6, "curve": [4.129, 2.17, 4.26, 0, 4.129, -0.27, 4.26, 0]}, {"time": 4.4, "curve": [4.749, 0, 5.047, 13.79, 4.749, 0, 5.047, -1.72]}, {"time": 5.4, "x": 13.79, "y": -1.72, "curve": [5.611, 13.79, 5.803, 8.78, 5.611, -1.72, 5.803, -1.1]}, {"time": 6, "x": 4.77, "y": -0.6, "curve": [6.129, 2.17, 6.26, 0, 6.129, -0.27, 6.26, 0]}, {"time": 6.4, "curve": [6.749, 0, 7.047, 13.79, 6.749, 0, 7.047, -1.72]}, {"time": 7.4, "x": 13.79, "y": -1.72, "curve": [7.611, 13.79, 7.803, 8.78, 7.611, -1.72, 7.803, -1.1]}, {"time": 8, "x": 4.77, "y": -0.6}]}, "R_foot": {"rotate": [{"value": 3.59, "curve": [0.112, 0, 0.223, -2.69]}, {"time": 0.3333, "value": -2.69, "curve": [0.677, -2.69, 1.008, 20.62]}, {"time": 1.3333, "value": 20.62, "curve": [1.561, 20.62, 1.782, 10.4]}, {"time": 2, "value": 3.59, "curve": [2.112, 0, 2.223, -2.69]}, {"time": 2.3333, "value": -2.69, "curve": [2.677, -2.69, 3.008, 20.62]}, {"time": 3.3333, "value": 20.62, "curve": [3.561, 20.62, 3.782, 10.4]}, {"time": 4, "value": 3.59, "curve": [4.112, 0, 4.223, -2.69]}, {"time": 4.3333, "value": -2.69, "curve": [4.677, -2.69, 5.008, 20.62]}, {"time": 5.3333, "value": 20.62, "curve": [5.561, 20.62, 5.782, 10.4]}, {"time": 6, "value": 3.59, "curve": [6.112, 0, 6.223, -2.69]}, {"time": 6.3333, "value": -2.69, "curve": [6.677, -2.69, 7.008, 20.62]}, {"time": 7.3333, "value": 20.62, "curve": [7.561, 20.62, 7.782, 10.4]}, {"time": 8, "value": 3.59}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 2.3333, "curve": "stepped"}, {"time": 3.3333, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.3333, "curve": "stepped"}, {"time": 5.3333, "curve": "stepped"}, {"time": 6, "curve": "stepped"}, {"time": 6.3333, "curve": "stepped"}, {"time": 7.3333, "curve": "stepped"}, {"time": 8}]}, "R_foot_finger_2": {"rotate": [{"value": 10.54, "curve": [0.311, 10.54, 0.628, -11.98]}, {"time": 1, "value": -11.98, "curve": [1.311, -11.98, 1.628, 10.54]}, {"time": 2, "value": 10.54, "curve": [2.311, 10.54, 2.628, -11.98]}, {"time": 3, "value": -11.98, "curve": [3.311, -11.98, 3.628, 10.54]}, {"time": 4, "value": 10.54, "curve": [4.311, 10.54, 4.628, -11.98]}, {"time": 5, "value": -11.98, "curve": [5.311, -11.98, 5.628, 10.54]}, {"time": 6, "value": 10.54, "curve": [6.311, 10.54, 6.628, -11.98]}, {"time": 7, "value": -11.98, "curve": [7.311, -11.98, 7.628, 10.54]}, {"time": 8, "value": 10.54}], "translate": [{"curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 5, "curve": "stepped"}, {"time": 6, "curve": "stepped"}, {"time": 7, "curve": "stepped"}, {"time": 8}]}, "R_leg2": {"rotate": [{"value": 5.75, "curve": [0.129, 2.61, 0.26, 0]}, {"time": 0.4, "curve": [0.749, 0, 1.047, 16.62]}, {"time": 1.4, "value": 16.62, "curve": [1.611, 16.62, 1.803, 10.58]}, {"time": 2, "value": 5.75, "curve": [2.129, 2.61, 2.26, 0]}, {"time": 2.4, "curve": [2.749, 0, 3.047, 16.62]}, {"time": 3.4, "value": 16.62, "curve": [3.611, 16.62, 3.803, 10.58]}, {"time": 4, "value": 5.75, "curve": [4.129, 2.61, 4.26, 0]}, {"time": 4.4, "curve": [4.749, 0, 5.047, 16.62]}, {"time": 5.4, "value": 16.62, "curve": [5.611, 16.62, 5.803, 10.58]}, {"time": 6, "value": 5.75, "curve": [6.129, 2.61, 6.26, 0]}, {"time": 6.4, "curve": [6.749, 0, 7.047, 16.62]}, {"time": 7.4, "value": 16.62, "curve": [7.611, 16.62, 7.803, 10.58]}, {"time": 8, "value": 5.75}], "translate": [{"x": -0.6, "y": 0.6, "curve": [0.129, -0.27, 0.26, 0, 0.129, 0.27, 0.26, 0]}, {"time": 0.4, "curve": [0.749, 0, 1.047, -1.72, 0.749, 0, 1.047, 1.72]}, {"time": 1.4, "x": -1.72, "y": 1.72, "curve": [1.611, -1.72, 1.803, -1.1, 1.611, 1.72, 1.803, 1.1]}, {"time": 2, "x": -0.6, "y": 0.6, "curve": [2.129, -0.27, 2.26, 0, 2.129, 0.27, 2.26, 0]}, {"time": 2.4, "curve": [2.749, 0, 3.047, -1.72, 2.749, 0, 3.047, 1.72]}, {"time": 3.4, "x": -1.72, "y": 1.72, "curve": [3.611, -1.72, 3.803, -1.1, 3.611, 1.72, 3.803, 1.1]}, {"time": 4, "x": -0.6, "y": 0.6, "curve": [4.129, -0.27, 4.26, 0, 4.129, 0.27, 4.26, 0]}, {"time": 4.4, "curve": [4.749, 0, 5.047, -1.72, 4.749, 0, 5.047, 1.72]}, {"time": 5.4, "x": -1.72, "y": 1.72, "curve": [5.611, -1.72, 5.803, -1.1, 5.611, 1.72, 5.803, 1.1]}, {"time": 6, "x": -0.6, "y": 0.6, "curve": [6.129, -0.27, 6.26, 0, 6.129, 0.27, 6.26, 0]}, {"time": 6.4, "curve": [6.749, 0, 7.047, -1.72, 6.749, 0, 7.047, 1.72]}, {"time": 7.4, "x": -1.72, "y": 1.72, "curve": [7.611, -1.72, 7.803, -1.1, 7.611, 1.72, 7.803, 1.1]}, {"time": 8, "x": -0.6, "y": 0.6}]}, "R_foot2": {"rotate": [{"value": -2.29, "curve": [0.112, -6.15, 0.223, -9.05]}, {"time": 0.3333, "value": -9.05, "curve": [0.677, -9.05, 1.008, 16.05]}, {"time": 1.3333, "value": 16.05, "curve": [1.561, 16.05, 1.782, 5.05]}, {"time": 2, "value": -2.29, "curve": [2.112, -6.15, 2.223, -9.05]}, {"time": 2.3333, "value": -9.05, "curve": [2.677, -9.05, 3.008, 16.05]}, {"time": 3.3333, "value": 16.05, "curve": [3.561, 16.05, 3.782, 5.05]}, {"time": 4, "value": -2.29, "curve": [4.112, -6.15, 4.223, -9.05]}, {"time": 4.3333, "value": -9.05, "curve": [4.677, -9.05, 5.008, 16.05]}, {"time": 5.3333, "value": 16.05, "curve": [5.561, 16.05, 5.782, 5.05]}, {"time": 6, "value": -2.29, "curve": [6.112, -6.15, 6.223, -9.05]}, {"time": 6.3333, "value": -9.05, "curve": [6.677, -9.05, 7.008, 16.05]}, {"time": 7.3333, "value": 16.05, "curve": [7.561, 16.05, 7.782, 5.05]}, {"time": 8, "value": -2.29}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 2.3333, "curve": "stepped"}, {"time": 3.3333, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.3333, "curve": "stepped"}, {"time": 5.3333, "curve": "stepped"}, {"time": 6, "curve": "stepped"}, {"time": 6.3333, "curve": "stepped"}, {"time": 7.3333, "curve": "stepped"}, {"time": 8}]}, "R_foot_finger_5": {"rotate": [{"value": 9.43, "curve": [0.311, 9.43, 0.628, -4.54]}, {"time": 1, "value": -4.54, "curve": [1.311, -4.54, 1.628, 9.43]}, {"time": 2, "value": 9.43, "curve": [2.311, 9.43, 2.628, -4.54]}, {"time": 3, "value": -4.54, "curve": [3.311, -4.54, 3.628, 9.43]}, {"time": 4, "value": 9.43, "curve": [4.311, 9.43, 4.628, -4.54]}, {"time": 5, "value": -4.54, "curve": [5.311, -4.54, 5.628, 9.43]}, {"time": 6, "value": 9.43, "curve": [6.311, 9.43, 6.628, -4.54]}, {"time": 7, "value": -4.54, "curve": [7.311, -4.54, 7.628, 9.43]}, {"time": 8, "value": 9.43}], "translate": [{"curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 3, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 5, "curve": "stepped"}, {"time": 6, "curve": "stepped"}, {"time": 7, "curve": "stepped"}, {"time": 8}]}, "SPINE": {"rotate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}], "translate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}]}, "CHEST": {"rotate": [{"value": 1.95, "curve": [0.055, 2.15, 0.11, 2.27]}, {"time": 0.1667, "value": 2.27, "curve": [0.506, 2.27, 0.818, -1.96]}, {"time": 1.1667, "value": -1.96, "curve": [1.451, -1.96, 1.716, 0.97]}, {"time": 2, "value": 1.95, "curve": [2.055, 2.15, 2.11, 2.27]}, {"time": 2.1667, "value": 2.27, "curve": [2.506, 2.27, 2.818, -1.96]}, {"time": 3.1667, "value": -1.96, "curve": [3.485, -1.96, 3.782, -2.88]}, {"time": 4.1, "value": -3.19, "curve": [4.364, -3.19, 4.497, -10.18]}, {"time": 4.7667, "value": -10.18, "curve": "stepped"}, {"time": 5.1667, "value": -10.18, "curve": [5.575, -10.18, 5.782, -0.33]}, {"time": 6.2, "value": -0.33, "curve": [6.659, -0.33, 6.818, 1.32]}, {"time": 7.0667, "value": 1.34, "curve": [7.436, 1.34, 7.622, 1.95]}, {"time": 8, "value": 1.95}], "translate": [{"curve": [0.527, 0, 0.464, 20.53, 0.527, 0, 0.464, -3.74]}, {"time": 1, "x": 20.53, "y": -3.74, "curve": [1.527, 20.53, 1.464, 0, 1.527, -3.74, 1.464, 0]}, {"time": 2, "curve": [2.527, 0, 2.464, 20.53, 2.527, 0, 2.464, -3.74]}, {"time": 3, "x": 20.53, "y": -3.74, "curve": [3.545, 20.53, 3.479, 0, 3.545, -3.74, 3.479, 0]}, {"time": 4.0333, "curve": [4.156, 0, 4.292, -15.21, 4.156, 0, 4.292, 2.77]}, {"time": 4.4333, "x": -15.21, "y": 2.77, "curve": [4.556, -15.21, 4.602, 20.53, 4.556, 2.77, 4.602, -3.74]}, {"time": 4.9333, "x": 20.53, "y": -3.74, "curve": [5.179, 20.53, 5.177, 0, 5.179, -3.74, 5.177, 0]}, {"time": 5.2667, "curve": [5.364, 0, 5.421, -15.21, 5.364, 0, 5.421, 2.77]}, {"time": 5.5667, "x": -15.21, "y": 2.77, "curve": [5.746, -15.21, 5.784, 20.53, 5.746, 2.77, 5.784, -3.74]}, {"time": 6.1333, "x": 20.53, "y": -3.74, "curve": [6.866, 20.53, 6.913, -4.82, 6.866, -3.74, 6.913, 0.88]}, {"time": 7.2, "x": -4.82, "y": 0.88, "curve": [7.622, -4.82, 7.571, 0, 7.622, 0.88, 7.571, 0]}, {"time": 8}]}, "HEAD": {"rotate": [{"value": -2.12, "curve": [0.25, -2.12, 0.75, -2.03]}, {"time": 1, "value": -2.03, "curve": [1.421, -2.03, 1.6, -1.3]}, {"time": 2, "value": -1.3, "curve": [2.393, -1.3, 2.56, -0.45]}, {"time": 2.9333, "value": -0.45, "curve": [3.308, -0.45, 3.238, 10.41]}, {"time": 4, "value": 10.41, "curve": [4.283, 10.41, 4.212, -3.62]}, {"time": 4.7667, "value": -3.62, "curve": "stepped"}, {"time": 5.3333, "value": -3.62, "curve": [5.769, -3.62, 5.954, 5.58]}, {"time": 6.3667, "value": 5.58, "curve": [6.671, 5.58, 6.652, -13.8]}, {"time": 7.0667, "value": -13.8, "curve": [7.178, -13.8, 7.277, -14.78]}, {"time": 7.4, "value": -14.78, "curve": [7.638, -14.78, 7.748, -2.12]}, {"time": 8, "value": -2.12}], "translate": [{"curve": "stepped"}, {"time": 2.5667, "curve": [2.683, 0, 2.917, 10.28, 2.683, 0, 2.917, -1.51]}, {"time": 3.0333, "x": 10.28, "y": -1.51, "curve": [3.142, 10.28, 3.358, -14.35, 3.142, -1.51, 3.358, 2.38]}, {"time": 3.4667, "x": -14.35, "y": 2.38, "curve": [3.567, -14.35, 3.767, 0, 3.567, 2.38, 3.767, 0]}, {"time": 3.8667, "curve": "stepped"}, {"time": 4.5, "curve": [4.633, 0, 4.835, 21.87, 4.633, 0, 4.835, 0]}, {"time": 5.0333, "x": 21.77, "curve": [5.183, 21.77, 5.483, 0, 5.183, 0, 5.483, 0]}, {"time": 5.6333, "curve": "stepped"}, {"time": 6.2, "curve": [6.317, 0, 6.55, 10.28, 6.317, 0, 6.55, -1.51]}, {"time": 6.6667, "x": 10.28, "y": -1.51, "curve": [6.792, 10.28, 7.042, -14.35, 6.792, -1.51, 7.042, 2.38]}, {"time": 7.1667, "x": -14.35, "y": 2.38, "curve": [7.3, -14.35, 7.388, 0, 7.3, 2.38, 7.388, 0]}, {"time": 7.7}]}, "L_brow": {"rotate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4.1333, "curve": "stepped"}, {"time": 4.3333, "curve": "stepped"}, {"time": 5.9, "curve": "stepped"}, {"time": 6.6333, "curve": "stepped"}, {"time": 8}], "translate": [{"x": 7.25, "y": -0.95, "curve": [0.122, 3.24, 0.244, 0, 0.122, 0, 0.244, 0]}, {"time": 0.3667, "curve": [0.7, 0, 1.033, 36.67, 0.7, 0, 1.033, -2.51]}, {"time": 1.3667, "x": 36.67, "y": -2.51, "curve": [1.578, 36.67, 1.789, 14.99, 1.578, -2.51, 1.789, -1.48]}, {"time": 2, "x": 7.25, "y": -0.95, "curve": [2.122, 2.77, 2.244, 0, 2.122, -0.64, 2.244, 0]}, {"time": 2.3667, "curve": [2.533, 0, 2.7, 10.73, 2.533, 0, 2.7, -0.57]}, {"time": 2.8667, "x": 23.18, "y": -1.59, "curve": [2.9, 25.67, 2.933, 44.82, 2.9, -1.79, 2.933, -3.66]}, {"time": 2.9667, "x": 44.82, "y": -3.66, "curve": [3, 44.82, 3.033, 9.02, 3, -3.66, 3.033, 2.56]}, {"time": 3.0667, "x": 7.38, "y": 2.56, "curve": [3.122, 4.66, 3.178, -10.2, 3.122, 2.56, 3.178, -7.78]}, {"time": 3.2333, "x": -10.2, "y": -7.78, "curve": [3.3, -10.2, 3.367, 33.27, 3.3, -7.78, 3.367, 0.27]}, {"time": 3.4333, "x": 37.03, "y": 0.27, "curve": [3.633, 48.32, 3.833, 45.44, 3.633, 0.27, 3.833, -0.16]}, {"time": 4.0333, "x": 45.44, "y": -0.16, "curve": [4.067, 45.44, 4.1, 17.6, 4.067, -0.16, 4.1, -1.89]}, {"time": 4.1333, "x": 17.6, "y": -1.89}, {"time": 4.3333, "x": -19.58, "y": -11.68, "curve": [4.387, -19.58, 4.413, 17.99, 4.387, -11.68, 4.413, -28.68]}, {"time": 4.4667, "x": 17.99, "y": -28.68, "curve": [4.623, 17.99, 4.779, 37.05, 4.623, -28.68, 4.779, -28.92]}, {"time": 4.9333, "x": 40.64, "y": -28.38, "curve": [5.123, 44.97, 5.312, 33.86, 5.123, -27.74, 5.312, -28.23]}, {"time": 5.5, "x": 27.07, "y": -24.01, "curve": [5.544, 25.46, 5.589, -20.24, 5.544, -23.01, 5.589, -6.72]}, {"time": 5.6333, "x": -20.24, "y": -3.82}, {"time": 5.8, "x": -6.07, "y": 5.13, "curve": [5.844, -6.07, 5.856, 65.65, 5.844, 7.97, 5.856, 23.79]}, {"time": 5.9, "x": 65.65, "y": 23.79, "curve": [5.945, 65.65, 5.99, 78.2, 5.945, 23.79, 5.99, 23.78]}, {"time": 6.0333, "x": 78.25, "y": 23.79, "curve": [6.234, 78.45, 6.434, 72.25, 6.234, 23.83, 6.434, 19.77]}, {"time": 6.6333, "x": 66.41, "y": 18.52, "curve": [6.722, 63.8, 6.811, -12.64, 6.722, 17.96, 6.811, 19.49]}, {"time": 6.9, "x": -12.64, "y": 13.63, "curve": [6.967, -12.64, 7.033, -21.66, 6.967, 9.24, 7.033, -4.03]}, {"time": 7.1, "x": -7.02, "y": -10.01, "curve": [7.133, 0.3, 7.167, 39.42, 7.133, -13.01, 7.167, -12.54]}, {"time": 7.2, "x": 39.42, "y": -12.54, "curve": [7.467, 39.42, 7.733, 16.01, 7.467, -12.54, 7.733, -3.01]}, {"time": 8, "x": 7.25, "y": -0.95}]}, "R_brow": {"rotate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4.1333, "curve": "stepped"}, {"time": 4.3333, "curve": "stepped"}, {"time": 5.9, "curve": "stepped"}, {"time": 6.6333, "curve": "stepped"}, {"time": 8}], "translate": [{"x": 7.25, "y": -0.95, "curve": [0.122, 3.24, 0.244, 0, 0.122, -0.36, 0.244, 0]}, {"time": 0.3667, "curve": [0.7, 0, 1.033, 36.67, 0.7, 0, 1.033, -2.51]}, {"time": 1.3667, "x": 36.67, "y": -2.51, "curve": [1.578, 36.67, 1.789, 14.99, 1.578, -2.51, 1.789, -1.48]}, {"time": 2, "x": 7.25, "y": -0.95, "curve": [2.122, 2.77, 2.244, 0, 2.122, -0.64, 2.244, 0]}, {"time": 2.3667, "curve": [2.533, 0, 2.7, 10.73, 2.533, 0, 2.7, -0.57]}, {"time": 2.8667, "x": 23.18, "y": -1.59, "curve": [2.9, 25.67, 2.933, 44.82, 2.9, -1.79, 2.933, -3.66]}, {"time": 2.9667, "x": 44.82, "y": -3.66, "curve": [3, 44.82, 3.033, 9.02, 3, -3.66, 3.033, 2.56]}, {"time": 3.0667, "x": 7.38, "y": 2.56, "curve": [3.122, 4.66, 3.178, -0.52, 3.122, 2.56, 3.178, 2.06]}, {"time": 3.2333, "x": -0.52, "y": 2.06, "curve": [3.3, -0.52, 3.367, 33.27, 3.3, 2.06, 3.367, 0.27]}, {"time": 3.4333, "x": 37.03, "y": 0.27, "curve": [3.633, 48.32, 3.833, 47.07, 3.633, 0.27, 3.833, 1.04]}, {"time": 4.0333, "x": 47.07, "y": 1.04, "curve": [4.067, 47.07, 4.1, 17.6, 4.067, 1.04, 4.1, -1.89]}, {"time": 4.1333, "x": 17.6, "y": -1.89}, {"time": 4.3333, "x": -8.55, "y": -4.75, "curve": [4.387, -8.55, 4.413, 17.99, 4.387, -4.75, 4.413, -28.68]}, {"time": 4.4667, "x": 17.99, "y": -28.68, "curve": [4.623, 17.99, 4.779, 37.05, 4.623, -28.68, 4.779, -28.92]}, {"time": 4.9333, "x": 40.64, "y": -28.38, "curve": [5.123, 44.97, 5.312, 33.86, 5.123, -27.74, 5.312, -28.23]}, {"time": 5.5, "x": 27.07, "y": -24.01, "curve": [5.544, 25.46, 5.589, -6.62, 5.544, -23.01, 5.589, -5.53]}, {"time": 5.6333, "x": -6.62, "y": -2.64}, {"time": 5.8, "x": -11.18, "y": 8.3, "curve": [5.844, -11.18, 5.856, 65.65, 5.844, 11.14, 5.856, 23.79]}, {"time": 5.9, "x": 65.65, "y": 23.79, "curve": [5.945, 65.65, 5.99, 78.2, 5.945, 23.79, 5.99, 23.78]}, {"time": 6.0333, "x": 78.25, "y": 23.79, "curve": [6.234, 78.45, 6.434, 72.25, 6.234, 23.83, 6.434, 19.77]}, {"time": 6.6333, "x": 66.41, "y": 18.52, "curve": [6.722, 63.8, 6.811, -2.33, 6.722, 17.96, 6.811, 14.49]}, {"time": 6.9, "x": -2.33, "y": 9.76, "curve": [6.911, -2.33, 6.922, 1.23, 6.911, 9.17, 6.922, 7.71]}, {"time": 6.9333, "x": 1.44, "y": 7.02, "curve": [6.989, 2.48, 7.044, -7.18, 6.989, 3.59, 7.044, 3.81]}, {"time": 7.1, "x": 5.02, "y": 0.19, "curve": [7.133, 12.34, 7.167, 39.42, 7.133, -1.98, 7.167, -5.74]}, {"time": 7.2, "x": 39.42, "y": -5.74, "curve": [7.467, 39.42, 7.733, 16.01, 7.467, -5.74, 7.733, -2.22]}, {"time": 8, "x": 7.25, "y": -0.95}]}, "face_v": {"rotate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 6.0333, "curve": "stepped"}, {"time": 6.7, "curve": "stepped"}, {"time": 7, "curve": "stepped"}, {"time": 7.3}], "translate": [{"x": 32.5, "curve": [0.105, 23.96, 0.225, 16.41, 0.105, 0, 0.225, 0]}, {"time": 0.4333, "x": 16.41, "curve": [0.908, 16.17, 0.92, 62.66, 0.908, 0, 0.92, 0]}, {"time": 1.4333, "x": 62.66, "curve": [1.717, 62.8, 1.845, 46.58, 1.717, 0, 1.845, 0]}, {"time": 2, "x": 32.5, "curve": [2.105, 30.78, 2.225, 29.25, 2.105, 0, 2.225, 0]}, {"time": 2.4333, "x": 29.25, "curve": [2.603, 29.25, 2.754, 62.97, 2.603, 0, 2.754, 0]}, {"time": 2.9333, "x": 62.97, "curve": [3.084, 62.97, 3.149, 11.05, 3.084, 0, 3.149, 0]}, {"time": 3.4333, "x": 11.05, "curve": [3.618, 11.05, 3.906, 29.15, 3.618, 0, 3.906, 0]}, {"time": 4, "x": 29.15, "curve": [4.13, 29.15, 4.334, -19.86, 4.13, 0, 4.334, 0]}, {"time": 4.4, "x": -19.86, "curve": [4.479, -19.86, 4.642, 23.97, 4.479, 0, 4.642, 0]}, {"time": 4.8333, "x": 23.97, "curve": "stepped"}, {"time": 5.3333, "x": 23.97, "curve": [5.408, 23.97, 5.558, 9.17, 5.408, 0, 5.558, 0]}, {"time": 5.6333, "x": 9.17, "curve": [5.733, 9.17, 5.933, 66.47, 5.733, 0, 5.933, 0]}, {"time": 6.0333, "x": 66.47, "curve": "stepped"}, {"time": 6.7, "x": 66.47, "curve": [6.792, 66.47, 6.923, 21.23, 6.792, 0, 6.923, 0]}, {"time": 7, "x": 21.23, "curve": [7.092, 21.23, 7.223, 32.5, 7.092, 0, 7.223, 0]}, {"time": 7.3, "x": 32.5}]}, "face_h": {"rotate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6.0333, "curve": "stepped"}, {"time": 6.7667, "curve": "stepped"}, {"time": 7.3}], "translate": [{"curve": "stepped"}, {"time": 2.4, "curve": [2.5, 0, 2.7, 0, 2.5, 0, 2.7, 6.68]}, {"time": 2.8, "y": 6.68, "curve": [2.967, 0, 3.2, 0, 2.967, 6.68, 3.2, -14.14]}, {"time": 3.4667, "y": -14.14, "curve": [3.6, 0, 3.867, 0, 3.6, -14.14, 3.867, 0]}, {"time": 4, "curve": [4.196, 0, 4.291, 0, 4.196, 0, 4.291, -42.55]}, {"time": 4.8333, "y": -42.55, "curve": "stepped"}, {"time": 5.3333, "y": -42.55, "curve": [5.544, 0, 5.783, 0, 5.544, -42.55, 5.783, 16.68]}, {"time": 6.0333, "y": 16.68, "curve": "stepped"}, {"time": 6.7667, "y": 16.68, "curve": [6.876, 0, 6.984, 0, 6.876, 16.68, 6.984, -27.98]}, {"time": 7.2333, "y": -27.98, "curve": [7.327, 0, 7.42, 0, 7.327, -27.98, 7.42, 0]}, {"time": 7.6333}]}, "L_eye": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 2.2667, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6.9}], "translate": [{"x": -2.23, "y": 1.95, "curve": [0.089, -2.23, 0.178, -0.56, 0.089, 1.95, 0.178, 1.69]}, {"time": 0.2667, "x": -0.56, "y": 1.69, "curve": [0.322, -0.56, 0.378, -1.32, 0.322, 1.69, 0.378, 7.82]}, {"time": 0.4333, "x": -1.32, "y": 7.89, "curve": [0.733, -1.32, 1.033, -7.84, 0.733, 8.28, 1.033, 5.34]}, {"time": 1.3333, "x": -11.62, "y": 5.34}, {"time": 1.4667, "x": -14.55, "y": 1.34, "curve": [1.511, -15.11, 1.822, -1.82, 1.511, 1.34, 1.822, 1.86]}, {"time": 2, "x": -1.82, "y": 1.86, "curve": "stepped"}, {"time": 2.2667, "x": -1.82, "y": 1.86, "curve": [2.322, -1.82, 2.378, -1.82, 2.322, 1.86, 2.378, 1.86]}, {"time": 2.4333, "x": -1.82, "y": 1.86, "curve": [2.611, -1.82, 2.789, -7.85, 2.611, 1.86, 2.789, 1.86]}, {"time": 2.9667, "x": -10.4, "y": 2.71, "curve": [3.044, -11.52, 3.122, -12.82, 3.044, 3.08, 3.122, 6.28]}, {"time": 3.2, "x": -12.82, "y": 8.96, "curve": [3.222, -12.82, 3.244, -8.51, 3.222, 9.73, 3.244, 13.06]}, {"time": 3.2667, "x": -7.5, "y": 13.06, "curve": [3.333, -4.45, 3.4, -0.64, 3.333, 13.06, 3.4, 10.07]}, {"time": 3.4667, "x": -0.64, "y": 9.37, "curve": [3.644, -0.64, 3.822, -4.21, 3.644, 7.53, 3.822, 5.44]}, {"time": 4, "x": -4.21, "y": 5.44, "curve": [4.1, -4.21, 4.2, 11.1, 4.1, 5.44, 4.2, 21.72]}, {"time": 4.3, "x": 11.1, "y": 21.72, "curve": [4.411, 11.1, 4.522, -25.01, 4.411, 21.72, 4.522, 11.75]}, {"time": 4.6333, "x": -25.01, "y": 11.75, "curve": "stepped"}, {"time": 4.9, "x": -25.01, "y": 11.75, "curve": [4.967, -25.01, 5.033, -14.32, 4.967, 11.75, 5.033, -8.7]}, {"time": 5.1, "x": -14.29, "y": -8.7, "curve": "stepped"}, {"time": 5.5667, "x": -14.29, "y": -8.7, "curve": [5.722, -14.22, 5.789, 7.18, 5.722, -8.7, 5.789, 8.71]}, {"time": 5.9, "x": 5.82, "y": 8.71, "curve": "stepped"}, {"time": 6.7, "x": 5.82, "y": 8.71, "curve": [6.967, 2.58, 6.833, -25.85, 6.967, 8.71, 6.833, 2.26]}, {"time": 6.9, "x": -24.86, "y": 2.4, "curve": [7.034, -22.87, 7.169, -2.46, 7.034, 2.7, 7.169, 14.09]}, {"time": 7.3, "x": 0.15, "y": 14.43, "curve": [7.423, 2.52, 7.546, 0.81, 7.423, 14.75, 7.546, 4.39]}, {"time": 7.6667, "x": -2.16, "y": 1.72, "curve": [7.779, -4.86, 7.889, -2.23, 7.779, -0.71, 7.889, 1.95]}, {"time": 8, "x": -2.23, "y": 1.95}]}, "R_eye": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 2.2667, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6.9}], "translate": [{"x": -0.16, "y": 8.09, "curve": [0.089, -0.16, 0.178, 1.51, 0.089, 8.09, 0.178, 7.83]}, {"time": 0.2667, "x": 1.51, "y": 7.83, "curve": [0.322, 1.51, 0.378, 0.06, 0.322, 7.83, 0.378, 12.01]}, {"time": 0.4333, "x": 0.06, "y": 12.01, "curve": [0.733, 0.06, 1.033, -6.13, 0.733, 12.01, 1.033, 9.07]}, {"time": 1.3333, "x": -9.53, "y": 8.82}, {"time": 1.4667, "x": -12.45, "y": 4.81, "curve": [1.511, -12.95, 1.822, -1.82, 1.511, 4.78, 1.822, 1.86]}, {"time": 2, "x": -1.82, "y": 1.86, "curve": [2.089, -1.82, 2.178, -1.82, 2.089, 1.86, 2.178, 1.86]}, {"time": 2.2667, "x": -1.82, "y": 1.86, "curve": "stepped"}, {"time": 2.4333, "x": -1.82, "y": 1.86, "curve": [2.611, -1.82, 2.789, -7.85, 2.611, 1.86, 2.789, 1.86]}, {"time": 2.9667, "x": -10.4, "y": 2.71, "curve": [3.044, -11.52, 3.122, -12.82, 3.044, 3.08, 3.122, 6.28]}, {"time": 3.2, "x": -12.82, "y": 8.96, "curve": [3.222, -12.82, 3.244, -8.51, 3.222, 9.73, 3.244, 13.06]}, {"time": 3.2667, "x": -7.5, "y": 13.06, "curve": [3.333, -4.45, 3.4, -0.64, 3.333, 13.06, 3.4, 10.07]}, {"time": 3.4667, "x": -0.64, "y": 9.37, "curve": [3.644, -0.64, 3.822, -4.21, 3.644, 7.53, 3.822, 5.44]}, {"time": 4, "x": -4.21, "y": 5.44, "curve": [4.1, -4.21, 4.2, 11.1, 4.1, 5.44, 4.2, 21.72]}, {"time": 4.3, "x": 11.1, "y": 21.72, "curve": [4.411, 11.1, 4.522, -23.1, 4.411, 21.72, 4.522, 16.2]}, {"time": 4.6333, "x": -23.1, "y": 16.2, "curve": "stepped"}, {"time": 4.9, "x": -23.1, "y": 16.2, "curve": [4.967, -23.1, 5.033, -17.43, 4.967, 16.2, 5.033, 4.77]}, {"time": 5.1, "x": -16.54, "y": 4.77, "curve": "stepped"}, {"time": 5.5667, "x": -16.54, "y": 4.77, "curve": [5.722, -14.47, 5.789, 7.18, 5.722, 4.77, 5.789, 8.71]}, {"time": 5.9, "x": 5.82, "y": 8.71, "curve": "stepped"}, {"time": 6.7, "x": 5.82, "y": 8.71, "curve": [6.967, 2.58, 6.833, -25.95, 6.967, 8.71, 6.833, 1.94]}, {"time": 6.9, "x": -24.86, "y": 2.4, "curve": [7.034, -22.65, 7.169, -1.92, 7.034, 3.33, 7.169, 15.69]}, {"time": 7.3, "x": 1.04, "y": 17.04, "curve": [7.423, 3.71, 7.546, 2.3, 7.423, 18.27, 7.546, 8.82]}, {"time": 7.6667, "x": -0.44, "y": 6.83, "curve": [7.779, -2.93, 7.889, -0.16, 7.779, 5.02, 7.889, 8.09]}, {"time": 8, "x": -0.16, "y": 8.09}]}, "mouth": {"rotate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}], "translate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}]}, "L_fingeL_4": {"rotate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}], "translate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}]}, "L_fingeL_5": {"rotate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4.4667, "curve": [4.583, 0, 4.817, 9.23]}, {"time": 4.9333, "value": 9.23, "curve": [5.067, 9.23, 5.333, -15.56]}, {"time": 5.4667, "value": -15.56, "curve": [5.6, -15.56, 5.867, 0]}, {"time": 6}], "translate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4.4667, "curve": "stepped"}, {"time": 6}]}, "L_fingeL_6": {"rotate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}], "translate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}]}, "R_hand_1": {"rotate": [{"value": 7.97, "curve": [0.101, 5.75, 0.194, 4.23]}, {"time": 0.2667, "value": 4.23, "curve": [0.559, 4.23, 0.956, 22.68]}, {"time": 1.2667, "value": 22.68, "curve": [1.444, 22.68, 1.753, 13.08]}, {"time": 2, "value": 7.97, "curve": [2.101, 5.75, 2.194, 4.23]}, {"time": 2.2667, "value": 4.23, "curve": [2.559, 4.23, 2.956, 22.68]}, {"time": 3.2667, "value": 22.68, "curve": [3.444, 22.68, 3.753, 13.08]}, {"time": 4, "value": 7.97, "curve": [4.101, 5.75, 4.194, 4.23]}, {"time": 4.2667, "value": 4.23, "curve": [4.354, 4.23, 4.473, 8.1]}, {"time": 4.5667, "value": 8.1, "curve": [4.712, 8.1, 4.964, 16.11]}, {"time": 5.1667, "value": 20.39, "curve": [5.676, 20.39, 5.854, 7.97]}, {"time": 6, "value": 7.97, "curve": [6.101, 5.75, 6.194, 4.23]}, {"time": 6.2667, "value": 4.23, "curve": [6.559, 4.23, 6.956, 22.68]}, {"time": 7.2667, "value": 22.68, "curve": [7.444, 22.68, 7.753, 13.08]}, {"time": 8, "value": 7.97}], "translate": [{"x": 3.53, "y": 1.05, "curve": [0.101, 1.43, 0.194, 0, 0.101, 0.42, 0.194, 0]}, {"time": 0.2667, "curve": [0.559, 0, 0.956, 17.44, 0.559, 0, 0.956, 5.16]}, {"time": 1.2667, "x": 17.44, "y": 5.16, "curve": [1.444, 17.44, 1.753, 8.37, 1.444, 5.16, 1.753, 2.48]}, {"time": 2, "x": 3.53, "y": 1.05, "curve": [2.101, 1.43, 2.194, 0, 2.101, 0.42, 2.194, 0]}, {"time": 2.2667, "curve": [2.559, 0, 2.956, 17.44, 2.559, 0, 2.956, 5.16]}, {"time": 3.2667, "x": 17.44, "y": 5.16, "curve": [3.444, 17.44, 3.753, 8.37, 3.444, 5.16, 3.753, 2.48]}, {"time": 4, "x": 3.53, "y": 1.05, "curve": [4.101, 1.43, 4.194, 0, 4.101, 0.42, 4.194, 0]}, {"time": 4.2667, "curve": [4.354, 0, 4.473, 17.44, 4.354, 0, 4.473, 5.16]}, {"time": 4.5667, "x": 17.44, "y": 5.16, "curve": "stepped"}, {"time": 5.1667, "x": 17.44, "y": 5.16, "curve": [5.676, 17.44, 5.854, 3.53, 5.676, 5.16, 5.854, 1.05]}, {"time": 6, "x": 3.53, "y": 1.05, "curve": [6.101, 1.43, 6.194, 0, 6.101, 0.42, 6.194, 0]}, {"time": 6.2667, "curve": [6.559, 0, 6.956, 17.44, 6.559, 0, 6.956, 5.16]}, {"time": 7.2667, "x": 17.44, "y": 5.16, "curve": [7.444, 17.44, 7.753, 8.37, 7.444, 5.16, 7.753, 2.48]}, {"time": 8, "x": 3.53, "y": 1.05}]}, "R_hand_2": {"rotate": [{"value": -7.76, "curve": "stepped"}, {"time": 0.2667, "value": -7.76, "curve": "stepped"}, {"time": 1.2667, "value": -7.76, "curve": "stepped"}, {"time": 2, "value": -7.76, "curve": "stepped"}, {"time": 2.2667, "value": -7.76, "curve": "stepped"}, {"time": 3.2667, "value": -7.76, "curve": "stepped"}, {"time": 4, "value": -7.76, "curve": "stepped"}, {"time": 4.2667, "value": -7.76, "curve": "stepped"}, {"time": 4.7, "value": -7.76, "curve": [4.817, -7.76, 5.05, 2.25]}, {"time": 5.1667, "value": 2.25, "curve": [5.676, 2.25, 5.854, -7.76]}, {"time": 6, "value": -7.76, "curve": "stepped"}, {"time": 6.2667, "value": -7.76, "curve": "stepped"}, {"time": 7.2667, "value": -7.76, "curve": "stepped"}, {"time": 8, "value": -7.76}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.2667, "curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 2.2667, "curve": "stepped"}, {"time": 3.2667, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.2667, "curve": "stepped"}, {"time": 4.7, "curve": "stepped"}, {"time": 6, "curve": "stepped"}, {"time": 6.2667, "curve": "stepped"}, {"time": 7.2667, "curve": "stepped"}, {"time": 8}]}, "R_hand_3": {"rotate": [{"value": -2.05, "curve": "stepped"}, {"time": 0.2667, "value": -2.05, "curve": "stepped"}, {"time": 1.2667, "value": -2.05, "curve": "stepped"}, {"time": 2, "value": -2.05, "curve": "stepped"}, {"time": 2.2667, "value": -2.05, "curve": "stepped"}, {"time": 3.2667, "value": -2.05, "curve": "stepped"}, {"time": 4, "value": -2.05, "curve": "stepped"}, {"time": 4.2667, "value": -2.05, "curve": "stepped"}, {"time": 4.7, "value": -2.05, "curve": [4.817, -2.05, 5.05, -14.9]}, {"time": 5.1667, "value": -14.9, "curve": [5.676, -14.9, 5.854, -2.05]}, {"time": 6, "value": -2.05, "curve": "stepped"}, {"time": 6.2667, "value": -2.05, "curve": "stepped"}, {"time": 7.2667, "value": -2.05, "curve": "stepped"}, {"time": 8, "value": -2.05}], "translate": [{"x": -5.01, "y": 2.53, "curve": "stepped"}, {"time": 0.2667, "x": -5.01, "y": 2.53, "curve": "stepped"}, {"time": 1.2667, "x": -5.01, "y": 2.53, "curve": "stepped"}, {"time": 2, "x": -5.01, "y": 2.53, "curve": "stepped"}, {"time": 2.2667, "x": -5.01, "y": 2.53, "curve": "stepped"}, {"time": 3.2667, "x": -5.01, "y": 2.53, "curve": "stepped"}, {"time": 4, "x": -5.01, "y": 2.53, "curve": "stepped"}, {"time": 4.2667, "x": -5.01, "y": 2.53, "curve": "stepped"}, {"time": 4.7, "x": -5.01, "y": 2.53, "curve": "stepped"}, {"time": 6, "x": -5.01, "y": 2.53, "curve": "stepped"}, {"time": 6.2667, "x": -5.01, "y": 2.53, "curve": "stepped"}, {"time": 7.2667, "x": -5.01, "y": 2.53, "curve": "stepped"}, {"time": 8, "x": -5.01, "y": 2.53}]}, "R_arm_1": {"rotate": [{"value": 6.14, "curve": "stepped"}, {"time": 0.2667, "value": 6.14, "curve": "stepped"}, {"time": 1.2667, "value": 6.14, "curve": "stepped"}, {"time": 2, "value": 6.14, "curve": "stepped"}, {"time": 2.2667, "value": 6.14, "curve": "stepped"}, {"time": 3.2667, "value": 6.14, "curve": "stepped"}, {"time": 4, "value": 6.14, "curve": "stepped"}, {"time": 4.2667, "value": 6.14, "curve": "stepped"}, {"time": 6.2667, "value": 6.14, "curve": "stepped"}, {"time": 7.2667, "value": 6.14, "curve": "stepped"}, {"time": 8, "value": 6.14}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.2667, "curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 2.2667, "curve": "stepped"}, {"time": 3.2667, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 4.2667, "curve": "stepped"}, {"time": 6.2667, "curve": "stepped"}, {"time": 7.2667, "curve": "stepped"}, {"time": 8}]}, "R_finger_1": {"rotate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}], "translate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}]}, "R_finger_2": {"rotate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}], "translate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}]}, "R_finger_3": {"rotate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}], "translate": [{"curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 4, "curve": "stepped"}, {"time": 6}]}, "tail base": {"rotate": [{"time": 1.4667, "curve": [1.897, -0.49, 2.204, -25.15]}, {"time": 2.3, "value": -24.98, "curve": [2.995, -23.68, 2.563, 6.07]}, {"time": 3.2667, "value": 6.07, "curve": [3.478, 6.07, 3.573, 3.34]}, {"time": 3.9, "value": 3.15, "curve": [4.533, 2.77, 5.167, 0.72]}, {"time": 5.8, "curve": [6.23, -0.49, 6.537, -25.15]}, {"time": 6.6333, "value": -24.98, "curve": [7.329, -23.68, 6.897, 6.07]}, {"time": 7.6, "value": 6.07, "curve": [7.811, 6.07, 8.022, 0.24]}, {"time": 8.2333}]}}, "transform": {"L_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}], "R_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}]}, "path": {"L_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0}]}, "R_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0}]}}, "attachments": {"default": {"head": {"monkey new/head": {"deform": [{"time": 2.9667, "curve": [3, 0, 3.04, 1]}, {"time": 3.0667, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": "stepped"}, {"time": 3.2667, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": [3.275, 0, 3.333, 1]}, {"time": 3.3667, "curve": "stepped"}, {"time": 4.0333, "curve": [4.067, 0, 4.107, 1]}, {"time": 4.1333, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": "stepped"}, {"time": 4.3333, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": [4.342, 0, 4.4, 1]}, {"time": 4.4333, "curve": "stepped"}, {"time": 5.5, "curve": [5.533, 0, 5.573, 1]}, {"time": 5.6, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": "stepped"}, {"time": 5.8, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": [5.808, 0, 5.867, 1]}, {"time": 5.9, "curve": "stepped"}, {"time": 6.8333, "curve": [6.867, 0, 6.907, 1]}, {"time": 6.9333, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": "stepped"}, {"time": 7.1333, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": [7.142, 0, 7.2, 1]}, {"time": 7.2333}]}}, "monkey_mouth smile open": {"monkey new/monkey_mouth smile open": {"deform": [{"time": 4.4667, "offset": 20, "vertices": [4.8644, 0.27689, 4.86434, 0.27689, 6.2628, -0.85142, 6.2627, -0.85142, 3.26963, -0.47106, 3.26947, -0.4711, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34415, 0.36221, -5.34421, 0.36221, -0.62111, -0.24179, -0.62134, -0.24177, 2.31476, -2.31107, 2.31456, -2.31105, 1.54562, -1.58092, 1.54558, -1.58093, 0, 0, 0, 0, 3.15429, -2.45897, 3.15425, -2.45897, 13.57594, -8.54459, 13.57582, -8.54461, 29.8631, -12.59916, 29.8629, -12.59918, 37.34046, -10.11008, 37.34019, -10.11013, 39.27617, -5.37841, 39.27599, -5.37841, 38.10587, -2.64022, 38.10558, -2.64022, 32.29321, 0.99618, 32.2929, 0.99617, 20.97192, 2.91691, 20.97177, 2.91693, 8.5104, 1.81163, 8.51024, 1.81162, 2.11884, 0.49203, 2.11881, 0.49204, 4.07748, 1.15525, 4.07738, 1.15522, 0.20969, 0.9987, 0.20964, 0.99868], "curve": [4.544, 0, 4.622, 1]}, {"time": 4.7, "offset": 8, "vertices": [-1.28351, -0.52665, -1.28351, -0.52659, -3.83746, -1.57442, -3.8375, -1.57436, -3.76192, 1.04626, -3.76199, 1.04635, -10.57374, -8.1484, -10.57376, -8.14831, -10.57374, -8.1484, -10.57376, -8.14831, -7.09026, -5.46395, -7.09029, -5.46386, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.44277, 0.04588, 0.44277, 0.04589, 2.88429, 0.78514, 2.88429, 0.7852, 0.7377, 1.49104, 0.73769, 1.49109, 0, 0, 0, 0, 0.22753, 3.67193, 0.2275, 3.67196, 0.17809, 11.74496, 0.17805, 11.74501, -5.59586, 10.92609, -5.59589, 10.92618, -12.27477, 10.97721, -12.27477, 10.97731, -16.42873, 5.47097, -16.42877, 5.47107, -18.42986, -5.86831, -18.42994, -5.86811, -15.80939, -16.45662, -15.80943, -16.45621, -9.02964, -16.01102, -9.02982, -16.0108, -4.58274, -13.48683, -4.58289, -13.48662, -2.28416, -10.05, -2.28421, -10.04985, -1.84745, -8.78892, -1.84754, -8.78885, -0.12933, -0.73415, -0.12939, -0.73405], "curve": "stepped"}, {"time": 5.6333, "offset": 8, "vertices": [-1.28351, -0.52665, -1.28351, -0.52659, -3.83746, -1.57442, -3.8375, -1.57436, -3.76192, 1.04626, -3.76199, 1.04635, -10.57374, -8.1484, -10.57376, -8.14831, -10.57374, -8.1484, -10.57376, -8.14831, -7.09026, -5.46395, -7.09029, -5.46386, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.44277, 0.04588, 0.44277, 0.04589, 2.88429, 0.78514, 2.88429, 0.7852, 0.7377, 1.49104, 0.73769, 1.49109, 0, 0, 0, 0, 0.22753, 3.67193, 0.2275, 3.67196, 0.17809, 11.74496, 0.17805, 11.74501, -5.59586, 10.92609, -5.59589, 10.92618, -12.27477, 10.97721, -12.27477, 10.97731, -16.42873, 5.47097, -16.42877, 5.47107, -18.42986, -5.86831, -18.42994, -5.86811, -15.80939, -16.45662, -15.80943, -16.45621, -9.02964, -16.01102, -9.02982, -16.0108, -4.58274, -13.48683, -4.58289, -13.48662, -2.28416, -10.05, -2.28421, -10.04985, -1.84745, -8.78892, -1.84754, -8.78885, -0.12933, -0.73415, -0.12939, -0.73405]}, {"time": 5.8333}]}}}}, "drawOrder": [{}]}, "InfoScreen": {"slots": {"banana_2_1": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_2_1"}]}, "banana_2_2": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_2_2"}]}, "banana_3_2": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_3_2"}]}, "banana_3_3": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_3_3"}]}, "monkeyOLD_banana_1": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_1"}]}, "monkeyOLD_banana_3_1": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "banana_3_1"}]}, "monkey_left_arm": {"attachment": [{}]}, "monkey_left_hand": {"attachment": [{}]}, "monkey_left_hand closed": {"attachment": [{}]}, "monkey_mouth angry": {"attachment": [{}]}, "monkey_mouth o": {"attachment": [{}]}, "monkey_mouth smile closed": {"attachment": [{}]}, "monkey_mouth smile open": {"attachment": [{"name": "monkey new/monkey_mouth smile open"}]}, "monkey_right_hand": {"attachment": [{}]}}, "bones": {"Banana": {"rotate": [{"value": -1.53}], "translate": [{"x": 7.65, "y": -12.69}], "scale": [{}]}, "eyes": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Emotions": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "L_hand_5": {"rotate": [{"value": -63.19}], "translate": [{"x": -6.23, "y": -18.72}], "scale": [{}]}, "L_hand_6": {"rotate": [{"value": -60.19}], "translate": [{"x": -29.6, "y": -32.99}], "scale": [{}]}, "L_hand_4": {"rotate": [{"value": -30.91}], "translate": [{"x": 14.31, "y": -49.84}], "scale": [{}]}, "L_arm_2": {"rotate": [{"value": 134.76}], "translate": [{"x": -59.71, "y": 19.43}], "scale": [{}]}, "R_leg2": {"rotate": [{"value": 3.08}], "translate": [{"x": -1.33, "y": 1.33}], "scale": [{}]}, "R_hand_3": {"rotate": [{"value": -2.05}], "translate": [{"x": -5.01, "y": 2.53}], "scale": [{}]}, "face_v": {"rotate": [{}], "translate": [{"x": 52.52, "y": -0.84}], "scale": [{}]}, "CHEST": {"rotate": [{"value": -4.77}], "translate": [{"x": 22.05, "y": -3.49}], "scale": [{}]}, "SPINE": {"rotate": [{"value": -2.99}], "translate": [{}], "scale": [{}]}, "R_leg": {"rotate": [{"value": -7.07}], "translate": [{"x": 10.62, "y": -1.33}], "scale": [{}]}, "R_brow": {"rotate": [{}], "translate": [{"x": 42.47, "y": 4.77}], "scale": [{}]}, "R_hand_1": {"rotate": [{"value": 21}], "translate": [{"x": 11.98, "y": 39.27}], "scale": [{}]}, "HEAD": {"rotate": [{"value": 9.97}], "translate": [{"x": 9.03, "y": -1.69}], "scale": [{}]}, "R_arm_1": {"rotate": [{"value": 6.14}], "translate": [{"x": 25.89, "y": 6.1}], "scale": [{}]}, "L_brow": {"rotate": [{}], "translate": [{"x": 42.47, "y": 4.77}], "scale": [{}]}, "face_h": {"rotate": [{}], "translate": [{"y": 15.56}], "scale": [{}]}, "R_eye": {"rotate": [{}], "translate": [{"x": -12.25, "y": 1.85}], "scale": [{}]}, "L_eye": {"rotate": [{}], "translate": [{"x": -12.25, "y": 1.85}], "scale": [{}]}, "R_hand_2": {"rotate": [{"value": -7.76}], "translate": [{}], "scale": [{}]}, "R_foot": {"rotate": [{"value": 13.84}], "translate": [{}], "scale": [{}]}, "R_foot_finger_5": {"rotate": [{"value": 9.66}], "translate": [{}], "scale": [{}]}, "L_fingeL_6": {"rotate": [{"value": -21.58}], "translate": [{}], "scale": [{}]}, "R_finger_2": {"rotate": [{"value": 8.89}], "translate": [{}], "scale": [{}]}, "R_foot2": {"rotate": [{"value": 8.75}], "translate": [{}], "scale": [{}]}, "L_fingeL_5": {"rotate": [{"value": -6.64}], "translate": [{}], "scale": [{}]}, "main_C": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "R_finger_1": {"rotate": [{"value": 13.08}], "translate": [{}], "scale": [{}]}, "R_foot_finger_2": {"rotate": [{"value": 13.46}], "translate": [{}], "scale": [{}]}, "L_fingeL_4": {"rotate": [{"value": 7.87}], "translate": [{}], "scale": [{}]}, "mouth": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "R_hand_c_2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "L_hand_c_2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "banana_c": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "R_hand_c_1": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "L_hand_c_1": {"rotate": [{}], "translate": [{}], "scale": [{}]}}, "transform": {"L_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}], "R_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}]}, "path": {"L_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0}]}, "R_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0}]}}, "drawOrder": [{}]}, "Lost_banana": {"slots": {"banana_2_1": {"rgba": [{"color": "ffffff00"}], "attachment": [{}, {"time": 0.2667}, {"time": 1.8}, {"time": 2.0667}]}, "banana_2_2": {"rgba": [{"color": "ffffff00"}], "attachment": [{}, {"time": 0.2667}, {"time": 1.8}, {"time": 2.0667}]}, "banana_3_2": {"rgba": [{"color": "ffffff00"}], "attachment": [{}, {"time": 0.2667}, {"time": 1.8}, {"time": 2.0667}]}, "banana_3_3": {"rgba": [{"color": "ffffff00"}], "attachment": [{}, {"time": 0.2667}, {"time": 1.8}, {"time": 2.0667}]}, "closed_eye_left": {"attachment": [{"time": 0.1333, "name": "monkey new/closed_eye_left"}, {"time": 0.2667}, {"time": 1.2, "name": "monkey new/closed_eye_left"}, {"time": 1.4}]}, "closed_eye_right": {"attachment": [{"time": 0.1333, "name": "monkey new/closed_eye_right"}, {"time": 0.2667}, {"time": 1.2, "name": "monkey new/closed_eye_right"}, {"time": 1.4}]}, "monkeyOLD_banana_1": {"rgba": [{"color": "ffffff00"}], "attachment": [{}, {"time": 0.2667}, {"time": 1.8}, {"time": 2.0667}]}, "monkeyOLD_banana_3_1": {"rgba": [{"color": "ffffff00"}], "attachment": [{}, {"time": 0.2667}, {"time": 1.8}, {"time": 2.0667}]}, "monkey_eyebrow left": {"alpha": [{"time": 2.0667, "value": 1, "curve": [2.089, 1, 2.111, 0]}, {"time": 2.1333, "value": 0, "curve": "stepped"}, {"time": 2.2333, "value": 0}]}, "monkey_eyebrow right": {"alpha": [{"time": 2.0667, "value": 1, "curve": [2.089, 1, 2.111, 0]}, {"time": 2.1333, "value": 0, "curve": "stepped"}, {"time": 2.2333, "value": 0}]}, "monkey_eyebrow_angry": {"alpha": [{"value": 0, "curve": "stepped"}, {"time": 2.0667, "value": 0, "curve": [2.122, 0, 2.178, 1]}, {"time": 2.2333, "value": 1}], "attachment": [{"time": 2.0667, "name": "monkey new/monkey_eyebrow_angry"}]}, "monkey_hand left": {"attachment": [{}]}, "monkey_hand right": {"attachment": [{}]}, "monkey_left_hand": {"attachment": [{"time": 2.3}]}, "monkey_left_hand closed": {"attachment": [{}, {"time": 2.3, "name": "monkey new/monkey_left_hand closed"}]}, "monkey_mouth angry": {"attachment": [{}, {"time": 2.0667, "name": "monkey new/monkey_mouth angry"}]}, "monkey_mouth o": {"attachment": [{"name": "monkey new/monkey_mouth o"}, {"time": 2.0667}]}, "monkey_mouth smile closed": {"attachment": [{}]}, "monkey_mouth smile open": {"attachment": [{}]}, "monkey_right_hand": {"attachment": [{"time": 2.3}]}}, "bones": {"Banana": {"rotate": [{"value": 0.03, "curve": "stepped"}, {"time": 1.8, "value": 0.03}], "translate": [{"x": 6.4, "y": -7.09, "curve": "stepped"}, {"time": 1.8, "x": 6.4, "y": -7.09}]}, "bone": {"rotate": [{"time": 1.1, "curve": [1.233, 0, 1.367, -25.32]}, {"time": 1.5, "value": -25.32, "curve": [1.644, -25.32, 1.789, 0]}, {"time": 1.9333}]}, "root": {"translate": [{}]}, "main_C": {"rotate": [{"curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 2.2667}], "translate": [{"curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": "stepped"}, {"time": 2, "curve": [2.015, 0, 2.068, 0, 2.015, 9.99, 2.068, 52.2]}, {"time": 2.1333, "y": 52.2, "curve": [2.184, 0, 2.245, 0, 2.184, 52.2, 2.245, 15.24]}, {"time": 2.2667, "curve": "stepped"}, {"time": 3.4, "curve": [3.415, 0, 3.468, 0, 3.415, 9.99, 3.468, 52.2]}, {"time": 3.5333, "y": 52.2, "curve": [3.584, 0, 3.645, 0, 3.584, 52.2, 3.645, 15.24]}, {"time": 3.6667}]}, "L_eye": {"translate": [{"x": 5.56, "y": 2.5, "curve": [0.033, -0.02, 0.067, -11.17, 0.033, -1.19, 0.067, -8.58]}, {"time": 0.1, "x": -11.17, "y": -8.58, "curve": [0.178, -11.17, 0.256, -10.25, 0.178, -8.58, 0.256, 2.76]}, {"time": 0.3333, "x": -10.25, "y": 5.22, "curve": [0.411, -10.25, 0.489, -12.63, 0.411, 7.69, 0.489, 7.69]}, {"time": 0.5667, "x": -15.93, "y": 7.69, "curve": [0.578, -16.4, 0.589, -21.55, 0.578, 7.69, 0.589, -7.55]}, {"time": 0.6, "x": -21.55, "y": -7.55, "curve": [0.756, -21.55, 0.911, -17.94, 0.756, -7.55, 0.911, -7.55]}, {"time": 1.0667, "x": -13.16, "y": -6.68, "curve": [1.244, -7.69, 1.422, 3.09, 1.244, -5.69, 1.422, 5.39]}, {"time": 1.6, "x": 9.2, "y": 5.39, "curve": [1.667, 11.49, 1.733, 9.98, 1.667, 5.39, 1.733, -1.26]}, {"time": 1.8, "x": 12.05, "y": -1.26, "curve": [1.856, 13.78, 1.911, 20.61, 1.856, -1.26, 1.911, 0.25]}, {"time": 1.9667, "x": 20.61, "y": 1.17, "curve": [2.011, 20.61, 2.056, -5.45, 2.011, 1.91, 2.056, 3.69]}, {"time": 2.1, "x": -11.08, "y": 3.69, "curve": [2.144, -16.71, 2.189, -16.71, 2.144, 3.69, 2.189, 1.36]}, {"time": 2.2333, "x": -16.71, "y": 0.66, "curve": [2.267, -16.71, 2.3, 0, 2.267, 0.13, 2.3, 0]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.5, "curve": [2.533, 0, 2.567, -0.24, 2.533, 0, 2.567, 7.44]}, {"time": 2.6, "x": -0.24, "y": 7.44, "curve": "stepped"}, {"time": 2.7, "x": -0.24, "y": 7.44, "curve": [2.733, -0.24, 2.767, -0.25, 2.733, 7.44, 2.767, -14.88]}, {"time": 2.8, "x": -0.25, "y": -14.88, "curve": [2.856, -0.25, 2.911, 3.19, 2.856, -14.88, 2.911, -13.28]}, {"time": 2.9667, "x": 3.19, "y": -8.03, "curve": [3, 3.19, 3.033, 3.19, 3, -4.89, 3.033, 10.28]}, {"time": 3.0667, "x": 2.79, "y": 10.28, "curve": [3.111, 2.26, 3.156, 0.16, 3.111, 10.28, 3.156, -0.01]}, {"time": 3.2, "x": -0.64, "y": -0.01, "curve": [3.233, -1.24, 3.267, -1.42, 3.233, -0.01, 3.267, 2.44]}, {"time": 3.3, "x": -1.42, "y": 2.44, "curve": [3.344, -1.42, 3.389, -1.42, 3.344, 2.44, 3.389, -9.92]}, {"time": 3.4333, "x": -1.09, "y": -9.92, "curve": [3.467, -0.85, 3.5, 12.49, 3.467, -9.92, 3.5, -6.09]}, {"time": 3.5333, "x": 12.49, "y": -6.09, "curve": [3.589, 12.49, 3.644, -2.01, 3.589, -6.09, 3.644, -7.27]}, {"time": 3.7, "x": -2.01, "y": -7.27, "curve": [3.8, -2.01, 3.9, 3.91, 3.8, -7.27, 3.9, -1.63]}, {"time": 4, "x": 3.91, "y": -1.63, "curve": [4.156, 3.91, 4.311, 0.15, 4.156, -1.63, 4.311, -2.18]}, {"time": 4.4667, "x": -1.73, "y": -2.45}]}, "R_brow": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": [0.267, 0, 0.299, -1.57]}, {"time": 0.4667, "value": -1.57, "curve": "stepped"}, {"time": 0.6, "value": -1.57, "curve": "stepped"}, {"time": 1.0667, "value": -1.57, "curve": "stepped"}, {"time": 1.2333, "value": -1.57, "curve": [1.297, -1.57, 1.311, -7.91]}, {"time": 1.4667, "value": -7.88, "curve": "stepped"}, {"time": 1.6333, "value": -7.88, "curve": "stepped"}, {"time": 1.8, "value": -7.88, "curve": [1.867, -7.88, 2, 5.09]}, {"time": 2.0667, "value": 5.09, "curve": [2.142, 5.09, 2.187, 26.19]}, {"time": 2.3667, "value": 26.19}], "translate": [{"x": -35.16, "y": 9.8, "curve": [0.069, -35.16, 0.136, -24.43, 0.069, 9.8, 0.136, 33.61]}, {"time": 0.2, "x": -24.43, "y": 33.61, "curve": [0.267, -24.43, 0.299, 22.19, 0.267, 33.61, 0.299, -26.7]}, {"time": 0.4667, "x": 22.19, "y": -26.7, "curve": "stepped"}, {"time": 0.6, "x": 22.19, "y": -26.7, "curve": [0.717, 22.19, 0.95, 22.73, 0.717, -26.7, 0.95, 51.9]}, {"time": 1.0667, "x": 22.73, "y": 51.9, "curve": "stepped"}, {"time": 1.2333, "x": 22.73, "y": 51.9, "curve": [1.297, 22.73, 1.311, 33.68, 1.297, 51.9, 1.311, 7.28]}, {"time": 1.4667, "x": 33.63, "y": 7.51, "curve": "stepped"}, {"time": 1.6333, "x": 33.63, "y": 7.51, "curve": "stepped"}, {"time": 1.8, "x": 33.63, "y": 7.51, "curve": [1.841, 33.63, 1.907, 13.2, 1.841, 7.51, 1.907, 10.48]}, {"time": 1.9667, "x": -2.12, "y": 12.7, "curve": [2.005, 22.43, 2.041, 42.06, 2.005, 15.91, 2.041, 18.47]}, {"time": 2.0667, "x": 42.06, "y": 18.47, "curve": [2.102, 42.06, 2.132, 22.78, 2.102, 18.47, 2.132, 19.59]}, {"time": 2.1667, "x": -2.93, "y": 21.08, "curve": [2.21, -58.16, 2.262, -141.01, 2.21, 24.75, 2.262, 30.25]}, {"time": 2.3667, "x": -141.01, "y": 30.25}]}, "R_hand_1": {"rotate": [{"value": -5.33, "curve": [0.1, 5.1, 0.2, 25.96]}, {"time": 0.3, "value": 25.96, "curve": "stepped"}, {"time": 0.6333, "value": 25.96, "curve": [0.811, 25.96, 0.989, 14.75]}, {"time": 1.1667, "value": 14.75, "curve": [1.278, 14.75, 1.389, 30.88]}, {"time": 1.5, "value": 30.88, "curve": [1.6, 30.88, 1.7, 26.45]}, {"time": 1.8, "value": 26.45, "curve": [1.889, 26.45, 1.978, 82.69]}, {"time": 2.0667, "value": 82.69, "curve": [2.156, 82.69, 2.244, 15.44]}, {"time": 2.3333, "value": 15.44, "curve": [2.522, 15.44, 2.711, 71.76]}, {"time": 2.9, "value": 71.76, "curve": "stepped"}, {"time": 3.2333, "value": 71.76, "curve": [3.311, 71.76, 3.389, 126.35]}, {"time": 3.4667, "value": 126.35, "curve": [3.556, 126.35, 3.644, 10.24]}, {"time": 3.7333, "value": 10.24, "curve": [3.844, 10.24, 3.956, 27.55]}, {"time": 4.0667, "value": 36.21}], "translate": [{"x": 2.03, "y": 11.13, "curve": [0.1, -1.13, 0.2, -7.46, 0.1, 14.17, 0.2, 20.25]}, {"time": 0.3, "x": -7.46, "y": 20.25, "curve": "stepped"}, {"time": 0.6333, "x": -7.46, "y": 20.25, "curve": [0.922, -7.46, 1.211, 10.53, 0.922, 20.25, 1.211, 37.38]}, {"time": 1.5, "x": 10.53, "y": 37.38, "curve": [1.6, 10.53, 1.7, -7.46, 1.6, 37.38, 1.7, 20.25]}, {"time": 1.8, "x": -7.46, "y": 20.25, "curve": [1.889, -7.46, 1.978, -7.46, 1.889, 20.25, 1.978, 22.24]}, {"time": 2.0667, "x": -4.9, "y": 25.36, "curve": [2.156, -2.35, 2.244, 34.02, 2.156, 28.47, 2.244, 38.94]}, {"time": 2.3333, "x": 34.02, "y": 38.94, "curve": [2.633, 34.02, 2.933, 34.02, 2.633, 38.94, 2.933, 38.94]}, {"time": 3.2333, "x": 34.02, "y": 38.94, "curve": [3.311, 34.02, 3.389, 34.02, 3.311, 38.94, 3.389, 38.94]}, {"time": 3.4667, "x": 34.02, "y": 38.94, "curve": [3.556, 34.02, 3.644, 34.02, 3.556, 38.94, 3.644, 38.94]}, {"time": 3.7333, "x": 34.02, "y": 38.94}]}, "R_arm_1": {"rotate": [{"value": 124.96, "curve": [0.049, 95.66, 0.101, 61.46]}, {"time": 0.1333, "value": 61.46, "curve": [0.2, 61.46, 0.217, -57.81]}, {"time": 0.4, "value": -57.81, "curve": "stepped"}, {"time": 0.6333, "value": -57.81, "curve": [0.767, -57.81, 0.826, -30.92]}, {"time": 1.1667, "value": -31.05, "curve": "stepped"}, {"time": 1.5, "value": -31.05, "curve": "stepped"}, {"time": 1.8, "value": -31.05, "curve": "stepped"}, {"time": 2.0667, "value": -31.05, "curve": [2.1, -31.05, 2.15, 6.39]}, {"time": 2.2, "value": 43.82, "curve": [2.25, 87.19, 2.3, 130.57]}, {"time": 2.3333, "value": 130.57, "curve": "stepped"}, {"time": 3.2333, "value": 130.57, "curve": "stepped"}, {"time": 3.4667, "value": 130.57, "curve": "stepped"}, {"time": 3.7333, "value": 130.57}], "translate": [{"x": -47.48, "y": 26.73, "curve": [0.049, -46.04, 0.101, -44.37, 0.049, 45.94, 0.101, 68.35]}, {"time": 0.1333, "x": -44.37, "y": 68.35, "curve": [0.2, -44.37, 0.217, -1.54, 0.2, 68.35, 0.217, -40.7]}, {"time": 0.4, "x": -1.54, "y": -40.7, "curve": "stepped"}, {"time": 0.6333, "x": -1.54, "y": -40.7, "curve": [0.767, -1.54, 0.826, 20.39, 0.767, -40.7, 0.826, -7.77]}, {"time": 1.1667, "x": 20.28, "y": -7.94, "curve": "stepped"}, {"time": 1.5, "x": 20.28, "y": -7.94, "curve": "stepped"}, {"time": 1.8, "x": 20.28, "y": -7.94, "curve": "stepped"}, {"time": 2.0667, "x": 20.28, "y": -7.94, "curve": [2.1, 20.28, 2.15, 1.45, 2.1, -7.94, 2.15, 23.32]}, {"time": 2.2, "x": -17.39, "y": 54.58, "curve": [2.25, -18.69, 2.3, -19.98, 2.25, 37.32, 2.3, 20.05]}, {"time": 2.3333, "x": -19.98, "y": 20.05, "curve": "stepped"}, {"time": 3.2333, "x": -19.98, "y": 20.05, "curve": "stepped"}, {"time": 3.4667, "x": -19.98, "y": 20.05, "curve": "stepped"}, {"time": 3.7333, "x": -19.98, "y": 20.05}]}, "R_finger_1": {"rotate": [{"curve": [0.069, 0, 0.148, 10.87]}, {"time": 0.2333, "value": 29.72, "curve": [0.275, 29.72, 0.358, -20.07]}, {"time": 0.4, "value": -20.07, "curve": [0.442, -20.07, 0.525, 0]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": [1.842, 0, 1.925, -3.02]}, {"time": 1.9667, "value": -3.02, "curve": [2.033, -3.02, 2.167, 26.59]}, {"time": 2.2333, "value": 26.59}], "translate": [{"curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": "stepped"}, {"time": 2.1, "curve": [2.133, 0, 2.2, -7.23, 2.133, 0, 2.2, -11.77]}, {"time": 2.2333, "x": -7.23, "y": -11.77}]}, "R_foot": {"rotate": [{"value": 3.59, "curve": [0.183, 3.59, 0.55, 0]}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": [1.892, 0, 2.075, -11.6]}, {"time": 2.1667, "value": -11.6, "curve": [2.208, -11.6, 2.28, 21.36]}, {"time": 2.3333, "value": 21.36, "curve": [2.367, 21.36, 2.424, -8.79]}, {"time": 2.4667, "value": -8.79, "curve": [2.5, -8.79, 2.557, 0]}, {"time": 2.6, "curve": "stepped"}, {"time": 2.6667, "curve": [2.683, 0, 2.717, -23.58]}, {"time": 2.7333, "value": -23.58, "curve": [2.775, -23.58, 2.858, 8.67]}, {"time": 2.9, "value": 8.67, "curve": [2.925, 8.67, 2.975, -23.58]}, {"time": 3, "value": -23.58, "curve": [3.042, -23.58, 3.125, 8.67]}, {"time": 3.1667, "value": 8.67, "curve": [3.192, 8.67, 3.242, -23.58]}, {"time": 3.2667, "value": -23.58, "curve": [3.308, -23.58, 3.392, 8.67]}, {"time": 3.4333, "value": 8.67, "curve": [3.458, 8.67, 3.508, -23.58]}, {"time": 3.5333, "value": -23.58, "curve": [3.583, -23.58, 3.683, 8.67]}, {"time": 3.7333, "value": 8.67}], "translate": [{"curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": "stepped"}, {"time": 2.6667}]}, "R_finger_2": {"rotate": [{"curve": [0.079, 0, 0.169, 10.87]}, {"time": 0.2667, "value": 29.72, "curve": [0.308, 29.72, 0.392, -19.81]}, {"time": 0.4333, "value": -19.81, "curve": [0.475, -19.81, 0.558, 3.86]}, {"time": 0.6, "value": 3.86, "curve": [0.633, 3.86, 0.7, 0]}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": [1.842, 0, 1.925, 9.3]}, {"time": 1.9667, "value": 9.3, "curve": [2.033, 9.3, 2.167, 10.15]}, {"time": 2.2333, "value": 10.15}], "translate": [{"curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": "stepped"}, {"time": 2.1, "curve": [2.133, 0, 2.2, -12.17, 2.133, 0, 2.2, -20.15]}, {"time": 2.2333, "x": -12.17, "y": -20.15}]}, "L_fingeL_5": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": [0.508, 0, 0.658, 18.94]}, {"time": 0.7333, "value": 18.94, "curve": [0.783, 18.94, 0.883, -14.12]}, {"time": 0.9333, "value": -14.12, "curve": [0.983, -14.12, 1.083, 14.99]}, {"time": 1.1333, "value": 14.99, "curve": [1.3, 14.99, 1.633, 0]}, {"time": 1.8, "curve": [1.842, 0, 1.925, 15.19]}, {"time": 1.9667, "value": 15.19, "curve": [2, 15.19, 2.067, -16.53]}, {"time": 2.1, "value": -16.53, "curve": [2.134, -16.53, 2.182, -4.39]}, {"time": 2.2333, "value": 10.18}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 1.8, "curve": "stepped"}, {"time": 2.1, "curve": [2.133, 0, 2.2, -3.87, 2.133, 0, 2.2, -22.21]}, {"time": 2.2333, "x": -3.87, "y": -22.21}]}, "L_hand_4": {"rotate": [{"value": -11.84, "curve": [0.1, -12.43, 0.2, -12.68]}, {"time": 0.3, "value": -13.63, "curve": [0.578, -16.25, 0.856, -38.34]}, {"time": 1.1333, "value": -38.34, "curve": [1.256, -38.34, 1.378, -4.34]}, {"time": 1.5, "value": -4.34, "curve": [1.6, -4.34, 1.7, -4.34]}, {"time": 1.8, "value": -16.33, "curve": [1.889, -26.98, 1.978, -82.88]}, {"time": 2.0667, "value": -82.88, "curve": [2.156, -82.88, 2.244, -11.01]}, {"time": 2.3333, "value": -11.01, "curve": [2.522, -11.01, 2.711, -64.93]}, {"time": 2.9, "value": -64.93, "curve": "stepped"}, {"time": 3.2333, "value": -64.93, "curve": [3.311, -64.93, 3.389, -121.8]}, {"time": 3.4667, "value": -121.8, "curve": [3.556, -121.8, 3.644, -4.63]}, {"time": 3.7333, "value": -4.63, "curve": [3.844, -4.63, 3.956, -20.99]}, {"time": 4.0667, "value": -29.17}], "translate": [{"x": -9.92, "y": -31.4, "curve": [0.1, -9.21, 0.2, -7.81, 0.1, -25.92, 0.2, -14.96]}, {"time": 0.3, "x": -7.81, "y": -14.96, "curve": [0.578, -7.81, 0.856, -22.95, 0.578, -14.96, 0.856, -35.25]}, {"time": 1.1333, "x": -22.95, "y": -35.25, "curve": [1.256, -22.95, 1.378, -3.32, 1.256, -35.25, 1.378, -47.06]}, {"time": 1.5, "x": -3.32, "y": -47.06, "curve": [1.6, -3.32, 1.7, -14.92, 1.6, -47.06, 1.7, -11.11]}, {"time": 1.8, "x": -21.23, "y": -11.11, "curve": [1.889, -26.85, 1.978, -51.85, 1.889, -11.11, 1.978, -41.44]}, {"time": 2.0667, "x": -51.85, "y": -42.61, "curve": [2.156, -51.85, 2.244, -11.86, 2.156, -43.78, 2.244, -18.15]}, {"time": 2.3333, "x": -11.86, "y": -18.15, "curve": [2.633, -11.86, 2.933, -11.86, 2.633, -18.15, 2.933, -18.15]}, {"time": 3.2333, "x": -11.86, "y": -18.15, "curve": [3.311, -11.86, 3.389, -25.58, 3.311, -18.15, 3.389, -19.86]}, {"time": 3.4667, "x": -25.58, "y": -19.86, "curve": [3.556, -25.58, 3.644, -16.43, 3.556, -19.86, 3.644, -18.72]}, {"time": 3.7333, "x": -11.86, "y": -18.15}]}, "CHEST": {"rotate": [{"value": -1.73, "curve": "stepped"}, {"time": 0.3667, "value": -1.73, "curve": "stepped"}, {"time": 0.6, "value": -1.73, "curve": "stepped"}, {"time": 0.8333, "value": -1.73, "curve": "stepped"}, {"time": 1.0667, "value": -1.73, "curve": "stepped"}, {"time": 1.1667, "value": -1.73, "curve": [1.267, -1.73, 1.288, -4.14]}, {"time": 1.5333, "value": -4.12, "curve": "stepped"}, {"time": 1.7333, "value": -4.12, "curve": "stepped"}, {"time": 1.8, "value": -4.12, "curve": [1.85, -4.12, 1.95, -6.12]}, {"time": 2, "value": -6.12, "curve": [2.092, -6.12, 2.275, -2.89]}, {"time": 2.3667, "value": -2.89, "curve": [2.4, -2.89, 2.467, -6.22]}, {"time": 2.5, "value": -6.22, "curve": [2.525, -6.22, 2.575, -4.78]}, {"time": 2.6, "value": -4.78, "curve": [2.625, -4.78, 2.675, -10.79]}, {"time": 2.7, "value": -10.79, "curve": [2.733, -10.79, 2.8, -1.14]}, {"time": 2.8333, "value": -1.14, "curve": [2.867, -1.14, 2.933, -10.79]}, {"time": 2.9667, "value": -10.79, "curve": [3, -10.79, 3.067, -1.14]}, {"time": 3.1, "value": -1.14, "curve": [3.133, -1.14, 3.2, -10.79]}, {"time": 3.2333, "value": -10.79, "curve": [3.267, -10.79, 3.333, -1.14]}, {"time": 3.3667, "value": -1.14, "curve": [3.4, -1.14, 3.467, -10.79]}, {"time": 3.5, "value": -10.79, "curve": [3.533, -10.79, 3.6, -1.14]}, {"time": 3.6333, "value": -1.14, "curve": [3.675, -1.14, 3.758, -10.79]}, {"time": 3.8, "value": -10.79, "curve": "stepped"}, {"time": 4.0333, "value": -10.79, "curve": [4.067, -10.79, 4.133, -11.01]}, {"time": 4.1667, "value": -11.01}], "translate": [{"x": -13.93, "y": -8.5, "curve": [0.061, -31.47, 0.126, -51.2, 0.061, -3.96, 0.126, 1.15]}, {"time": 0.1667, "x": -51.2, "y": 1.15, "curve": [0.217, -51.2, 0.317, 28.73, 0.217, 1.15, 0.317, -14.7]}, {"time": 0.3667, "x": 28.73, "y": -14.7, "curve": "stepped"}, {"time": 0.6, "x": 28.73, "y": -14.7, "curve": [0.658, 28.73, 0.775, 13.8, 0.658, -14.7, 0.775, -15.67]}, {"time": 0.8333, "x": 13.8, "y": -15.67, "curve": [0.892, 13.8, 1.008, 28.73, 0.892, -15.67, 1.008, -14.7]}, {"time": 1.0667, "x": 28.73, "y": -14.7, "curve": "stepped"}, {"time": 1.1667, "x": 28.73, "y": -14.7, "curve": [1.267, 28.73, 1.288, 39.55, 1.267, -14.7, 1.288, -13.81]}, {"time": 1.5333, "x": 39.49, "y": -13.81, "curve": [1.583, 39.49, 1.683, 28.96, 1.583, -13.81, 1.683, -12.63]}, {"time": 1.7333, "x": 28.96, "y": -12.63, "curve": "stepped"}, {"time": 1.8, "x": 28.96, "y": -12.63, "curve": [1.858, 28.96, 1.917, 102.57, 1.858, -12.63, 1.917, -20.89]}, {"time": 2, "x": 102.57, "y": -20.89, "curve": [2.121, 102.57, 2.219, 43.87, 2.121, -20.89, 2.219, -14.31]}, {"time": 2.3333, "x": 43.87, "y": -14.31, "curve": [2.458, 43.87, 2.708, -9.99, 2.458, -14.31, 2.708, -10.55]}, {"time": 2.8333, "x": -9.99, "y": -10.55, "curve": [2.958, -9.99, 3.208, 35.96, 2.958, -10.55, 3.208, -14]}, {"time": 3.3333, "x": 35.96, "y": -14, "curve": "stepped"}, {"time": 3.4333, "x": 35.96, "y": -14, "curve": [3.482, 35.96, 3.518, 71.55, 3.482, -14, 3.518, -18.58]}, {"time": 3.5667, "x": 71.55, "y": -18.58, "curve": [3.627, 71.55, 3.673, -14.29, 3.627, -18.58, 3.673, -7.54]}, {"time": 3.7333, "x": -14.29, "y": -7.54, "curve": [3.806, -14.29, 3.861, 35.96, 3.806, -7.54, 3.861, -14]}, {"time": 3.9333, "x": 35.96, "y": -14, "curve": "stepped"}, {"time": 4.1, "x": 35.96, "y": -14, "curve": [4.142, 35.96, 4.225, 15.66, 4.142, -14, 4.225, -11.4]}, {"time": 4.2667, "x": 15.66, "y": -11.4, "curve": [4.317, 15.66, 4.417, 31.9, 4.317, -11.4, 4.417, -15.22]}, {"time": 4.4667, "x": 31.9, "y": -15.22}]}, "L_hand_5": {"rotate": [{"value": -51.08, "curve": [0.092, -51.08, 0.117, -2.94]}, {"time": 0.3667, "value": -2.94, "curve": [0.558, -2.94, 0.942, 5.4]}, {"time": 1.1333, "value": 5.4, "curve": [1.225, 5.4, 1.408, -4.35]}, {"time": 1.5, "value": -4.35, "curve": "stepped"}, {"time": 1.8, "value": -4.35, "curve": [1.838, -4.35, 1.886, -33.82]}, {"time": 1.9333, "value": -33.82, "curve": [1.997, -33.82, 2.123, 4.8]}, {"time": 2.2, "value": 4.8, "curve": [2.287, 4.8, 2.347, -35.78]}, {"time": 2.4333, "value": -35.78, "curve": [2.52, -35.78, 2.58, -45.28]}, {"time": 2.6667, "value": -45.28, "curve": "stepped"}, {"time": 3.1667, "value": -45.28, "curve": [3.217, -45.28, 3.317, -78.04]}, {"time": 3.3667, "value": -78.04, "curve": [3.425, -78.04, 3.542, -9.42]}, {"time": 3.6, "value": -9.42, "curve": [3.667, -9.42, 3.8, -45.28]}, {"time": 3.8667, "value": -45.28, "curve": "stepped"}, {"time": 4.2667, "value": -45.28, "curve": "stepped"}, {"time": 4.4, "value": -45.28}], "translate": [{"x": 3.7, "y": -2.44, "curve": [0.092, 3.7, 0.117, -20.14, 0.092, -2.44, 0.117, -6.91]}, {"time": 0.3667, "x": -20.14, "y": -6.91, "curve": "stepped"}, {"time": 1.1333, "x": -20.14, "y": -6.91, "curve": [1.225, -20.14, 1.408, -16.93, 1.225, -6.91, 1.408, -9.01]}, {"time": 1.5, "x": -16.93, "y": -9.01, "curve": "stepped"}, {"time": 1.8, "x": -16.93, "y": -9.01, "curve": [1.838, -16.93, 1.886, -15.27, 1.838, -9.01, 1.886, -1.67]}, {"time": 1.9333, "x": -15.27, "y": -1.67, "curve": [1.997, -15.27, 2.123, -7.36, 1.997, -1.67, 2.123, -2.79]}, {"time": 2.2, "x": -7.36, "y": -2.79, "curve": "stepped"}, {"time": 3.1667, "x": -7.36, "y": -2.79, "curve": "stepped"}, {"time": 3.3667, "x": -7.36, "y": -2.79, "curve": "stepped"}, {"time": 3.6, "x": -7.36, "y": -2.79, "curve": "stepped"}, {"time": 3.8667, "x": -7.36, "y": -2.79, "curve": "stepped"}, {"time": 4.2667, "x": -7.36, "y": -2.79, "curve": "stepped"}, {"time": 4.4, "x": -7.36, "y": -2.79}]}, "L_brow": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": [0.267, 0, 0.299, 5.84]}, {"time": 0.4667, "value": 5.84, "curve": "stepped"}, {"time": 0.6, "value": 5.84, "curve": "stepped"}, {"time": 1.0667, "value": 5.84, "curve": "stepped"}, {"time": 1.2333, "value": 5.84, "curve": [1.297, 5.84, 1.311, 15.55]}, {"time": 1.4667, "value": 15.5, "curve": "stepped"}, {"time": 1.6333, "value": 15.5, "curve": "stepped"}, {"time": 1.8, "value": 15.5, "curve": [1.867, 15.5, 2, -0.9]}, {"time": 2.0667, "value": -0.9, "curve": [2.142, -0.9, 2.187, -23.5]}, {"time": 2.3667, "value": -23.5}], "translate": [{"x": -35.16, "y": -11.69, "curve": [0.069, -35.16, 0.136, -24.43, 0.069, -11.69, 0.136, 33.61]}, {"time": 0.2, "x": -24.43, "y": 33.61, "curve": [0.267, -24.43, 0.299, 19.33, 0.267, 33.61, 0.299, -24.87]}, {"time": 0.4667, "x": 19.33, "y": -24.87, "curve": "stepped"}, {"time": 0.6, "x": 19.33, "y": -24.87, "curve": [0.717, 19.33, 0.95, 19.87, 0.717, -24.87, 0.95, 53.74]}, {"time": 1.0667, "x": 19.87, "y": 53.74, "curve": "stepped"}, {"time": 1.2333, "x": 19.87, "y": 53.74, "curve": [1.297, 19.87, 1.311, 26.28, 1.297, 53.74, 1.311, 11.69]}, {"time": 1.4667, "x": 26.25, "y": 11.9, "curve": "stepped"}, {"time": 1.6333, "x": 26.25, "y": 11.9, "curve": "stepped"}, {"time": 1.8, "x": 26.25, "y": 11.9, "curve": [1.841, 26.25, 1.907, 7.69, 1.841, 11.9, 1.907, 5.31]}, {"time": 1.9667, "x": -6.23, "y": 0.37, "curve": [2.005, 19.24, 2.041, 39.62, 2.005, -1.38, 2.041, -2.78]}, {"time": 2.0667, "x": 39.62, "y": -2.78, "curve": [2.102, 39.62, 2.132, 23.69, 2.102, -2.78, 2.132, -5.66]}, {"time": 2.1667, "x": 2.45, "y": -9.49, "curve": [2.21, -48.5, 2.262, -124.92, 2.21, -17.25, 2.262, -28.88]}, {"time": 2.3667, "x": -124.92, "y": -28.88}]}, "L_fingeL_6": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": [0.483, 0, 0.583, 18.47]}, {"time": 0.6333, "value": 18.47, "curve": [0.692, 18.47, 0.808, -14.12]}, {"time": 0.8667, "value": -14.12, "curve": [0.917, -14.12, 1.017, 14.21]}, {"time": 1.0667, "value": 14.21, "curve": [1.25, 14.21, 1.617, 0]}, {"time": 1.8, "curve": [1.842, 0, 1.925, 22.67]}, {"time": 1.9667, "value": 22.67, "curve": [2, 22.67, 2.067, -18.49]}, {"time": 2.1, "value": -18.49, "curve": [2.134, -18.49, 2.182, -14.2]}, {"time": 2.2333, "value": -9.04}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 1.8, "curve": "stepped"}, {"time": 2.1, "curve": [2.133, 0, 2.2, 4.69, 2.133, 0, 2.2, -22.38]}, {"time": 2.2333, "x": 4.69, "y": -22.38}]}, "R_hand_2": {"rotate": [{"value": -37.8, "curve": [0.1, -37.8, 0.127, 5.47]}, {"time": 0.4, "value": 5.47, "curve": "stepped"}, {"time": 0.6333, "value": 5.47, "curve": [0.767, 5.47, 0.826, -7.01]}, {"time": 1.1667, "value": -6.94, "curve": [1.292, -6.94, 1.357, -4.88]}, {"time": 1.5, "value": -4.88, "curve": "stepped"}, {"time": 1.8, "value": -4.88, "curve": [1.833, -4.88, 1.9, -24.37]}, {"time": 1.9333, "value": -24.37, "curve": [1.983, -24.37, 2.083, 26.04]}, {"time": 2.1333, "value": 26.04, "curve": [2.183, 26.04, 2.283, -31.37]}, {"time": 2.3333, "value": -31.37, "curve": [2.383, -31.37, 2.483, -38.99]}, {"time": 2.5333, "value": -38.99, "curve": "stepped"}, {"time": 3.1667, "value": -38.99, "curve": [3.217, -38.99, 3.317, -70.55]}, {"time": 3.3667, "value": -70.55, "curve": [3.425, -70.55, 3.542, -9.39]}, {"time": 3.6, "value": -9.39, "curve": [3.667, -9.39, 3.8, -58.16]}, {"time": 3.8667, "value": -58.16, "curve": [3.967, -58.16, 4.167, -38.99]}, {"time": 4.2667, "value": -38.99, "curve": "stepped"}, {"time": 4.4, "value": -38.99}], "translate": [{"curve": [0.1, 0, 0.127, -33.49, 0.1, 0, 0.127, -2.34]}, {"time": 0.4, "x": -33.49, "y": -2.34, "curve": "stepped"}, {"time": 0.6333, "x": -33.49, "y": -2.34, "curve": "stepped"}, {"time": 1.5, "x": -33.49, "y": -2.34, "curve": "stepped"}, {"time": 1.8, "x": -33.49, "y": -2.34, "curve": "stepped"}, {"time": 1.9333, "x": -33.49, "y": -2.34, "curve": [1.983, -33.49, 2.083, -16.67, 1.983, -2.34, 2.083, 4.21]}, {"time": 2.1333, "x": -16.67, "y": 4.21, "curve": "stepped"}, {"time": 3.1667, "x": -16.67, "y": 4.21, "curve": "stepped"}, {"time": 3.3667, "x": -16.67, "y": 4.21, "curve": [3.425, -16.67, 3.542, -4.98, 3.425, 4.21, 3.542, 3.08]}, {"time": 3.6, "x": -4.98, "y": 3.08, "curve": [3.667, -4.98, 3.8, -16.67, 3.667, 3.08, 3.8, 4.21]}, {"time": 3.8667, "x": -16.67, "y": 4.21, "curve": "stepped"}, {"time": 4.2667, "x": -16.67, "y": 4.21, "curve": "stepped"}, {"time": 4.4, "x": -16.67, "y": 4.21}]}, "L_arm_2": {"rotate": [{"value": 149.73, "curve": [0.055, 121.97, 0.104, 89.59]}, {"time": 0.1333, "value": 89.59, "curve": [0.17, 89.59, 0.199, 62.58]}, {"time": 0.2333, "value": 28.81, "curve": [0.271, -20.79, 0.315, -86.93]}, {"time": 0.4, "value": -86.93, "curve": "stepped"}, {"time": 1.1333, "value": -86.93, "curve": "stepped"}, {"time": 1.5, "value": -86.93, "curve": "stepped"}, {"time": 1.8, "value": -86.93, "curve": [1.838, -86.93, 1.886, 16.94]}, {"time": 1.9333, "value": 16.94, "curve": [1.965, 16.94, 2.028, -26.3]}, {"time": 2.0667, "value": -26.3, "curve": [2.1, -26.3, 2.15, -6.17]}, {"time": 2.2, "value": 13.97, "curve": [2.25, 86.59, 2.3, 159.21]}, {"time": 2.3333, "value": 159.21, "curve": [2.558, 159.21, 3.008, 159.21]}, {"time": 3.2333, "value": 159.21, "curve": "stepped"}, {"time": 3.4667, "value": 159.21, "curve": "stepped"}, {"time": 3.7333, "value": 159.21}], "translate": [{"x": -59.39, "y": 16.66, "curve": [0.055, -58.29, 0.104, -57.02, 0.055, 34.39, 0.104, 55.08]}, {"time": 0.1333, "x": -57.02, "y": 55.08, "curve": [0.17, -57.02, 0.199, -35.38, 0.17, 55.08, 0.199, 47.42]}, {"time": 0.2333, "x": -8.34, "y": 37.84, "curve": [0.271, -21.35, 0.315, -38.7, 0.271, -1.57, 0.315, -54.11]}, {"time": 0.4, "x": -38.7, "y": -54.11, "curve": "stepped"}, {"time": 1.1333, "x": -38.7, "y": -54.11, "curve": "stepped"}, {"time": 1.5, "x": -38.7, "y": -54.11, "curve": "stepped"}, {"time": 1.8, "x": -38.7, "y": -54.11, "curve": [1.838, -38.7, 1.886, -15.06, 1.838, -54.11, 1.886, 40.25]}, {"time": 1.9333, "x": -15.06, "y": 40.25, "curve": [1.965, -15.06, 2.028, -3.37, 1.965, 40.25, 2.028, -18.52]}, {"time": 2.0667, "x": -3.37, "y": -18.52, "curve": [2.1, -3.37, 2.15, -4.63, 2.1, -18.52, 2.15, 0.69]}, {"time": 2.2, "x": -5.88, "y": 19.9, "curve": [2.25, -26.9, 2.3, -47.91, 2.25, 14.26, 2.3, 8.61]}, {"time": 2.3333, "x": -47.91, "y": 8.61, "curve": "stepped"}, {"time": 3.2333, "x": -47.91, "y": 8.61, "curve": "stepped"}, {"time": 3.4667, "x": -47.91, "y": 8.61, "curve": "stepped"}, {"time": 3.7333, "x": -47.91, "y": 8.61}]}, "face_v": {"rotate": [{"curve": "stepped"}, {"time": 1.8}], "translate": [{"x": -64.67, "curve": [0.025, -64.67, 0.075, 13.35, 0.025, 0, 0.075, 0]}, {"time": 0.1, "x": 13.35, "curve": [0.15, 13.35, 0.25, -52.21, 0.15, 0, 0.25, 0]}, {"time": 0.3, "x": -52.21, "curve": [0.367, -52.21, 0.5, -28.36, 0.367, 0, 0.5, 0]}, {"time": 0.5667, "x": -28.36, "curve": "stepped"}, {"time": 0.6, "x": -28.36, "curve": [0.658, -28.36, 0.775, -38.54, 0.658, 0, 0.775, 0]}, {"time": 0.8333, "x": -38.54, "curve": [0.892, -38.54, 1.008, -35.17, 0.892, 0, 1.008, 0]}, {"time": 1.0667, "x": -35.17, "curve": "stepped"}, {"time": 1.2, "x": -35.17, "curve": [1.291, -35.17, 1.311, 0.38, 1.291, 0, 1.311, 0]}, {"time": 1.5333, "x": 0.2, "curve": "stepped"}, {"time": 1.6667, "x": 0.2, "curve": "stepped"}, {"time": 1.8, "x": 0.2, "curve": [1.842, 0.2, 1.925, -49.51, 1.842, 0, 1.925, 0]}, {"time": 1.9667, "x": -49.51, "curve": [2.017, -49.51, 2.117, 108.69, 2.017, 0, 2.117, 0]}, {"time": 2.1667, "x": 108.69, "curve": [2.225, 108.69, 2.342, -3.46, 2.225, 0, 2.342, 0]}, {"time": 2.4, "x": -3.46, "curve": [2.525, -3.46, 2.404, -12.3, 2.525, 0, 2.404, 0]}, {"time": 2.9, "x": -12.3, "curve": [3.236, -12.3, 3.25, 6.26, 3.236, 0, 3.25, 0]}, {"time": 3.3667, "x": 6.26, "curve": [3.408, 6.26, 3.492, -46.49, 3.408, 0, 3.492, 0]}, {"time": 3.5333, "x": -46.49, "curve": [3.558, -46.49, 3.608, 30.66, 3.558, 0, 3.608, 0]}, {"time": 3.6333, "x": 30.66, "curve": [3.675, 30.66, 3.758, -19.03, 3.675, 0, 3.758, 0]}, {"time": 3.8, "x": -19.03, "curve": [3.85, -19.03, 3.95, 6.26, 3.85, 0, 3.95, 0]}, {"time": 4, "x": 6.26, "curve": [4.042, 6.26, 4.125, 19.6, 4.042, 0, 4.125, 0]}, {"time": 4.1667, "x": 19.6, "curve": [4.225, 19.6, 4.342, -16.92, 4.225, 0, 4.342, 0]}, {"time": 4.4, "x": -16.92}]}, "L_fingeL_4": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": [0.517, 0, 0.683, 19.46]}, {"time": 0.7667, "value": 19.46, "curve": [0.817, 19.46, 0.917, -14.12]}, {"time": 0.9667, "value": -14.12, "curve": [1.025, -14.12, 1.142, 14.99]}, {"time": 1.2, "value": 14.99, "curve": [1.35, 14.99, 1.65, 0]}, {"time": 1.8, "curve": [1.842, 0, 1.925, 2.51]}, {"time": 1.9667, "value": 2.51, "curve": [2, 2.51, 2.067, -10.6]}, {"time": 2.1, "value": -10.6, "curve": [2.134, -10.6, 2.182, -1.23]}, {"time": 2.2333, "value": 10.02}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 1.8, "curve": "stepped"}, {"time": 2.1, "curve": [2.133, 0, 2.2, -12.06, 2.133, 0, 2.2, -3.19]}, {"time": 2.2333, "x": -12.06, "y": -3.19}]}, "R_foot_finger_5": {"rotate": [{"value": 9.43, "curve": [0.183, 9.43, 0.55, 0]}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": "stepped"}, {"time": 2.7333, "curve": [2.767, 0, 2.833, -8.81]}, {"time": 2.8667, "value": -8.81, "curve": [2.915, -8.81, 2.96, 16.32]}, {"time": 3, "value": 16.19, "curve": [3.049, 16.19, 3.093, -8.94]}, {"time": 3.1333, "value": -8.81, "curve": [3.182, -8.81, 3.227, 16.32]}, {"time": 3.2667, "value": 16.19, "curve": [3.315, 16.19, 3.36, -8.94]}, {"time": 3.4, "value": -8.81, "curve": [3.449, -8.81, 3.493, 16.32]}, {"time": 3.5333, "value": 16.19, "curve": [3.582, 16.19, 3.627, -8.94]}, {"time": 3.6667, "value": -8.81}], "translate": [{"curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": "stepped"}, {"time": 2.7333}]}, "R_finger_3": {"rotate": [{"curve": [0.089, 0, 0.19, 10.87]}, {"time": 0.3, "value": 29.72, "curve": [0.342, 29.72, 0.425, -26.56]}, {"time": 0.4667, "value": -26.56, "curve": [0.508, -26.56, 0.592, 8.2]}, {"time": 0.6333, "value": 8.2, "curve": [0.658, 8.2, 0.708, 0]}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": [1.842, 0, 1.925, 22.67]}, {"time": 1.9667, "value": 22.67, "curve": [2.033, 22.67, 2.167, -15.2]}, {"time": 2.2333, "value": -15.2}], "translate": [{"curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": "stepped"}, {"time": 2.1, "curve": [2.133, 0, 2.2, 6.87, 2.133, 0, 2.2, -20]}, {"time": 2.2333, "x": 6.87, "y": -20}]}, "HEAD": {"rotate": [{"value": -2.08, "curve": [0.056, -3.07, 0.111, -5.05]}, {"time": 0.1667, "value": -5.05, "curve": [0.256, -5.05, 0.344, -2.55]}, {"time": 0.4333, "value": -2.55, "curve": "stepped"}, {"time": 0.6, "value": -2.55, "curve": [0.756, -2.55, 0.911, -3.61]}, {"time": 1.0667, "value": -3.61, "curve": "stepped"}, {"time": 1.1667, "value": -3.61, "curve": [1.3, -3.61, 1.433, 4.74]}, {"time": 1.5667, "value": 4.96, "curve": [1.644, 5.1, 1.722, 5.1]}, {"time": 1.8, "value": 5.1, "curve": [1.889, 5.1, 1.978, -1.31]}, {"time": 2.0667, "value": -1.31, "curve": [2.178, -1.31, 2.289, 4.09]}, {"time": 2.4, "value": 4.09, "curve": [2.433, 4.09, 2.467, -2.35]}, {"time": 2.5, "value": -2.35, "curve": [2.533, -2.35, 2.567, 3.82]}, {"time": 2.6, "value": 3.82, "curve": [2.656, 3.82, 2.711, -2.19]}, {"time": 2.7667, "value": -2.19, "curve": [2.811, -2.19, 2.856, 7.46]}, {"time": 2.9, "value": 7.46, "curve": [2.944, 7.46, 2.989, -2.19]}, {"time": 3.0333, "value": -2.19, "curve": [3.078, -2.19, 3.122, 7.46]}, {"time": 3.1667, "value": 7.46, "curve": [3.211, 7.46, 3.256, -2.19]}, {"time": 3.3, "value": -2.19, "curve": [3.344, -2.19, 3.389, 7.46]}, {"time": 3.4333, "value": 7.46, "curve": [3.478, 7.46, 3.522, -2.19]}, {"time": 3.5667, "value": -2.19, "curve": [3.622, -2.19, 3.678, 7.46]}, {"time": 3.7333, "value": 7.46, "curve": [3.8, 7.46, 3.867, 1.03]}, {"time": 3.9333, "value": -2.19}], "translate": [{"x": -11.56, "y": -4.68, "curve": [0.061, -26.97, 0.126, -44.3, 0.061, -2.45, 0.126, 0.06]}, {"time": 0.1667, "x": -44.3, "y": 0.06, "curve": [0.248, -44.3, 0.235, 25.06, 0.248, 0.06, 0.235, -8.21]}, {"time": 0.4333, "x": 25.06, "y": -8.21, "curve": "stepped"}, {"time": 0.6, "x": 25.06, "y": -8.21, "curve": [0.658, 25.06, 0.775, 12.8, 0.658, -8.21, 0.775, -8.21]}, {"time": 0.8333, "x": 12.8, "y": -8.21, "curve": [0.892, 12.8, 1.008, 5.87, 0.892, -8.21, 1.008, -22.31]}, {"time": 1.0667, "x": 5.87, "y": -22.31, "curve": "stepped"}, {"time": 1.1667, "x": 5.87, "y": -22.31, "curve": [1.276, 5.87, 1.3, 35.83, 1.276, -22.31, 1.3, -3.52]}, {"time": 1.5667, "x": 35.68, "y": -3.61, "curve": [1.625, 35.68, 1.742, 17.14, 1.625, -3.61, 1.742, -3.6]}, {"time": 1.8, "x": 17.14, "y": -3.6, "curve": [1.877, 17.14, 1.956, 25.51, 1.877, -3.6, 1.956, -8.12]}, {"time": 2.0667, "x": 25.51, "y": -8.12, "curve": [2.188, 25.51, 2.123, -8.48, 2.188, -8.12, 2.123, -3.57]}, {"time": 2.4, "x": -8.48, "y": -3.57}]}, "R_leg": {"rotate": [{"value": -3.22, "curve": [0.183, -3.22, 0.55, -12.54]}, {"time": 0.7333, "value": -12.54, "curve": "stepped"}, {"time": 1.3, "value": -12.54, "curve": [1.392, -12.54, 1.575, -4.48]}, {"time": 1.6667, "value": -4.48, "curve": [1.7, -4.48, 1.767, 0]}, {"time": 1.8, "curve": [1.858, 0, 1.975, -26.71]}, {"time": 2.0333, "value": -26.71, "curve": [2.083, -26.71, 2.183, 39.24]}, {"time": 2.2333, "value": 39.24, "curve": [2.319, 39.43, 2.379, 4.73]}, {"time": 2.4, "value": 5.18, "curve": [2.418, 5.18, 2.459, 17.39]}, {"time": 2.5333, "value": 17.39, "curve": [2.609, 17.39, 2.651, 5.18]}, {"time": 2.6667, "value": 5.18, "curve": [2.706, 5.18, 2.724, 30.53]}, {"time": 2.8, "value": 30.53, "curve": [2.88, 30.53, 2.91, 5.18]}, {"time": 2.9333, "value": 5.18, "curve": [2.973, 5.18, 2.991, 30.53]}, {"time": 3.0667, "value": 30.53, "curve": [3.146, 30.53, 3.177, 5.18]}, {"time": 3.2, "value": 5.18, "curve": [3.239, 5.18, 3.258, 30.53]}, {"time": 3.3333, "value": 30.53, "curve": [3.413, 30.53, 3.443, 5.18]}, {"time": 3.4667, "value": 5.18}], "translate": [{"x": 4.77, "y": -0.6, "curve": [0.183, 4.77, 0.55, 0, 0.183, -0.6, 0.55, 0]}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.3, "curve": "stepped"}, {"time": 1.8, "curve": [1.908, 0, 2.125, 43.98, 1.908, 0, 2.125, 0.39]}, {"time": 2.2333, "x": 43.98, "y": 0.39, "curve": "stepped"}, {"time": 2.4, "x": 43.98, "y": 0.39, "curve": "stepped"}, {"time": 2.5333, "x": 43.98, "y": 0.39, "curve": "stepped"}, {"time": 2.6667, "x": 43.98, "y": 0.39, "curve": [2.706, 43.98, 2.724, 48.62, 2.706, 0.39, 2.724, 44.54]}, {"time": 2.8, "x": 48.62, "y": 44.54, "curve": [2.88, 48.62, 2.91, 43.98, 2.88, 44.54, 2.91, 0.39]}, {"time": 2.9333, "x": 43.98, "y": 0.39, "curve": [2.973, 43.98, 2.991, 48.62, 2.973, 0.39, 2.991, 44.54]}, {"time": 3.0667, "x": 48.62, "y": 44.54, "curve": [3.146, 48.62, 3.177, 43.98, 3.146, 44.54, 3.177, 0.39]}, {"time": 3.2, "x": 43.98, "y": 0.39, "curve": [3.239, 43.98, 3.258, 48.62, 3.239, 0.39, 3.258, 44.54]}, {"time": 3.3333, "x": 48.62, "y": 44.54, "curve": [3.413, 48.62, 3.443, 43.98, 3.413, 44.54, 3.443, 0.39]}, {"time": 3.4667, "x": 43.98, "y": 0.39}]}, "R_hand_3": {"rotate": [{"value": -27.27, "curve": [0.1, -27.27, 0.127, -9.88]}, {"time": 0.4, "value": -9.88, "curve": "stepped"}, {"time": 0.6333, "value": -9.88, "curve": [0.767, -9.88, 0.826, -16.95]}, {"time": 1.1667, "value": -16.92, "curve": [1.292, -16.92, 1.357, -27.47]}, {"time": 1.5, "value": -27.47, "curve": "stepped"}, {"time": 1.8, "value": -27.47, "curve": [1.833, -27.47, 1.9, -46.96]}, {"time": 1.9333, "value": -46.96, "curve": [1.983, -46.96, 2.083, 8.72]}, {"time": 2.1333, "value": 8.72, "curve": [2.183, 8.72, 2.283, -14.29]}, {"time": 2.3333, "value": -14.29, "curve": [2.383, -14.29, 2.483, -21.91]}, {"time": 2.5333, "value": -21.91, "curve": "stepped"}, {"time": 3.1667, "value": -21.91, "curve": [3.217, -21.91, 3.317, -53.46]}, {"time": 3.3667, "value": -53.46, "curve": [3.425, -53.46, 3.542, -9.18]}, {"time": 3.6, "value": -9.18, "curve": [3.667, -9.18, 3.8, -41.08]}, {"time": 3.8667, "value": -41.08, "curve": [3.967, -41.08, 4.167, -21.91]}, {"time": 4.2667, "value": -21.91, "curve": "stepped"}, {"time": 4.4, "value": -21.91}], "translate": [{"x": -5.01, "y": 2.53, "curve": [0.1, -5.01, 0.127, -20.43, 0.1, 2.53, 0.127, -1.18]}, {"time": 0.4, "x": -20.43, "y": -1.18, "curve": "stepped"}, {"time": 0.6333, "x": -20.43, "y": -1.18, "curve": "stepped"}, {"time": 1.5, "x": -20.43, "y": -1.18, "curve": "stepped"}, {"time": 1.8, "x": -20.43, "y": -1.18, "curve": "stepped"}, {"time": 1.9333, "x": -20.43, "y": -1.18, "curve": [1.983, -20.43, 2.083, -4.37, 1.983, -1.18, 2.083, 6.07]}, {"time": 2.1333, "x": -4.37, "y": 6.07, "curve": [2.183, -4.37, 2.283, -2.72, 2.183, 6.07, 2.283, -0.35]}, {"time": 2.3333, "x": -2.72, "y": -0.35, "curve": "stepped"}, {"time": 2.5333, "x": -2.72, "y": -0.35, "curve": "stepped"}, {"time": 3.1667, "x": -2.72, "y": -0.35, "curve": "stepped"}, {"time": 3.3667, "x": -2.72, "y": -0.35, "curve": "stepped"}, {"time": 3.6, "x": -2.72, "y": -0.35, "curve": "stepped"}, {"time": 3.8667, "x": -2.72, "y": -0.35, "curve": "stepped"}, {"time": 4.2667, "x": -2.72, "y": -0.35, "curve": "stepped"}, {"time": 4.4, "x": -2.72, "y": -0.35}]}, "mouth": {"rotate": [{"curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8}], "translate": [{"curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8}]}, "face_h": {"rotate": [{"curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1.8}], "translate": [{"curve": [0.042, 0, 0.125, 0, 0.042, 0, 0.125, 39.46]}, {"time": 0.1667, "y": 39.46, "curve": [0.25, 0, 0.257, 0, 0.25, 39.46, 0.257, -37.99]}, {"time": 0.5, "y": -37.99, "curve": "stepped"}, {"time": 0.6, "y": -37.99, "curve": [0.717, 0, 0.95, 0, 0.717, -37.99, 0.95, 76.08]}, {"time": 1.0667, "y": 76.08, "curve": "stepped"}, {"time": 1.2, "y": 76.08, "curve": [1.291, 0, 1.311, 0, 1.291, 76.08, 1.311, 9.25]}, {"time": 1.5333, "y": 9.59, "curve": "stepped"}, {"time": 1.6667, "y": 9.59, "curve": "stepped"}, {"time": 1.8, "y": 9.59, "curve": "stepped"}, {"time": 2.5333, "y": 9.59, "curve": [2.575, 0, 2.658, 0.37, 2.575, 9.59, 2.658, 34.92]}, {"time": 2.7, "x": 0.37, "y": 34.92, "curve": [2.733, 0.37, 2.8, -1.49, 2.733, 34.92, 2.8, -11.47]}, {"time": 2.8333, "x": -1.49, "y": -11.47, "curve": [2.867, -1.49, 2.933, 0.37, 2.867, -11.47, 2.933, 34.92]}, {"time": 2.9667, "x": 0.37, "y": 34.92, "curve": [3, 0.37, 3.067, -1.49, 3, 34.92, 3.067, -11.47]}, {"time": 3.1, "x": -1.49, "y": -11.47, "curve": [3.133, -1.49, 3.2, 0.37, 3.133, -11.47, 3.2, 34.92]}, {"time": 3.2333, "x": 0.37, "y": 34.92, "curve": [3.267, 0.37, 3.333, -1.49, 3.267, 34.92, 3.333, -11.47]}, {"time": 3.3667, "x": -1.49, "y": -11.47, "curve": [3.4, -1.49, 3.467, 0.37, 3.4, -11.47, 3.467, 34.92]}, {"time": 3.5, "x": 0.37, "y": 34.92, "curve": "stepped"}, {"time": 3.9, "x": 0.37, "y": 34.92, "curve": [3.95, 0.37, 4.05, 0.37, 3.95, 34.92, 4.05, 56.84]}, {"time": 4.1, "x": 0.37, "y": 56.84, "curve": [4.175, 0.37, 4.325, 0.37, 4.175, 56.84, 4.325, 7.36]}, {"time": 4.4, "x": 0.37, "y": 7.36}]}, "SPINE": {"rotate": [{"value": -3.9, "curve": "stepped"}, {"time": 0.7333, "value": -3.9, "curve": "stepped"}, {"time": 1.8, "value": -3.9, "curve": "stepped"}, {"time": 2.5667, "value": -3.9, "curve": [2.634, -3.93, 2.704, 1.09]}, {"time": 2.7667, "value": 1.09, "curve": [2.812, 1.12, 2.858, -5.34]}, {"time": 2.9, "value": -5.34, "curve": [2.945, -5.36, 2.992, -0.1]}, {"time": 3.0333, "value": -0.1, "curve": [3.078, -0.07, 3.125, -6.91]}, {"time": 3.1667, "value": -6.91, "curve": [3.212, -6.94, 3.258, -1.24]}, {"time": 3.3, "value": -1.24, "curve": [3.345, -1.21, 3.392, -6.76]}, {"time": 3.4333, "value": -6.76, "curve": [3.478, -6.78, 3.525, -2.99]}, {"time": 3.5667, "value": -2.99}], "translate": [{"curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": "stepped"}, {"time": 2.5667}]}, "L_hand_6": {"rotate": [{"value": -45.31, "curve": [0.1, -45.31, 0.127, 9.17]}, {"time": 0.4, "value": 9.17, "curve": [0.583, 9.17, 0.95, 10.35]}, {"time": 1.1333, "value": 10.35, "curve": [1.225, 10.35, 1.408, 1.24]}, {"time": 1.5, "value": 1.24, "curve": "stepped"}, {"time": 1.8, "value": 1.24, "curve": [1.838, 1.24, 1.886, -35.19]}, {"time": 1.9333, "value": -35.19, "curve": [1.997, -35.19, 2.123, 7.36]}, {"time": 2.2, "value": 7.36, "curve": [2.287, 7.36, 2.347, -22.54]}, {"time": 2.4333, "value": -22.54, "curve": [2.52, -22.54, 2.58, -32.04]}, {"time": 2.6667, "value": -32.04, "curve": "stepped"}, {"time": 3.1667, "value": -32.04, "curve": [3.217, -32.04, 3.317, -64.79]}, {"time": 3.3667, "value": -64.79, "curve": [3.425, -64.79, 3.542, -8.31]}, {"time": 3.6, "value": -8.31, "curve": [3.667, -8.31, 3.8, -32.04]}, {"time": 3.8667, "value": -32.04}], "translate": [{"x": 1.83, "y": -7.01, "curve": [0.1, 1.83, 0.127, -3.99, 0.1, -7.01, 0.127, -6.12]}, {"time": 0.4, "x": -3.99, "y": -6.12, "curve": "stepped"}, {"time": 1.1333, "x": -3.99, "y": -6.12, "curve": "stepped"}, {"time": 1.5, "x": -3.99, "y": -6.12, "curve": "stepped"}, {"time": 1.8, "x": -3.99, "y": -6.12, "curve": "stepped"}, {"time": 1.9333, "x": -3.99, "y": -6.12, "curve": [1.997, -3.99, 2.123, 7.36, 1.997, -6.12, 2.123, 5.37]}, {"time": 2.2, "x": 7.36, "y": 5.37, "curve": "stepped"}, {"time": 3.1667, "x": 7.36, "y": 5.37, "curve": "stepped"}, {"time": 3.3667, "x": 7.36, "y": 5.37, "curve": "stepped"}, {"time": 3.6, "x": 7.36, "y": 5.37, "curve": "stepped"}, {"time": 3.8667, "x": 7.36, "y": 5.37}]}, "R_eye": {"translate": [{"x": 5.56, "y": 2.5, "curve": [0.033, 0.03, 0.067, -11.03, 0.033, -1.04, 0.067, -8.11]}, {"time": 0.1, "x": -11.03, "y": -8.11, "curve": [0.178, -11.03, 0.256, -9.44, 0.178, -8.11, 0.256, 4.63]}, {"time": 0.3333, "x": -9.44, "y": 8.02, "curve": [0.411, -9.44, 0.489, -11.45, 0.411, 11.4, 0.489, 12.22]}, {"time": 0.5667, "x": -14.6, "y": 12.22, "curve": [0.578, -15.05, 0.589, -20.22, 0.578, 12.22, 0.589, -2.81]}, {"time": 0.6, "x": -20.22, "y": -3.02, "curve": [0.756, -20.22, 0.911, -20.22, 0.756, -5.9, 0.911, -5.9]}, {"time": 1.0667, "x": -17.34, "y": -5.9, "curve": [1.244, -14.04, 1.422, 2.08, 1.244, -5.9, 1.422, 3.64]}, {"time": 1.6, "x": 9.2, "y": 3.64, "curve": [1.667, 11.88, 1.733, 9.98, 1.667, 3.64, 1.733, -3.01]}, {"time": 1.8, "x": 12.05, "y": -3.01, "curve": [1.856, 13.78, 1.911, 20.61, 1.856, -3.01, 1.911, -1.28]}, {"time": 1.9667, "x": 20.61, "y": -0.17, "curve": [2.011, 20.61, 2.056, -5.45, 2.011, 0.72, 2.056, 2.98]}, {"time": 2.1, "x": -11.08, "y": 2.98, "curve": [2.144, -16.71, 2.189, -16.71, 2.144, 2.98, 2.189, 1.06]}, {"time": 2.2333, "x": -16.71, "y": 0.5, "curve": [2.267, -16.71, 2.3, 0, 2.267, 0.07, 2.3, 0]}, {"time": 2.3333, "curve": [2.389, 0, 2.444, 0, 2.389, 0, 2.444, 0]}, {"time": 2.5, "curve": [2.533, 0, 2.567, -0.24, 2.533, 0, 2.567, 7.44]}, {"time": 2.6, "x": -0.24, "y": 7.44, "curve": "stepped"}, {"time": 2.7, "x": -0.24, "y": 7.44, "curve": [2.733, -0.24, 2.767, -0.25, 2.733, 7.44, 2.767, -14.88]}, {"time": 2.8, "x": -0.25, "y": -14.88, "curve": [2.856, -0.25, 2.911, 3.19, 2.856, -14.88, 2.911, -13.28]}, {"time": 2.9667, "x": 3.19, "y": -8.03, "curve": [3, 3.19, 3.033, 3.19, 3, -4.89, 3.033, 10.28]}, {"time": 3.0667, "x": 2.79, "y": 10.28, "curve": [3.111, 2.26, 3.156, 0.17, 3.111, 10.28, 3.156, -0.01]}, {"time": 3.2, "x": -0.64, "y": -0.01, "curve": [3.233, -1.24, 3.267, -1.42, 3.233, -0.01, 3.267, 2.44]}, {"time": 3.3, "x": -1.42, "y": 2.44, "curve": [3.344, -1.42, 3.389, -1.42, 3.344, 2.44, 3.389, -9.92]}, {"time": 3.4333, "x": -1.09, "y": -9.92, "curve": [3.467, -0.85, 3.5, 12.49, 3.467, -9.92, 3.5, -6.09]}, {"time": 3.5333, "x": 12.49, "y": -6.09, "curve": [3.589, 12.49, 3.644, -2.01, 3.589, -6.09, 3.644, -7.27]}, {"time": 3.7, "x": -2.01, "y": -7.27, "curve": [3.8, -2.01, 3.9, 3.91, 3.8, -7.27, 3.9, -1.63]}, {"time": 4, "x": 3.91, "y": -1.63, "curve": [4.156, 3.91, 4.311, 0.15, 4.156, -1.63, 4.311, -2.18]}, {"time": 4.4667, "x": -1.73, "y": -2.45}]}, "R_foot_finger_2": {"rotate": [{"value": 10.54, "curve": [0.183, 10.54, 0.55, 0]}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": "stepped"}, {"time": 2.6667, "curve": [2.683, 0, 2.717, -6.3]}, {"time": 2.7333, "value": -6.3, "curve": [2.782, -6.3, 2.827, 16.68]}, {"time": 2.8667, "value": 16.56, "curve": [2.915, 16.56, 2.96, -6.41]}, {"time": 3, "value": -6.3, "curve": [3.049, -6.3, 3.093, 16.68]}, {"time": 3.1333, "value": 16.56, "curve": [3.182, 16.56, 3.227, -6.41]}, {"time": 3.2667, "value": -6.3, "curve": [3.315, -6.3, 3.36, 16.68]}, {"time": 3.4, "value": 16.56, "curve": [3.449, 16.56, 3.493, -6.41]}, {"time": 3.5333, "value": -6.3}], "translate": [{"curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": "stepped"}, {"time": 2.6667}]}, "R_foot2": {"rotate": [{"value": -2.29, "curve": [0.183, -2.29, 0.55, 0]}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": [1.892, 0, 2.075, -6.75]}, {"time": 2.1667, "value": -6.75, "curve": [2.208, -6.75, 2.28, 8.52]}, {"time": 2.3333, "value": 8.52, "curve": [2.367, 8.52, 2.424, -10.61]}, {"time": 2.4667, "value": -10.61, "curve": [2.5, -10.61, 2.557, 0]}, {"time": 2.6, "curve": "stepped"}, {"time": 2.8, "curve": [2.817, 0, 2.85, -25.16]}, {"time": 2.8667, "value": -25.16, "curve": [2.908, -25.16, 2.992, 11.26]}, {"time": 3.0333, "value": 11.26, "curve": [3.058, 11.26, 3.108, -25.16]}, {"time": 3.1333, "value": -25.16, "curve": [3.175, -25.16, 3.258, 11.26]}, {"time": 3.3, "value": 11.26, "curve": [3.325, 11.26, 3.375, -25.16]}, {"time": 3.4, "value": -25.16, "curve": [3.442, -25.16, 3.525, 11.26]}, {"time": 3.5667, "value": 11.26, "curve": [3.592, 11.26, 3.642, -25.16]}, {"time": 3.6667, "value": -25.16, "curve": [3.733, -25.16, 3.867, 11.26]}, {"time": 3.9333, "value": 11.26}], "translate": [{"curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.8, "curve": "stepped"}, {"time": 2.8}]}, "R_leg2": {"rotate": [{"value": 5.75, "curve": [0.183, 5.75, 0.55, 15.05]}, {"time": 0.7333, "value": 15.05, "curve": "stepped"}, {"time": 1.3, "value": 15.05, "curve": [1.392, 15.05, 1.575, 6.85]}, {"time": 1.6667, "value": 6.85, "curve": [1.7, 6.85, 1.767, 0]}, {"time": 1.8, "curve": [1.858, 0, 1.975, 29.43]}, {"time": 2.0333, "value": 29.43, "curve": [2.083, 29.43, 2.183, -35.28]}, {"time": 2.2333, "value": -35.28, "curve": [2.319, -35.51, 2.379, 6.77]}, {"time": 2.4, "value": 6.23, "curve": [2.418, 6.23, 2.459, -9.15]}, {"time": 2.5333, "value": -9.15, "curve": [2.609, -9.15, 2.651, 6.23]}, {"time": 2.6667, "value": 6.23, "curve": "stepped"}, {"time": 2.8, "value": 6.23, "curve": [2.823, 6.23, 2.854, -24.3]}, {"time": 2.9333, "value": -24.15, "curve": [3.008, -24.15, 3.045, 6.23]}, {"time": 3.0667, "value": 6.23, "curve": [3.09, 6.23, 3.121, -24.3]}, {"time": 3.2, "value": -24.15, "curve": [3.275, -24.15, 3.312, 6.23]}, {"time": 3.3333, "value": 6.23, "curve": [3.356, 6.23, 3.388, -24.3]}, {"time": 3.4667, "value": -24.15, "curve": [3.541, -24.15, 3.578, 6.23]}, {"time": 3.6, "value": 6.23}], "translate": [{"x": -0.6, "y": 0.6, "curve": [0.183, -0.6, 0.55, 0, 0.183, 0.6, 0.55, 0]}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.3, "curve": "stepped"}, {"time": 1.8, "curve": [1.908, 0, 2.125, -14.79, 1.908, 0, 2.125, 0.39]}, {"time": 2.2333, "x": -14.79, "y": 0.39, "curve": "stepped"}, {"time": 2.4, "x": -14.79, "y": 0.39, "curve": "stepped"}, {"time": 2.5333, "x": -14.79, "y": 0.39, "curve": "stepped"}, {"time": 2.6667, "x": -14.79, "y": 0.39, "curve": "stepped"}, {"time": 2.8, "x": -14.79, "y": 0.39, "curve": [2.823, -14.79, 2.854, -12.45, 2.823, 0.39, 2.854, 70.46]}, {"time": 2.9333, "x": -12.46, "y": 70.11, "curve": [3.008, -12.46, 3.045, -14.79, 3.008, 70.11, 3.045, 0.39]}, {"time": 3.0667, "x": -14.79, "y": 0.39, "curve": [3.09, -14.79, 3.121, -12.45, 3.09, 0.39, 3.121, 70.46]}, {"time": 3.2, "x": -12.46, "y": 70.11, "curve": [3.275, -12.46, 3.312, -14.79, 3.275, 70.11, 3.312, 0.39]}, {"time": 3.3333, "x": -14.79, "y": 0.39, "curve": [3.356, -14.79, 3.388, -12.45, 3.356, 0.39, 3.388, 70.46]}, {"time": 3.4667, "x": -12.46, "y": 70.11, "curve": [3.541, -12.46, 3.578, -14.79, 3.541, 70.11, 3.578, 0.39]}, {"time": 3.6, "x": -14.79, "y": 0.39}]}, "banana_c": {"rotate": [{}], "translate": [{"x": -706.46, "y": 31.14}]}, "tail3": {"rotate": [{"value": 1.07}]}, "tail base": {"rotate": [{"time": 1.8}, {"time": 2.0667, "value": -44.1, "curve": "stepped"}, {"time": 3.4667, "value": -44.1}, {"time": 3.6667, "value": 16.42}, {"time": 3.9667}]}}, "transform": {"L_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}], "R_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}]}, "path": {"L_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0}]}, "R_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0}]}}, "attachments": {"default": {"head": {"monkey new/head": {"deform": [{"time": 0.0667, "curve": [0.089, 0, 0.115, 1]}, {"time": 0.1333, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": "stepped"}, {"time": 0.2667, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": [0.272, 0, 0.311, 1]}, {"time": 0.3333, "curve": "stepped"}, {"time": 1.1333, "curve": [1.156, 0, 1.182, 1]}, {"time": 1.2, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": "stepped"}, {"time": 1.4, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": [1.406, 0, 1.444, 1]}, {"time": 1.4667}]}}, "monkey_eyebrow_angry": {"monkey new/monkey_eyebrow_angry": {"deform": [{"time": 2.0667, "vertices": [1.03926, -0.0304, 1.03891, -0.03038, 0, 0, 0, 0, 58.83936, -7.99845, 58.83826, -7.9984, 123.02536, -4.37517, 123.02341, -4.37511, 61.31232, 0.9563, 61.31116, 0.95637, 11.49254, 0.69672, 11.49133, 0.69676, 8.23302, 1.92806, 8.23203, 1.92808, 10.22086, 2.54206, 10.2197, 2.54207, 17.2816, 5.41804, 17.2807, 5.41806, 16.66623, 6.40797, 16.66571, 6.40798, 90.47401, 22.85586, 90.47195, 22.85596, 114.24461, 17.85987, 114.24316, 17.85998, 26.75212, -0.78209, 26.75177, -0.78208, 16.75063, -0.48972, 16.75009, -0.4897], "curve": [2.111, 0, 2.156, 1]}, {"time": 2.2}]}}, "monkey_mouth o": {"monkey new/monkey_mouth o": {"deform": [{"offset": 32, "vertices": [7.99897, -1.64798, 7.99895, -1.64797, 18.2873, -4.47478, 18.28722, -4.47478, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13.88089, 0.32795, 13.88083, 0.32796, 13.88089, 0.32795, 13.88083, 0.32796, 13.88089, 0.32795, 13.88083, 0.32796, 13.88089, 0.32795, 13.88083, 0.32796, 13.88089, 0.32795, 13.88083, 0.32796, 13.88089, 0.32795, 13.88083, 0.32796, 13.88089, 0.32795, 13.88083, 0.32796, 13.88089, 0.32795, 13.88083, 0.32796, 13.88089, 0.32795, 13.88083, 0.32796]}]}}}}, "drawOrder": [{"offsets": [{"slot": "monkeyOLD_banana_3_1", "offset": -9}, {"slot": "monkeyOLD_banana_1", "offset": -9}, {"slot": "banana_3_2", "offset": -9}, {"slot": "banana_2_1", "offset": -9}, {"slot": "banana_2_2", "offset": -9}, {"slot": "banana_3_3", "offset": -9}, {"slot": "monkey_right_hand", "offset": 10}]}, {"time": 0.4333}, {"time": 1.8}, {"time": 2.0667}, {"time": 2.2333}]}, "Match": {"slots": {"banana_2_1": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff", "curve": [0.85, 1, 0.883, 1, 0.85, 1, 0.883, 1, 0.85, 1, 0.883, 1, 0.85, 1, 0.883, 0]}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00"}], "attachment": [{}, {"time": 1.0333}]}, "banana_2_2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff", "curve": [0.85, 1, 0.883, 1, 0.85, 1, 0.883, 1, 0.85, 1, 0.883, 1, 0.85, 1, 0.883, 0]}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00"}], "attachment": [{}, {"time": 1.0333}]}, "banana_3_2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff", "curve": [0.85, 1, 0.883, 1, 0.85, 1, 0.883, 1, 0.85, 1, 0.883, 1, 0.85, 1, 0.883, 0]}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00"}], "attachment": [{}, {"time": 1.0333}]}, "banana_3_3": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff", "curve": [0.85, 1, 0.883, 1, 0.85, 1, 0.883, 1, 0.85, 1, 0.883, 1, 0.85, 1, 0.883, 0]}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00"}], "attachment": [{}, {"time": 1.0333}]}, "closed_eye_left": {"attachment": [{"time": 0.1333, "name": "monkey new/closed_eye_left"}, {"time": 0.2667}]}, "closed_eye_right": {"attachment": [{"time": 0.1333, "name": "monkey new/closed_eye_right"}, {"time": 0.2667}]}, "monkeyOLD_banana_1": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff", "curve": [0.85, 1, 0.883, 1, 0.85, 1, 0.883, 1, 0.85, 1, 0.883, 1, 0.85, 1, 0.883, 0]}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00"}], "attachment": [{}, {"time": 1.0333}]}, "monkeyOLD_banana_3_1": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff", "curve": [0.85, 1, 0.883, 1, 0.85, 1, 0.883, 1, 0.85, 1, 0.883, 1, 0.85, 1, 0.883, 0]}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00"}], "attachment": [{}, {"time": 1.0333}]}, "monkey_hand left": {"attachment": [{}]}, "monkey_hand right": {"attachment": [{}]}, "monkey_left_hand": {"attachment": [{}, {"time": 0.3, "name": "monkey new/monkey_left_hand"}, {"time": 0.6667}]}, "monkey_left_hand closed": {"attachment": [{"name": "monkey new/monkey_left_hand closed"}, {"time": 0.3}, {"time": 0.6667, "name": "monkey new/monkey_left_hand closed"}]}, "monkey_mouth angry": {"attachment": [{}, {"time": 0.0667, "name": "monkey new/monkey_mouth angry"}, {"time": 0.3}]}, "monkey_mouth o": {"attachment": [{}, {"time": 0.3, "name": "monkey new/monkey_mouth o"}]}, "monkey_mouth smile closed": {"attachment": [{"time": 0.0667}]}, "monkey_mouth smile open": {"attachment": [{}]}, "monkey_right_hand": {"attachment": [{}, {"time": 0.6, "name": "monkey new/monkey_right_hand"}, {"time": 0.8333}]}}, "bones": {"Banana": {"rotate": [{"value": 0.03, "curve": [0.023, -6.61, 0.071, -21.69]}, {"time": 0.1, "value": -30.94, "curve": [0.117, -34.36, 0.145, -39.88]}, {"time": 0.1667, "value": -43.46, "curve": [0.208, -70.68, 0.279, -114.76]}, {"time": 0.3333, "value": -143.27, "curve": [0.362, -162.12, 0.408, -192.8]}, {"time": 0.4333, "value": -210.77, "curve": [0.51, -252.15, 0.632, -319.5]}, {"time": 0.7, "value": -358.95, "curve": [0.767, -403.2, 0.874, -475.23]}, {"time": 0.9333, "value": -517.42, "curve": [0.962, -473.46, 1.008, -401.89]}, {"time": 1.0333, "value": -359.97}], "translate": [{"x": 6.4, "y": -7.09, "curve": [0.057, 12.62, 0.169, 25.53, 0.057, 257.67, 0.169, 807.39]}, {"time": 0.2667, "x": 25.53, "y": 807.39, "curve": [0.344, 25.53, 0.588, 4.15, 0.344, 807.39, 0.588, 282.05]}, {"time": 0.7, "x": -5.51, "y": 44.64, "curve": [0.783, -5.51, 0.95, 6.4, 0.783, 44.64, 0.95, -7.09]}, {"time": 1.0333, "x": 6.4, "y": -7.09}]}, "bone": {"rotate": [{"time": 0.2667, "curve": [0.3, 0, 0.333, 25.24]}, {"time": 0.3667, "value": 25.24, "curve": "stepped"}, {"time": 0.6333, "value": 25.24, "curve": [0.648, 25.24, 0.652, -28.82]}, {"time": 0.6667, "value": -28.82, "curve": [0.722, -28.82, 0.778, -69.15]}, {"time": 0.8333, "value": -69.15}]}, "R_hand_4": {"rotate": [{"value": 33, "curve": "stepped"}, {"time": 0.7, "value": 33, "curve": [0.744, 33, 0.789, -63.87]}, {"time": 0.8333, "value": -63.87}], "translate": [{"x": 54.53, "y": -47.18, "curve": [0.189, 54.53, 0.378, -60.44, 0.189, -47.18, 0.378, 14.8]}, {"time": 0.5667, "x": -60.44, "y": 14.8, "curve": [0.578, -60.44, 0.589, 72.76, 0.578, 14.8, 0.589, -30.61]}, {"time": 0.6, "x": 72.76, "y": -30.61, "curve": [0.633, 72.76, 0.667, 9.77, 0.633, -30.61, 0.667, -49.02]}, {"time": 0.7, "x": 9.77, "y": -49.02}], "inherit": [{"inherit": "onlyTranslation"}]}, "root": {"scale": [{}]}, "L_hand_5": {"rotate": [{"value": -57.17, "curve": [0.05, -57.17, 0.064, -78.22]}, {"time": 0.2, "value": -78.22, "curve": [0.225, -78.22, 0.275, -59.91]}, {"time": 0.3, "value": -59.91, "curve": [0.333, -59.91, 0.4, 29.82]}, {"time": 0.4333, "value": 29.82, "curve": [0.493, 26.93, 0.575, 23.33]}, {"time": 0.6333, "value": 20.41, "curve": [0.673, 11.58, 0.728, 0.56]}, {"time": 0.7667, "value": -8.36, "curve": [0.787, -26.32, 0.814, -48.72]}, {"time": 0.8333, "value": -66.84, "curve": [0.883, -66.84, 0.983, -51.08]}, {"time": 1.0333, "value": -51.08}], "translate": [{"x": 3.7, "y": -2.44, "curve": [0.05, 3.7, 0.064, 3.2, 0.05, -2.44, 0.064, 1.07]}, {"time": 0.2, "x": 3.2, "y": 1.07, "curve": [0.258, 3.2, 0.375, -20.14, 0.258, 1.07, 0.375, -6.91]}, {"time": 0.4333, "x": -20.14, "y": -6.91, "curve": "stepped"}, {"time": 0.6333, "x": -20.14, "y": -6.91, "curve": "stepped"}, {"time": 0.8333, "x": -20.14, "y": -6.91, "curve": [0.883, -20.14, 0.983, 3.7, 0.883, -6.91, 0.983, -2.44]}, {"time": 1.0333, "x": 3.7, "y": -2.44}]}, "L_fingeL_6": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": [0.217, 0, 0.25, 17.73]}, {"time": 0.2667, "value": 17.73, "curve": [0.283, 17.73, 0.317, 0]}, {"time": 0.3333, "curve": [0.345, -5.04, 0.356, -10.47]}, {"time": 0.3667, "value": -14.31, "curve": [0.433, -14.31, 0.567, 0]}, {"time": 0.6333, "curve": [0.701, -14.36, 0.771, -29.87]}, {"time": 0.8333, "value": -40.81, "curve": [0.883, -40.81, 0.983, 0]}, {"time": 1.0333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.0333}]}, "R_finger_1": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": [0.25, 0, 0.306, -5.38]}, {"time": 0.3667, "value": -14.73, "curve": "stepped"}, {"time": 0.6333, "value": -14.73, "curve": "stepped"}, {"time": 0.8333, "value": -14.73, "curve": [0.883, -14.73, 0.983, 0]}, {"time": 1.0333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.0333}]}, "R_brow": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": [0.275, 0, 0.311, -1.57]}, {"time": 0.5, "value": -1.57, "curve": "stepped"}, {"time": 0.9, "value": -1.57, "curve": [0.932, -1.57, 0.988, -0.54]}, {"time": 1.0333}], "translate": [{"x": 7.25, "y": -0.95, "curve": [0.069, 7.25, 0.136, -24.43, 0.069, -0.95, 0.136, 33.61]}, {"time": 0.2, "x": -24.43, "y": 33.61, "curve": [0.253, -24.43, 0.286, 11.22, 0.253, 33.61, 0.286, 62.12]}, {"time": 0.3667, "x": 34.44, "y": 59.92, "curve": [0.401, 44.23, 0.444, 104.75, 0.401, 58.99, 0.444, 47.94]}, {"time": 0.5, "x": 104.75, "y": 47.94, "curve": [0.543, 104.75, 0.604, 130.55, 0.543, 47.94, 0.604, 26.64]}, {"time": 0.6667, "x": 97.54, "y": 15.82, "curve": [0.7, 80.24, 0.734, 96.05, 0.7, 10.16, 0.734, 6.4]}, {"time": 0.7667, "x": 76.37, "y": 6.02, "curve": [0.818, 45.58, 0.865, -23.23, 0.818, 5.43, 0.865, 9.57]}, {"time": 0.9, "x": -23.23, "y": 9.57, "curve": [0.933, -23.23, 1, -26.42, 0.933, 9.57, 1, 5.48]}, {"time": 1.0333, "x": -26.42, "y": 5.48}]}, "L_hand_4": {"rotate": [{"value": -18.67, "curve": [0.05, -18.67, 0.064, 46.93]}, {"time": 0.2, "value": 46.93, "curve": [0.264, 46.93, 0.289, -104.92]}, {"time": 0.3667, "value": -104.92, "curve": [0.433, -104.92, 0.567, 4.16]}, {"time": 0.6333, "value": 4.16, "curve": [0.683, 4.16, 0.783, -14.81]}, {"time": 0.8333, "value": -14.81, "curve": [0.883, -14.81, 0.983, -11.84]}, {"time": 1.0333, "value": -11.84}], "translate": [{"x": -9.92, "y": -31.4, "curve": [0.05, -9.92, 0.064, 9.62, 0.05, -31.4, 0.064, -20.06]}, {"time": 0.2, "x": 9.62, "y": -20.06, "curve": [0.264, 9.62, 0.289, -43.78, 0.264, -20.06, 0.289, -31.41]}, {"time": 0.3667, "x": -43.78, "y": -31.41, "curve": "stepped"}, {"time": 0.6333, "x": -43.78, "y": -31.41, "curve": "stepped"}, {"time": 0.8333, "x": -43.78, "y": -31.41, "curve": [0.883, -43.78, 0.983, -9.92, 0.883, -31.41, 0.983, -31.4]}, {"time": 1.0333, "x": -9.92, "y": -31.4}]}, "face_h": {"rotate": [{"curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 1.0333}], "translate": [{"curve": [0.05, 0, 0.15, 0, 0.05, 0, 0.15, 39.46]}, {"time": 0.2, "y": 39.46, "curve": [0.25, 0, 0.254, -9.65, 0.25, 39.46, 0.254, 80.95]}, {"time": 0.4, "x": -9.65, "y": 80.95, "curve": [0.475, -9.65, 0.625, 70.3, 0.475, 80.95, 0.625, 3.64]}, {"time": 0.7, "x": 70.3, "y": 3.64, "curve": [0.758, 70.3, 0.875, 39.48, 0.758, 3.64, 0.875, 10.31]}, {"time": 0.9333, "x": 39.48, "y": 10.31, "curve": [0.958, 39.48, 1.008, 0, 0.958, 10.31, 1.008, 0]}, {"time": 1.0333}]}, "face_v": {"rotate": [{"curve": "stepped"}, {"time": 1.0333}], "translate": [{"x": 32.5, "curve": [0.025, 32.5, 0.075, 45.22, 0.025, 0, 0.075, 0]}, {"time": 0.1, "x": 45.22, "curve": [0.142, 45.22, 0.225, -52.21, 0.142, 0, 0.225, 0]}, {"time": 0.2667, "x": -52.21, "curve": [0.325, -52.21, 0.442, 93.35, 0.325, 0, 0.442, 7.66]}, {"time": 0.5, "x": 93.35, "y": 7.66, "curve": [0.558, 93.35, 0.675, 11.98, 0.558, 7.66, 0.675, -0.29]}, {"time": 0.7333, "x": 11.98, "y": -0.29, "curve": [0.775, 11.98, 0.858, -114.46, 0.775, -0.29, 0.858, 2.68]}, {"time": 0.9, "x": -114.46, "y": 2.68, "curve": [0.933, -114.46, 1, -64.67, 0.933, 2.68, 1, 0]}, {"time": 1.0333, "x": -64.67}]}, "R_hand_2": {"rotate": [{"value": -43.09, "curve": [0.05, -43.09, 0.064, -77.94]}, {"time": 0.2, "value": -77.94, "curve": [0.265, -77.94, 0.312, -12.4]}, {"time": 0.3667, "value": -12.4, "curve": [0.433, -12.4, 0.567, 27.9]}, {"time": 0.6333, "value": 27.9, "curve": [0.683, 27.9, 0.783, -17.38]}, {"time": 0.8333, "value": -17.38, "curve": [0.883, -17.38, 0.983, -37.8]}, {"time": 1.0333, "value": -37.8}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": [0.265, 0, 0.312, -33.49, 0.265, 0, 0.312, -2.34]}, {"time": 0.3667, "x": -33.49, "y": -2.34, "curve": [0.433, -33.49, 0.567, 4.9, 0.433, -2.34, 0.567, 15.54]}, {"time": 0.6333, "x": 4.9, "y": 15.54, "curve": [0.683, 4.9, 0.783, -33.49, 0.683, 15.54, 0.783, -2.34]}, {"time": 0.8333, "x": -33.49, "y": -2.34, "curve": [0.883, -33.49, 0.983, 0, 0.883, -2.34, 0.983, 0]}, {"time": 1.0333}]}, "main_C": {"rotate": [{"curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 1.0333}], "translate": [{"curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 1.0333}], "scale": [{}]}, "R_foot_finger_5": {"rotate": [{"value": 9.43, "curve": [0.023, 9.43, 0.029, -14.74]}, {"time": 0.1, "value": -14.74, "curve": [0.175, -14.74, 0.25, 21.44]}, {"time": 0.3, "value": 21.44, "curve": [0.367, 21.44, 0.5, 5.82]}, {"time": 0.5667, "value": 5.82, "curve": [0.608, 5.82, 0.692, 9.43]}, {"time": 0.7333, "value": 9.43, "curve": "stepped"}, {"time": 1.0333, "value": 9.43}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.0333}]}, "R_foot": {"rotate": [{"value": 3.59, "curve": [0.044, 3.59, 0.089, 15.56]}, {"time": 0.1333, "value": 15.56, "curve": [0.2, 15.56, 0.267, -25.65]}, {"time": 0.3333, "value": -25.65, "curve": [0.467, -25.65, 0.6, 3.59]}, {"time": 0.7333, "value": 3.59, "curve": [0.8, 3.59, 0.867, 3.59]}, {"time": 0.9333, "value": 3.59, "curve": "stepped"}, {"time": 1.0333, "value": 3.59}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.0333}]}, "R_arm_1": {"rotate": [{"value": 124.12, "curve": "stepped"}, {"time": 0.2, "value": 124.12, "curve": [0.242, 124.12, 0.325, 95.06]}, {"time": 0.3667, "value": 95.06, "curve": [0.392, 95.06, 0.411, 71.97]}, {"time": 0.4333, "value": 43.09, "curve": "stepped"}, {"time": 0.5, "value": -73.87, "curve": [0.527, -99.35, 0.564, -124.83]}, {"time": 0.6333, "value": -124.83, "curve": [0.683, -124.83, 0.783, 18.47]}, {"time": 0.8333, "value": 18.47, "curve": [0.85, 18.47, 0.879, 38.98]}, {"time": 0.9, "value": 45.82, "curve": "stepped"}, {"time": 0.9333, "value": 168.3, "curve": [0.959, 168.3, 0.995, 148.07]}, {"time": 1.0333, "value": 124.96}], "translate": [{"x": -34.03, "y": 23.46, "curve": "stepped"}, {"time": 0.2, "x": -34.03, "y": 23.46, "curve": [0.242, -34.03, 0.325, -70, 0.242, 23.46, 0.325, 70.49]}, {"time": 0.3667, "x": -70, "y": 70.49, "curve": [0.392, -70, 0.411, -52.49, 0.392, 70.49, 0.411, 71.34]}, {"time": 0.4333, "x": -30.6, "y": 72.39, "curve": "stepped"}, {"time": 0.5, "x": -47.98, "y": -12.92, "curve": [0.527, -41.33, 0.564, -34.69, 0.527, -18.65, 0.564, -24.39]}, {"time": 0.6333, "x": -34.69, "y": -24.39, "curve": [0.683, -34.69, 0.783, -13.86, 0.683, -24.39, 0.783, 23.94]}, {"time": 0.8333, "x": -13.86, "y": 23.94, "curve": [0.85, -13.86, 0.879, 30.35, 0.85, 23.94, 0.879, 19.95]}, {"time": 0.9, "x": 45.08, "y": 18.62, "curve": "stepped"}, {"time": 0.9333, "x": -49.6, "y": -1.67, "curve": [0.959, -49.6, 0.995, -48.61, 0.959, -1.67, 0.995, 11.58]}, {"time": 1.0333, "x": -47.48, "y": 26.73}], "scale": [{"time": 0.3667, "curve": "stepped"}, {"time": 0.5, "y": -1, "curve": "stepped"}, {"time": 0.9, "y": -1, "curve": "stepped"}, {"time": 0.9333}]}, "HEAD": {"rotate": [{"value": -1.35, "curve": [0.017, -1.35, 0.05, 0.49]}, {"time": 0.0667, "value": 0.49, "curve": [0.092, 0.49, 0.142, -5.05]}, {"time": 0.1667, "value": -5.05, "curve": [0.228, -5.05, 0.218, -12.6]}, {"time": 0.3667, "value": -12.6, "curve": [0.442, -12.6, 0.592, -2.55]}, {"time": 0.6667, "value": -2.55, "curve": [0.725, -2.55, 0.813, -0.33]}, {"time": 0.9, "value": 1.88, "curve": [0.945, 0.47, 0.991, -0.94]}, {"time": 1.0333, "value": -2.08}], "translate": [{"x": -24.48, "y": 0.06, "curve": [0.017, -24.48, 0.05, -7.33, 0.017, 0.06, 0.05, 0.06]}, {"time": 0.0667, "x": -7.33, "y": 0.06, "curve": [0.092, -7.33, 0.142, -90.38, 0.092, 0.06, 0.142, 2.2]}, {"time": 0.1667, "x": -90.38, "y": 2.2, "curve": [0.212, -90.38, 0.231, -64.35, 0.212, 2.2, 0.231, -1.33]}, {"time": 0.2667, "x": -38.32, "y": -4.85, "curve": [0.285, -6.63, 0.311, 25.06, 0.285, -6.53, 0.311, -8.21]}, {"time": 0.3667, "x": 25.06, "y": -8.21, "curve": [0.401, 25.06, 0.449, 33.29, 0.401, -8.21, 0.449, -8.51]}, {"time": 0.5, "x": 42.43, "y": -8.84, "curve": [0.561, 28.92, 0.626, 13.72, 0.561, -5.6, 0.626, -1.95]}, {"time": 0.6667, "x": 13.72, "y": -1.95, "curve": [0.725, 13.72, 0.842, 12.8, 0.725, -1.95, 0.842, -8.21]}, {"time": 0.9, "x": 12.8, "y": -8.21, "curve": [0.934, 12.8, 0.983, 1.26, 0.934, -8.21, 0.983, -6.54]}, {"time": 1.0333, "x": -11.56, "y": -4.68}]}, "CHEST": {"rotate": [{"value": -2.99, "curve": [0.092, -2.99, 0.275, -1.73]}, {"time": 0.3667, "value": -1.73, "curve": "stepped"}, {"time": 0.6667, "value": -1.73, "curve": "stepped"}, {"time": 0.9, "value": -1.73, "curve": "stepped"}, {"time": 1.0333, "value": -1.73}], "translate": [{"x": -12.19, "y": 1.15, "curve": [0.042, -12.19, 0.125, -62.6, 0.042, 1.15, 0.125, 2.25]}, {"time": 0.1667, "x": -62.6, "y": 2.25, "curve": [0.217, -62.6, 0.317, 28.73, 0.217, 2.25, 0.317, -14.7]}, {"time": 0.3667, "x": 28.73, "y": -14.7, "curve": "stepped"}, {"time": 0.6667, "x": 28.73, "y": -14.7, "curve": [0.725, 28.73, 0.842, 13.8, 0.725, -14.7, 0.842, -15.67]}, {"time": 0.9, "x": 13.8, "y": -15.67, "curve": [0.934, 13.8, 0.983, 0.67, 0.934, -15.67, 0.983, -12.27]}, {"time": 1.0333, "x": -13.93, "y": -8.5}]}, "R_hand_1": {"rotate": [{"value": 3.5, "curve": [0.05, 3.5, 0.064, -42.29]}, {"time": 0.2, "value": -42.29, "curve": [0.264, -42.29, 0.289, -0.77]}, {"time": 0.3667, "value": -0.77, "curve": [0.433, -0.77, 0.567, 74.66]}, {"time": 0.6333, "value": 74.66, "curve": [0.683, 74.66, 0.783, -2.91]}, {"time": 0.8333, "value": -2.91, "curve": [0.883, -2.91, 0.983, -5.33]}, {"time": 1.0333, "value": -5.33}], "translate": [{"x": 2.03, "y": 11.13, "curve": [0.05, 2.03, 0.064, 3.05, 0.05, 11.13, 0.064, 21.48]}, {"time": 0.2, "x": 3.05, "y": 21.48, "curve": [0.264, 3.05, 0.289, -7.46, 0.264, 21.48, 0.289, 20.25]}, {"time": 0.3667, "x": -7.46, "y": 20.25, "curve": [0.433, -7.46, 0.567, -17.46, 0.433, 20.25, 0.567, 25.04]}, {"time": 0.6333, "x": -17.46, "y": 25.04, "curve": [0.683, -17.46, 0.783, -7.46, 0.683, 25.04, 0.783, 20.25]}, {"time": 0.8333, "x": -7.46, "y": 20.25, "curve": [0.883, -7.46, 0.983, 2.03, 0.883, 20.25, 0.983, 11.13]}, {"time": 1.0333, "x": 2.03, "y": 11.13}]}, "R_leg": {"rotate": [{"value": -3.22, "curve": [0.044, -3.22, 0.089, 22.83]}, {"time": 0.1333, "value": 22.83, "curve": [0.222, 22.83, 0.311, -0.15]}, {"time": 0.4, "value": -0.15, "curve": [0.489, -0.15, 0.578, 12.94]}, {"time": 0.6667, "value": 12.94, "curve": [0.744, 12.94, 0.822, -3.22]}, {"time": 0.9, "value": -3.22, "curve": "stepped"}, {"time": 1.0333, "value": -3.22}], "translate": [{"x": 4.77, "y": -0.6, "curve": [0.044, 4.77, 0.089, 39.31, 0.044, -0.6, 0.089, 4.43]}, {"time": 0.1333, "x": 39.31, "y": 6.71, "curve": [0.222, 39.31, 0.311, 5.69, 0.222, 11.27, 0.311, 19.91]}, {"time": 0.4, "x": 5.23, "y": 19.91, "curve": [0.489, 4.77, 0.578, 4.77, 0.489, 19.91, 0.578, -0.6]}, {"time": 0.6667, "x": 4.77, "y": -0.6, "curve": "stepped"}, {"time": 0.9, "x": 4.77, "y": -0.6, "curve": "stepped"}, {"time": 1.0333, "x": 4.77, "y": -0.6}]}, "R_hand_3": {"rotate": [{"value": -31.36, "curve": [0.05, -31.36, 0.064, -19.03]}, {"time": 0.2, "value": -19.03, "curve": [0.265, -19.03, 0.312, -49.15]}, {"time": 0.3667, "value": -49.15, "curve": [0.433, -49.15, 0.567, 1.8]}, {"time": 0.6333, "value": 1.8, "curve": [0.683, 1.8, 0.783, -19.37]}, {"time": 0.8333, "value": -19.37, "curve": [0.883, -19.37, 0.983, -27.27]}, {"time": 1.0333, "value": -27.27}], "translate": [{"x": -5.01, "y": 2.53, "curve": [0.05, -5.01, 0.064, -1.45, 0.05, 2.53, 0.064, 0.73]}, {"time": 0.2, "x": -1.45, "y": 0.73, "curve": [0.265, -1.45, 0.312, -20.43, 0.265, 0.73, 0.312, -1.18]}, {"time": 0.3667, "x": -20.43, "y": -1.18, "curve": [0.433, -20.43, 0.567, 4.3, 0.433, -1.18, 0.567, -12.06]}, {"time": 0.6333, "x": 4.3, "y": -12.06, "curve": [0.683, 4.3, 0.783, -20.43, 0.683, -12.06, 0.783, -1.18]}, {"time": 0.8333, "x": -20.43, "y": -1.18, "curve": [0.883, -20.43, 0.983, -5.01, 0.883, -1.18, 0.983, 2.53]}, {"time": 1.0333, "x": -5.01, "y": 2.53}]}, "R_finger_2": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": [0.25, 0, 0.306, -5.38]}, {"time": 0.3667, "value": -14.73, "curve": "stepped"}, {"time": 0.6333, "value": -14.73, "curve": "stepped"}, {"time": 0.8333, "value": -14.73, "curve": [0.883, -14.73, 0.983, 0]}, {"time": 1.0333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.0333}]}, "mouth": {"rotate": [{"curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 1.0333}], "translate": [{"curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 1.0333}]}, "SPINE": {"rotate": [{"value": -4.94, "curve": [0.2, -4.94, 0.6, -3.9]}, {"time": 0.8, "value": -3.9, "curve": "stepped"}, {"time": 1.0333, "value": -3.9}], "translate": [{"curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 1.0333}]}, "R_eye": {"rotate": [{"curve": "stepped"}, {"time": 1.0333}], "translate": [{"x": -2.07, "y": -4.13, "curve": [0.092, -2.07, 0.275, 16.11, 0.092, -4.13, 0.275, 7.29]}, {"time": 0.3667, "x": 16.11, "y": 7.29, "curve": [0.386, 16.11, 0.409, 12.77, 0.386, 7.29, 0.409, -2.69]}, {"time": 0.4333, "x": 7.54, "y": -3.67, "curve": [0.465, 1.15, 0.499, -7.22, 0.465, -4.87, 0.499, 2.5]}, {"time": 0.5333, "x": -5.19, "y": -2.67, "curve": [0.579, -2.56, 0.625, -6.58, 0.579, -9.33, 0.625, -8.44]}, {"time": 0.6667, "x": -2.28, "y": -7.66, "curve": [0.678, -1.12, 0.689, -9.01, 0.678, -7.44, 0.689, -13.57]}, {"time": 0.7, "x": -7.79, "y": -12.59, "curve": [0.712, -6.47, 0.723, -28.45, 0.712, -11.51, 0.723, -8.55]}, {"time": 0.7333, "x": -25.36, "y": -5.84, "curve": [0.745, -21.82, 0.757, -32.36, 0.745, -2.75, 0.757, -6.17]}, {"time": 0.7667, "x": -32.36, "y": -6.17, "curve": [0.793, -32.36, 0.829, -3.01, 0.793, -6.17, 0.829, -7.46]}, {"time": 0.8667, "x": -4.64, "y": -8.24, "curve": [0.927, -7.19, 0.993, 11.57, 0.927, -9.45, 0.993, -0.54]}, {"time": 1.0333, "x": 11.57, "y": -0.54}]}, "L_arm_2": {"rotate": [{"value": 139.17, "curve": [0.076, 139.17, 0.16, 125.9]}, {"time": 0.2, "value": 125.9, "curve": [0.222, 125.9, 0.245, 118.81]}, {"time": 0.2667, "value": 109.36, "curve": "stepped"}, {"time": 0.3, "value": 0.58, "curve": [0.355, -61.06, 0.403, -130.4]}, {"time": 0.4333, "value": -130.4, "curve": [0.493, -128.98, 0.575, -127.21]}, {"time": 0.6333, "value": -125.77, "curve": [0.673, -166.53, 0.728, -217.36]}, {"time": 0.7667, "value": -258.46, "curve": [0.787, -269.24, 0.814, -282.69]}, {"time": 0.8333, "value": -293.56, "curve": [0.85, -293.56, 0.879, -273.63]}, {"time": 0.9, "value": -266.99, "curve": "stepped"}, {"time": 0.9333, "value": -182.6, "curve": [0.967, -182.6, 1.001, -195.51]}, {"time": 1.0333, "value": -210.27}], "translate": [{"x": -54.48, "y": 4.65, "curve": [0.076, -54.48, 0.16, -92.81, 0.076, 4.65, 0.16, 49.22]}, {"time": 0.2, "x": -92.81, "y": 49.22, "curve": [0.222, -92.81, 0.245, -88.64, 0.222, 49.22, 0.245, 50.85]}, {"time": 0.2667, "x": -83.07, "y": 53.03, "curve": "stepped"}, {"time": 0.3, "x": -7.24, "y": -3.02, "curve": [0.355, -29.74, 0.403, -55.04, 0.355, -18.06, 0.403, -34.98]}, {"time": 0.4333, "x": -55.04, "y": -34.98, "curve": [0.493, -56.36, 0.575, -58.01, 0.493, -30.46, 0.575, -24.82]}, {"time": 0.6333, "x": -59.34, "y": -20.25, "curve": [0.693, -49.47, 0.775, -37.16, 0.693, -6.22, 0.775, 11.27]}, {"time": 0.8333, "x": -27.2, "y": 25.42, "curve": [0.85, -27.2, 0.879, -19.7, 0.85, 25.42, 0.879, 41.02]}, {"time": 0.9, "x": -17.2, "y": 46.22, "curve": "stepped"}, {"time": 0.9333, "x": -60.48, "y": -1.02, "curve": [0.967, -60.48, 1.001, -59.97, 0.967, -1.02, 1.001, 7.23]}, {"time": 1.0333, "x": -59.39, "y": 16.66}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3, "y": -1, "curve": "stepped"}, {"time": 0.9, "y": -1, "curve": "stepped"}, {"time": 0.9333}]}, "L_brow": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": [0.275, 0, 0.311, 5.84]}, {"time": 0.5, "value": 5.84, "curve": "stepped"}, {"time": 0.9, "value": 5.84, "curve": [0.932, 5.84, 0.988, 2.01]}, {"time": 1.0333}], "translate": [{"x": 7.25, "y": -0.95, "curve": [0.069, 7.25, 0.136, -24.43, 0.069, -0.95, 0.136, 33.61]}, {"time": 0.2, "x": -24.43, "y": 33.61, "curve": [0.253, -24.43, 0.286, 9.81, 0.253, 33.61, 0.286, 63.03]}, {"time": 0.3667, "x": 32.19, "y": 61.36, "curve": [0.401, 41.63, 0.444, 101.89, 0.401, 60.65, 0.444, 49.77]}, {"time": 0.5, "x": 101.89, "y": 49.77, "curve": [0.543, 101.89, 0.604, 127.6, 0.543, 49.77, 0.604, 30.72]}, {"time": 0.6667, "x": 94.5, "y": 22.49, "curve": [0.7, 77.15, 0.734, 92.91, 0.7, 18.18, 0.734, 15.86]}, {"time": 0.7667, "x": 73.18, "y": 16.74, "curve": [0.818, 42.31, 0.865, -26.55, 0.818, 18.12, 0.865, 23.8]}, {"time": 0.9, "x": -26.55, "y": 23.8, "curve": [0.933, -26.55, 1, -35.16, 0.933, 23.8, 1, -11.69]}, {"time": 1.0333, "x": -35.16, "y": -11.69}]}, "L_fingeL_5": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": [0.217, 0, 0.25, 17.75]}, {"time": 0.2667, "value": 17.75, "curve": [0.283, 17.75, 0.317, 0]}, {"time": 0.3333, "curve": [0.342, 0, 0.354, -6.61]}, {"time": 0.3667, "value": -14.32, "curve": [0.433, -14.32, 0.567, 0]}, {"time": 0.6333, "curve": [0.685, 0, 0.757, -18.84]}, {"time": 0.8333, "value": -40.81, "curve": [0.883, -40.81, 0.983, 0]}, {"time": 1.0333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.0333}]}, "R_leg2": {"rotate": [{"value": 5.75, "curve": [0.044, 5.75, 0.089, -22.56]}, {"time": 0.1333, "value": -22.56, "curve": [0.222, -22.56, 0.311, -2.12]}, {"time": 0.4, "value": -2.12, "curve": [0.489, -2.12, 0.578, -7.85]}, {"time": 0.6667, "value": -7.85, "curve": [0.744, -7.85, 0.822, 5.75]}, {"time": 0.9, "value": 5.75, "curve": "stepped"}, {"time": 1.0333, "value": 5.75}], "translate": [{"x": -0.6, "y": 0.6, "curve": [0.044, -0.6, 0.089, -14.79, 0.044, 0.6, 0.089, 15.91]}, {"time": 0.1333, "x": -14.79, "y": 15.94, "curve": [0.222, -14.79, 0.311, 17.28, 0.222, 15.98, 0.311, 15.98]}, {"time": 0.4, "x": 17.28, "y": 15.98, "curve": [0.489, 17.28, 0.578, -0.6, 0.489, 15.98, 0.578, 0.6]}, {"time": 0.6667, "x": -0.6, "y": 0.6, "curve": "stepped"}, {"time": 0.9, "x": -0.6, "y": 0.6, "curve": "stepped"}, {"time": 1.0333, "x": -0.6, "y": 0.6}]}, "L_hand_6": {"rotate": [{"value": -45.31, "curve": [0.05, -45.31, 0.064, -12.69]}, {"time": 0.2, "value": -12.69, "curve": [0.225, -12.69, 0.275, -20.01]}, {"time": 0.3, "value": -20.01, "curve": [0.333, -20.01, 0.4, 11.84]}, {"time": 0.4333, "value": 11.84, "curve": [0.493, 26.02, 0.575, 43.7]}, {"time": 0.6333, "value": 58, "curve": [0.673, 103.52, 0.728, 160.29]}, {"time": 0.7667, "value": 206.2, "curve": [0.787, 240.86, 0.814, 284.09]}, {"time": 0.8333, "value": 319.05, "curve": [0.883, 319.05, 0.983, 314.69]}, {"time": 1.0333, "value": 314.69}], "translate": [{"x": 1.83, "y": -7.01, "curve": [0.05, 1.83, 0.064, -10.52, 0.05, -7.01, 0.064, -6.79]}, {"time": 0.2, "x": -10.52, "y": -6.79, "curve": [0.225, -10.52, 0.263, -15.95, 0.225, -6.79, 0.263, -2.79]}, {"time": 0.3, "x": -21.38, "y": 1.21, "curve": [0.35, -1.53, 0.4, 18.32, 0.35, 1.39, 0.4, 1.57]}, {"time": 0.4333, "x": 18.32, "y": 1.57, "curve": [0.493, 12.48, 0.575, 5.19, 0.493, 4.55, 0.575, 8.26]}, {"time": 0.6333, "x": -0.7, "y": 11.26, "curve": [0.693, -1.71, 0.775, -2.97, 0.693, 5.92, 0.775, -0.73]}, {"time": 0.8333, "x": -3.99, "y": -6.12, "curve": [0.883, -3.99, 0.983, 1.83, 0.883, -6.12, 0.983, -7.01]}, {"time": 1.0333, "x": 1.83, "y": -7.01}]}, "R_foot2": {"rotate": [{"value": -2.29, "curve": [0.044, -2.29, 0.089, 6.26]}, {"time": 0.1333, "value": 6.26, "curve": [0.2, 6.26, 0.267, -34.94]}, {"time": 0.3333, "value": -34.94, "curve": [0.467, -34.94, 0.6, -2.29]}, {"time": 0.7333, "value": -2.29, "curve": "stepped"}, {"time": 0.9333, "value": -2.29, "curve": "stepped"}, {"time": 1.0333, "value": -2.29}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.0333}]}, "L_fingeL_4": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": [0.217, 0, 0.25, 17.75]}, {"time": 0.2667, "value": 17.75, "curve": [0.283, 17.75, 0.317, 0]}, {"time": 0.3333, "curve": [0.342, 0, 0.354, -6.61]}, {"time": 0.3667, "value": -14.32, "curve": [0.433, -14.32, 0.567, 0]}, {"time": 0.6333, "curve": [0.685, 0, 0.757, -18.84]}, {"time": 0.8333, "value": -40.81, "curve": [0.883, -40.81, 0.983, 0]}, {"time": 1.0333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.0333}]}, "R_finger_3": {"rotate": [{"curve": "stepped"}, {"time": 0.2, "curve": [0.25, 0, 0.306, -5.38]}, {"time": 0.3667, "value": -14.72, "curve": "stepped"}, {"time": 0.6333, "value": -14.72, "curve": "stepped"}, {"time": 0.8333, "value": -14.72, "curve": [0.883, -14.72, 0.983, 0]}, {"time": 1.0333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.0333}]}, "R_foot_finger_2": {"rotate": [{"value": 10.54, "curve": [0.023, 10.54, 0.029, -28.65]}, {"time": 0.1, "value": -28.65, "curve": [0.175, -28.65, 0.25, 21.16]}, {"time": 0.3, "value": 21.16, "curve": [0.367, 21.16, 0.5, 3.59]}, {"time": 0.5667, "value": 3.59, "curve": [0.608, 3.59, 0.692, 10.54]}, {"time": 0.7333, "value": 10.54, "curve": "stepped"}, {"time": 1.0333, "value": 10.54}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.0333}]}, "L_eye": {"rotate": [{"curve": "stepped"}, {"time": 1.0333}], "translate": [{"x": -2.07, "y": -4.13, "curve": [0.092, -2.07, 0.275, 15.89, 0.092, -4.13, 0.275, -7.86]}, {"time": 0.3667, "x": 15.89, "y": -7.86, "curve": [0.386, 15.89, 0.409, 11.49, 0.386, -7.86, 0.409, -17.39]}, {"time": 0.4333, "x": 5.04, "y": -17.86, "curve": [0.465, -2.83, 0.499, -12.87, 0.465, -18.43, 0.499, -10.34]}, {"time": 0.5333, "x": -10.83, "y": -15.52, "curve": [0.579, -8.21, 0.625, -10.68, 0.579, -22.18, 0.625, -17.76]}, {"time": 0.6667, "x": -4.9, "y": -13.6, "curve": [0.678, -3.34, 0.689, -10.83, 0.678, -12.48, 0.689, -17.71]}, {"time": 0.7, "x": -9.25, "y": -15.91, "curve": [0.712, -7.54, 0.723, -29.18, 0.712, -13.96, 0.723, -10.21]}, {"time": 0.7333, "x": -25.83, "y": -6.92, "curve": [0.745, -22.01, 0.757, -32.36, 0.745, -3.16, 0.757, -6.17]}, {"time": 0.7667, "x": -32.36, "y": -6.17, "curve": [0.793, -32.36, 0.829, -3.01, 0.793, -6.17, 0.829, -7.46]}, {"time": 0.8667, "x": -4.64, "y": -8.24, "curve": [0.927, -7.19, 0.993, 11.57, 0.927, -9.45, 0.993, -0.54]}, {"time": 1.0333, "x": 11.57, "y": -0.54}]}, "banana_c": {"rotate": [{"curve": "stepped"}, {"time": 1.0333}], "translate": [{"curve": [0.013, -16.27, 0.044, -65.94, 0.013, 0.3, 0.044, 1.23]}, {"time": 0.1, "x": -65.94, "y": 1.23, "curve": [0.148, -65.94, 0.164, 1057.12, 0.148, 1.23, 0.164, -39.98]}, {"time": 0.4333, "x": 1057.12, "y": -39.98, "curve": [0.611, 1066.07, 0.789, 239.38, 0.611, -40.34, 0.789, -7]}, {"time": 0.9333, "x": -706.46, "y": 31.14, "curve": "stepped"}, {"time": 1.0333, "x": -706.46, "y": 31.14}]}, "R_hand_c_2": {"rotate": [{"curve": [0.089, 26.79, 0.178, 80.37]}, {"time": 0.2667, "value": 80.37, "curve": [0.278, 80.37, 0.289, -38.67]}, {"time": 0.3, "value": -38.67, "curve": [0.311, -38.67, 0.322, 57.09]}, {"time": 0.3333, "value": 57.09, "curve": [0.344, 57.09, 0.356, 49.85]}, {"time": 0.3667, "value": 49.85, "curve": [0.389, 49.85, 0.411, 68.31]}, {"time": 0.4333, "value": 68.31, "curve": [0.478, 68.31, 0.522, 68.31]}, {"time": 0.5667, "value": 57.95, "curve": [0.578, 55.36, 0.589, -26.17]}, {"time": 0.6, "value": -41.32, "curve": [0.622, -71.62, 0.644, -70.41]}, {"time": 0.6667, "value": -78.4, "curve": [0.7, -90.38, 0.733, -101.24]}, {"time": 0.7667, "value": -101.24, "curve": [0.811, -101.24, 0.856, -90.96]}, {"time": 0.9, "value": -85.82}], "translate": [{"curve": [0.089, 20.26, 0.178, 60.79, 0.089, 10.95, 0.178, 5.16]}, {"time": 0.2667, "x": 60.79, "y": 32.86, "curve": [0.278, 60.79, 0.289, -1.62, 0.278, 36.32, 0.289, 44.02]}, {"time": 0.3, "x": -43.51, "y": 93.49, "curve": [0.311, -85.4, 0.322, -190.54, 0.311, 142.95, 0.322, 277.33]}, {"time": 0.3333, "x": -190.54, "y": 329.63, "curve": [0.344, -190.54, 0.356, -129.14, 0.344, 381.93, 0.356, 392.81]}, {"time": 0.3667, "x": -129.14, "y": 407.29, "curve": [0.389, -129.14, 0.411, -199.65, 0.389, 436.27, 0.411, 460.01]}, {"time": 0.4333, "x": -199.65, "y": 460.01, "curve": [0.478, -199.65, 0.522, -82.54, 0.478, 460.01, 0.522, 393.67]}, {"time": 0.5667, "x": -23.09, "y": 298.18, "curve": [0.578, -8.23, 0.589, 14.54, 0.578, 274.3, 0.589, 133.89]}, {"time": 0.6, "x": 23.27, "y": 101.92, "curve": [0.622, 40.73, 0.644, 45.41, 0.622, 37.99, 0.644, 32.39]}, {"time": 0.6667, "x": 55.48, "y": 10.48, "curve": [0.7, 70.58, 0.733, 78.04, 0.7, -22.38, 0.733, -41.68]}, {"time": 0.7667, "x": 98.79, "y": -62.38, "curve": [0.789, 112.62, 0.811, 143.68, 0.789, -76.18, 0.811, -87.48]}, {"time": 0.8333, "x": 159.19, "y": -93.02, "curve": [0.9, 205.72, 0.967, 243.02, 0.9, -109.62, 0.967, -116.87]}, {"time": 1.0333, "x": 284.93, "y": -128.79}]}, "L_hand_c_2": {"rotate": [{"curve": [0.089, 25.42, 0.178, 76.27]}, {"time": 0.2667, "value": 76.27, "curve": [0.322, 76.27, 0.378, 27.92]}, {"time": 0.4333, "value": 27.92, "curve": [0.478, 27.92, 0.522, 67.13]}, {"time": 0.5667, "value": 67.13, "curve": [0.6, 67.13, 0.633, 43.64]}, {"time": 0.6667, "value": 43.64, "curve": [0.711, 43.64, 0.756, 53.07]}, {"time": 0.8, "value": 53.07, "curve": "stepped"}, {"time": 0.8667, "value": 53.07, "curve": [0.889, 53.07, 0.911, 29.38]}, {"time": 0.9333, "value": 29.38, "curve": [0.967, 29.38, 1, 45.17]}, {"time": 1.0333, "value": 53.07}], "translate": [{"curve": [0.089, -78.35, 0.178, -151.22, 0.089, -17.18, 0.178, -51.53]}, {"time": 0.2667, "x": -235.04, "y": -51.53, "curve": [0.311, -276.96, 0.356, -328.98, 0.311, -51.53, 0.356, 40.66]}, {"time": 0.4, "x": -377.22, "y": 86.7, "curve": [0.411, -389.27, 0.422, -412.13, 0.411, 98.21, 0.422, 111.02]}, {"time": 0.4333, "x": -415.91, "y": 121.12, "curve": [0.444, -419.69, 0.456, -419.69, 0.444, 131.21, 0.456, 147.28]}, {"time": 0.4667, "x": -419.69, "y": 147.28, "curve": [0.5, -419.69, 0.533, -405.97, 0.5, 147.28, 0.533, 81.14]}, {"time": 0.5667, "x": -357.28, "y": 74.36, "curve": [0.6, -308.59, 0.633, -163.41, 0.6, 67.58, 0.633, 74.36]}, {"time": 0.6667, "x": -127.54, "y": 67.58, "curve": [0.689, -103.63, 0.711, -103.63, 0.689, 63.06, 0.711, 44.63]}, {"time": 0.7333, "x": -103.63, "y": 25.76, "curve": [0.756, -103.63, 0.778, -103.88, 0.756, 6.89, 0.778, -35.96]}, {"time": 0.8, "x": -118.55, "y": -45.64, "curve": [0.822, -133.23, 0.844, -178.12, 0.822, -55.31, 0.844, -50.8]}, {"time": 0.8667, "x": -191.7, "y": -55.31, "curve": [0.889, -205.27, 0.911, -199.24, 0.889, -59.83, 0.911, -66.69]}, {"time": 0.9333, "x": -205.27, "y": -72.72, "curve": [0.967, -214.31, 1, -226.34, 0.967, -81.75, 1, -91.24]}, {"time": 1.0333, "x": -236.88, "y": -100.49}]}, "R_hand_c_1": {"rotate": [{"curve": [0.067, 0, 0.2, -39.19]}, {"time": 0.2667, "value": -39.19, "curve": [0.3, -39.19, 0.367, -101.28]}, {"time": 0.4, "value": -101.28, "curve": [0.45, -101.28, 0.55, -88.47]}, {"time": 0.6, "value": -88.47, "curve": [0.625, -88.47, 0.675, -82.84]}, {"time": 0.7, "value": -82.84, "curve": "stepped"}, {"time": 0.8, "value": -82.84}], "translate": [{"curve": [0.067, 0, 0.2, -11.96, 0.067, 0, 0.2, -17.77]}, {"time": 0.2667, "x": -11.96, "y": -17.77, "curve": [0.3, -11.96, 0.367, -33.91, 0.3, -17.77, 0.367, -17.01]}, {"time": 0.4, "x": -33.91, "y": -17.01, "curve": [0.45, -33.91, 0.55, 5.38, 0.45, -17.01, 0.55, -56.12]}, {"time": 0.6, "x": 5.38, "y": -56.12, "curve": [0.625, 5.38, 0.675, 21.7, 0.625, -56.12, 0.675, -41.09]}, {"time": 0.7, "x": 21.7, "y": -41.09, "curve": [0.725, 21.7, 0.775, 19.37, 0.725, -41.09, 0.775, -22.16]}, {"time": 0.8, "x": 19.37, "y": -22.16}]}, "L_hand_c_1": {"rotate": [{"curve": [0.067, 0, 0.2, -4.62]}, {"time": 0.2667, "value": -4.62, "curve": [0.324, -4.62, 0.415, 18.68]}, {"time": 0.5, "value": 37.32, "curve": [0.564, 14.44, 0.624, -4.62]}, {"time": 0.6667, "value": -4.62, "curve": [0.717, -4.62, 0.817, -17.55]}, {"time": 0.8667, "value": -17.55}], "translate": [{"curve": [0.067, 0, 0.2, -2.57, 0.067, 0, 0.2, 28.01]}, {"time": 0.2667, "x": -2.57, "y": 28.01, "curve": [0.324, -2.57, 0.415, 2.02, 0.324, 28.01, 0.415, 44.7]}, {"time": 0.5, "x": 5.68, "y": 58.05, "curve": [0.564, 1.18, 0.624, -2.57, 0.564, 41.66, 0.624, 28.01]}, {"time": 0.6667, "x": -2.57, "y": 28.01, "curve": "stepped"}, {"time": 0.8667, "x": -2.57, "y": 28.01}]}, "tail": {"rotate": [{}, {"time": 0.4, "value": 32.99}, {"time": 0.8, "value": 2.39}, {"time": 1.0333}]}, "tail base": {"rotate": [{}, {"time": 0.2, "value": 13.28}, {"time": 0.4667, "value": -17.77}, {"time": 1.0333}], "translate": [{"time": 0.2}, {"time": 0.4667, "x": -18.57, "y": -176.45, "curve": "stepped"}, {"time": 0.7333, "x": -18.57, "y": -176.45}, {"time": 1.0333}]}}, "transform": {"L_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0, "curve": "stepped"}, {"time": 0.2333, "mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0, "curve": [0.256, 0, 0.278, 0.67, 0.256, 0, 0.278, 0.67, 0.256, 0, 0.278, 0.67, 0.256, 0, 0.278, 0.67, 0.256, 0, 0.278, 0.67, 0.256, 0, 0.278, 0.67]}, {"time": 0.3}], "R_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0, "curve": "stepped"}, {"time": 0.2333, "mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0, "curve": [0.256, 0, 0.278, 0.67, 0.256, 0, 0.278, 0.67, 0.256, 0, 0.278, 0.67, 0.256, 0, 0.278, 0.67, 0.256, 0, 0.278, 0.67, 0.256, 0, 0.278, 0.67]}, {"time": 0.3}]}, "path": {"L_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0, "curve": "stepped"}, {"time": 0.2333, "mixRotate": 0, "mixX": 0, "curve": [0.256, 0, 0.278, 1, 0.256, 0, 0.278, 1, 0.256, 0, 0.278, 1]}, {"time": 0.3}]}, "R_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0, "curve": "stepped"}, {"time": 0.2333, "mixRotate": 0, "mixX": 0, "curve": [0.256, 0, 0.278, 1, 0.256, 0, 0.278, 1, 0.256, 0, 0.278, 1]}, {"time": 0.3}]}}, "attachments": {"default": {"head": {"monkey new/head": {"deform": [{"time": 0.1, "curve": [0.111, 0, 0.124, 1]}, {"time": 0.1333, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": "stepped"}, {"time": 0.2667, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": [0.272, 0, 0.311, 1]}, {"time": 0.3333}]}}, "L_hand_spline": {"L_hand_spline": {"deform": [{"curve": "stepped"}, {"time": 0.2667, "curve": [0.289, 0, 0.311, 1]}, {"time": 0.3333, "offset": 6, "vertices": [45.46436, -36.54021, -56.85041, 67.63202, -592.3539, 84.3313], "curve": [0.367, 0, 0.423, 1]}, {"time": 0.4333, "vertices": [75.37566, 20.1938, 0, 0, -117.44333, -41.95375, 19.90451, -201.91098, -41.44551, 49.3056, -563.4349, 530.9757], "curve": [0.444, 0, 0.456, 1]}, {"time": 0.4667, "vertices": [113.91019, 41.86969, 0, 0, -157.67361, -63.00906, -23.29182, -93.11394, -31.5891, 37.57994, -301.64282, 636.37115], "curve": [0.5, 0, 0.533, 1]}, {"time": 0.5667, "vertices": [107.6085, -57.11588, 0, 0, -115.90843, 35.46012, -3.96707, -62.56441, -12.96265, 15.421, 6.93921, 809.58575], "curve": [0.589, 0, 0.611, 1]}, {"time": 0.6333, "vertices": [-54.13553, -59.7841, 0, 0, 89.75018, 98.7539, -25.64429, -43.37576, 0, 0, 473.9501, 672.29315], "curve": [0.678, 0, 0.722, 1]}, {"time": 0.7667, "vertices": [-17.85054, -21.50287, 0, 0, 28.85746, 33.03732, -35.75528, -27.67271, 0, 0, 214.9952, 390.45883], "curve": [0.8, 0, 0.833, 1]}, {"time": 0.8667, "vertices": [-3.80672, -1.19175, 0, 0, 9.99442, -6.52224, 73.77628, -48.55659, -49.56529, 32.77007, -290.8686, -118.08383], "curve": "stepped"}, {"time": 1.0333, "vertices": [-3.80672, -1.19175, 0, 0, 9.99442, -6.52224, 73.77628, -48.55659, -49.56529, 32.77007, -290.8686, -118.08383]}]}}, "monkey_mouth angry": {"monkey new/monkey_mouth angry": {"deform": [{"time": 0.1667}, {"time": 0.2333, "vertices": [-1.17473, -1.54317, -1.17483, -1.54317, -0.41172, -0.54091, -0.41182, -0.54094, 0, 0, 0, 0, 0, 0, 0, 0, 0.10404, -0.00366, 0.10398, -0.00367, 14.06284, -0.49335, 14.06274, -0.49334, 7.6233, -0.26743, 7.62323, -0.26744, 15.46686, -16.54249, 15.46656, -16.54252, 6.2486, -9.74724, 6.24852, -9.74723, 0.70818, -1.10455, 0.7081, -1.10458, 0, 0, 0, 0, -0.31367, -0.41213, -0.31379, -0.41214, -1.52003, -3.82923, -1.52022, -3.82926, 0.56046, -9.16548, 0.56027, -9.16551, 6.11253, -14.45808, 6.11217, -14.45812, 9.94184, -17.87662, 9.94155, -17.8767, 13.29081, -20.65813, 13.29047, -20.65822, 16.42549, -24.97956, 16.42527, -24.9796, 13.37118, -21.73329, 13.37089, -21.73329, 9.70808, -17.69413, 9.70772, -17.69419, 4.99123, -11.9393, 4.99096, -11.93933, 0.7248, -4.68637, 0.72452, -4.68641, 1.62262, -1.88308, 1.62245, -1.88312, -0.3444, -1.70176, -0.34463, -1.7018, 6.36536, -0.36208, 6.36515, -0.36211, 7.54922, -0.55916, 7.54903, -0.5592, 11.92958, -0.4185, 11.92949, -0.41851, 10.86577, -0.38119, 10.86569, -0.38118, 14.17754, -0.49736, 14.17743, -0.49738, 15.87381, -0.55687, 15.87374, -0.55688, 17.92982, -0.629, 17.92978, -0.62901, 15.34803, -0.53842, 15.34792, -0.53843]}, {"time": 0.2667, "vertices": [-1.17473, -1.54317, -1.17483, -1.54317, -0.41172, -0.54091, -0.41182, -0.54094, 0, 0, 0, 0, 0, 0, 0, 0, 0.10404, -0.00366, 0.10398, -0.00367, 17.32421, 4.09042, 17.32416, 4.09041, 9.87391, 2.89573, 9.87384, 2.89569, 14.93251, -26.12864, 14.9323, -26.12878, 5.35965, -25.69613, 5.35967, -25.69622, 0.44386, -5.84625, 0.44385, -5.84643, 0, 0, 0, 0, -0.31367, -0.41213, -0.31379, -0.41214, -1.60998, -5.44157, -1.61006, -5.44167, 0.1004, -17.41905, 0.1003, -17.4192, 5.24053, -30.1022, 5.24028, -30.10233, 8.71918, -39.81255, 8.719, -39.81276, 11.76298, -48.0702, 11.76272, -48.0704, 15.14964, -47.87005, 15.14953, -47.87017, 12.26776, -41.53105, 12.2675, -41.53119, 8.88238, -32.50819, 8.88208, -32.50839, 4.49929, -20.76528, 4.49905, -20.76541, 0.8642, -6.84334, 0.864, -6.84355, 3.2413, 0.39195, 3.24115, 0.39189, 1.05388, 0.26347, 1.05366, 0.26342, 9.12529, 3.51692, 9.1251, 3.51687, 10.50877, 3.60043, 10.50861, 3.60034, 15.89375, 5.15301, 15.89368, 5.15296, 14.64028, 4.92378, 14.64024, 4.92376, 18.39648, 5.43221, 18.39639, 5.43218, 20.84448, 6.42921, 20.84442, 6.42917, 22.41179, 5.67025, 22.41177, 5.67022, 19.48181, 5.27147, 19.48172, 5.27143]}, {"time": 0.3333}]}}, "monkey_mouth o": {"monkey new/monkey_mouth o": {"deform": [{"time": 0.3, "offset": 28, "vertices": [5.902, -1.83607, 5.90199, -1.83612, 16.66796, -5.64338, 16.66801, -5.6435, 26.98745, -13.80212, 26.98751, -13.80224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.42729, -3.86908, 10.4274, -3.8692, 19.97005, -7.86111, 19.97014, -7.86125, 22.81251, -8.09716, 22.81258, -8.09729, 11.56286, -3.59717, 11.5629, -3.59723, 7.27706, -2.26384, 7.27708, -2.26391, 4.36782, -1.35877, 4.36785, -1.35884, 1.65257, -0.51407, 1.65258, -0.51411, 0.88341, -0.27478, 0.88341, -0.27482, 3.15026, -0.98, 3.15029, -0.98005], "curve": [0.322, 0, 0.344, 1]}, {"time": 0.3667, "curve": [0.4, 0, 0.368, 1]}, {"time": 0.4667, "vertices": [2.02875, 3.58424, 2.02901, 3.58421, 41.92592, 0.34519, 41.92616, 0.34501, 43.47891, -8.6626, 43.47884, -8.66275, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -71.65257, 11.26435, -71.65195, 11.2642, -57.18196, 26.13006, -57.18167, 26.12982, -31.92245, 15.83664, -31.92116, 15.83648, -2.23189, 1.26908, -2.23144, 1.26904, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.22844, 15.29345, -1.228, 15.29335, -11.46504, 15.74349, -11.46419, 15.74334, -36.50731, 12.52378, -36.50547, 12.52359, -29.8886, -1.95406, -29.88689, -1.95422, -13.38759, -14.2605, -13.3871, -14.26055, -14.36021, -10.96493, -14.35993, -10.965, -2.49518, -5.92721, -2.49454, -5.92726, 0.12289, -0.46465, 0.12334, -0.4647, 2.99948, 4.93354, 3, 4.93349], "curve": "stepped"}, {"time": 0.7, "vertices": [2.02875, 3.58424, 2.02901, 3.58421, 41.92592, 0.34519, 41.92616, 0.34501, 43.47891, -8.6626, 43.47884, -8.66275, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -71.65257, 11.26435, -71.65195, 11.2642, -57.18196, 26.13006, -57.18167, 26.12982, -31.92245, 15.83664, -31.92116, 15.83648, -2.23189, 1.26908, -2.23144, 1.26904, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.22844, 15.29345, -1.228, 15.29335, -11.46504, 15.74349, -11.46419, 15.74334, -36.50731, 12.52378, -36.50547, 12.52359, -29.8886, -1.95406, -29.88689, -1.95422, -13.38759, -14.2605, -13.3871, -14.26055, -14.36021, -10.96493, -14.35993, -10.965, -2.49518, -5.92721, -2.49454, -5.92726, 0.12289, -0.46465, 0.12334, -0.4647, 2.99948, 4.93354, 3, 4.93349], "curve": [0.744, 0, 0.822, 1]}, {"time": 0.8333, "offset": 28, "vertices": [6.8802, -1.3913, 6.88019, -1.39131, 15.76192, -4.80061, 15.76189, -4.80062, 25.78085, -12.08836, 25.78096, -12.08838, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.58142, -1.59557, 6.58141, -1.59557, 14.40101, -4.52587, 14.40106, -4.52589, 15.98287, -4.23659, 15.98297, -4.2366, 8.12596, -1.64323, 8.12604, -1.64324, 5.14012, -1.03944, 5.1402, -1.03945, 2.84422, -0.57515, 2.8443, -0.57517, 1.19243, -0.24113, 1.19242, -0.24115, 0.82856, -0.16754, 0.82855, -0.16756, 2.52441, -0.51048, 2.52449, -0.51048], "curve": [0.878, 0, 0.922, 1]}, {"time": 0.9667, "offset": 28, "vertices": [-3.26576, 1.83011, -3.2655, 1.83002, 5.25847, -3.56183, 5.25848, -3.56188, 25.78085, -12.08836, 25.78096, -12.08838, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.4286, -1.69951, 9.42863, -1.69952, 21.1927, -5.91422, 21.19288, -5.91427, 22.82653, -4.2013, 22.82677, -4.20135, 15.40885, 1.30268, 15.40911, 1.30261, 11.36238, -2.40701, 11.36251, -2.40704, 8.60099, 0.92529, 8.60117, 0.92526, 1.19243, -0.24113, 1.19242, -0.24115, 0.82856, -0.16754, 0.82855, -0.16756, 2.52441, -0.51048, 2.52449, -0.51048]}]}}, "monkey_right_hand": {"monkey new/monkey_right_hand": {"deform": [{"time": 0.6, "vertices": [-42.90184, -27.17498, -52.91936, -12.65083, -37.51963, 37.7781, -28.52212, 45.43459, -13.27825, 39.84474, 12.19791, 53.11275, 22.38596, 49.38632, 52.74242, -2.23877, 44.79075, -27.18495, 28.41784, -46.57342, 4.91267, -51.05005, -17.09184, -45.26394], "curve": [0.622, 0, 0.644, 1]}, {"time": 0.6667, "vertices": [-13.2543, 22.91414, -13.25427, 22.91413, -13.2543, 22.91414, -13.2543, 22.91414, -13.25428, 22.91413, -13.2543, 22.91414, -13.2543, 22.91412, -13.25428, 22.91412, -13.25427, 22.91412, -13.2543, 22.91414, -13.25427, 22.91413, -13.2543, 22.91412]}]}}, "R_hand_spline": {"R_hand_spline": {"deform": [{"curve": "stepped"}, {"time": 0.3, "curve": [0.333, 0, 0.367, 1]}, {"time": 0.4, "vertices": [-106.01997, 58.51639, 0, 0, 130.6737, -104.04964, -123.99382, -199.56183, 0, 0, 38.19003, -243.79892], "curve": [0.426, 0, 0.466, 0.3]}, {"time": 0.5, "vertices": [-86.41229, 35.74129, 9.2695, 1.09599, 153.64922, -135.4558, -75.81712, -160.53261, 17.58855, 1.22305, 14.48956, -146.13124], "curve": [0.529, 0.79, 0.555, 1]}, {"time": 0.5667, "vertices": [-79.65539, 14.23963, 16.37392, 1.93599, 131.56033, -27.07488, -38.893, -130.6194, 31.06896, 2.16043, -3.67522, -71.27569], "curve": [0.578, 0, 0.589, 1]}, {"time": 0.6, "vertices": [-58.51627, 43.80947, 9.30908, 15.03894, 87.8484, -13.67762, -22.29016, -8.80339, 53.09473, 21.03746, 548.7324, 164.0152], "curve": [0.611, 0, 0.622, 1]}, {"time": 0.6333, "vertices": [-49.75108, 56.0704, 6.3797, 20.47198, 69.72356, -8.12254, -70.71706, -6.92322, 62.22755, 28.86469, 774.50543, 220.61612], "curve": [0.7, 0, 0.767, 1]}, {"time": 0.8333, "vertices": [-58.43848, 43.30479, 22.65725, 25.05527, 118.68332, -47.76932, -68.27036, -223.1461, 62.24887, 39.43907, 784.868, 655.7247], "curve": [0.9, 0, 0.967, 1]}, {"time": 1.0333, "vertices": [-69.03403, 39.28627, 22.65725, 25.05527, 162.57172, -60.39627, -119.70172, -140.55115, 62.24887, 39.43907, 900.05896, 576.45294]}]}}}}, "drawOrder": [{}, {"time": 0.0333, "offsets": [{"slot": "monkey_right_arm", "offset": 1}]}, {"time": 0.0667, "offsets": [{"slot": "monkey_right_arm", "offset": 1}, {"slot": "monkey_right_hand", "offset": 7}]}, {"time": 0.2667, "offsets": [{"slot": "monkey_right_arm", "offset": 7}, {"slot": "monkeyOLD_banana_3_1", "offset": -9}, {"slot": "monkeyOLD_banana_1", "offset": -9}, {"slot": "banana_3_2", "offset": -9}, {"slot": "banana_2_1", "offset": -9}, {"slot": "banana_2_2", "offset": -9}, {"slot": "banana_3_3", "offset": -9}, {"slot": "monkey_right_hand", "offset": 21}]}, {"time": 0.7333, "offsets": [{"slot": "monkey_right_arm", "offset": 1}]}, {"time": 1.0333, "offsets": [{"slot": "monkey_right_arm", "offset": 1}]}]}, "Tap": {"slots": {"banana_2_1": {"attachment": [{"name": "banana_2_1"}]}, "banana_2_2": {"attachment": [{"name": "banana_2_2"}]}, "banana_3_2": {"attachment": [{"name": "banana_3_2"}]}, "banana_3_3": {"attachment": [{"name": "banana_3_3"}]}, "closed_eye_left": {"attachment": [{"time": 0.0667, "name": "monkey new/closed_eye_left"}, {"time": 0.4}]}, "closed_eye_right": {"attachment": [{"time": 0.0667, "name": "monkey new/closed_eye_right"}, {"time": 0.4}]}, "monkeyOLD_banana_1": {"attachment": [{"name": "banana_1"}]}, "monkeyOLD_banana_3_1": {"attachment": [{"name": "banana_3_1"}]}, "monkey_hand left": {"attachment": [{"time": 0.0667}]}, "monkey_hand right": {"attachment": [{"time": 0.0667}]}, "monkey_left_arm": {"attachment": [{}, {"time": 0.0667, "name": "monkey new/monkey_left_arm"}]}, "monkey_left_hand": {"attachment": [{}]}, "monkey_left_hand closed": {"attachment": [{}, {"time": 0.0667, "name": "monkey new/monkey_left_hand closed"}]}, "monkey_mouth angry": {"attachment": [{}, {"time": 0.0667}, {"time": 0.5333}]}, "monkey_mouth o": {"attachment": [{}, {"time": 0.0667, "name": "monkey new/monkey_mouth o"}, {"time": 0.5333}]}, "monkey_mouth smile closed": {"attachment": [{"time": 0.0667}, {"time": 0.5333, "name": "monkey new/monkey_mouth smile closed"}]}, "monkey_mouth smile open": {"attachment": [{}]}, "monkey_right_hand": {"attachment": [{}]}}, "bones": {"Banana": {"rotate": [{"value": 0.03, "curve": [0.044, 0.03, 0.089, -8.21]}, {"time": 0.1333, "value": -9.7, "curve": [0.209, -12.25, 0.224, -14.54]}, {"time": 0.3, "value": -14.54, "curve": [0.367, -14.54, 0.433, -5.28]}, {"time": 0.5, "value": -3.04, "curve": [0.593, 0.11, 0.773, 0.03]}, {"time": 0.8667, "value": 0.03}], "translate": [{"x": 6.4, "y": -7.09, "curve": [0.044, 13.59, 0.089, 74.07, 0.044, -7.09, 0.089, 75.48]}, {"time": 0.1333, "x": 74.07, "y": 89.93, "curve": [0.209, 74.07, 0.224, -53.39, 0.209, 114.57, 0.224, 133.86]}, {"time": 0.3, "x": -53.39, "y": 133.86, "curve": [0.367, -53.39, 0.433, -15.38, 0.367, 133.86, 0.433, 44.26]}, {"time": 0.5, "x": -6.18, "y": 22.57, "curve": [0.593, 6.72, 0.773, -8.72, 0.593, -7.83, 0.773, -7.09]}, {"time": 0.8667, "x": 6.4, "y": -7.09}]}, "bone": {"rotate": [{"value": -44.99, "curve": [0.022, -45.76, 0.044, -47.29]}, {"time": 0.0667, "value": -47.29, "curve": [0.078, -47.29, 0.089, -44.53]}, {"time": 0.1, "value": -43.76, "curve": [0.189, -37.6, 0.278, -26.48]}, {"time": 0.3667, "value": -26.48, "curve": [0.511, -26.48, 0.656, -39.88]}, {"time": 0.8, "value": -47.34, "curve": [0.822, -48.49, 0.844, -50.66]}, {"time": 0.8667, "value": -52.31}]}, "root": {"translate": [{}]}, "L_hand_4": {"rotate": [{"value": -33.11, "curve": [0.064, -33.11, 0.089, 46.93]}, {"time": 0.1667, "value": 46.93, "curve": [0.281, 46.93, 0.327, -25.73]}, {"time": 0.4667, "value": -25.73, "curve": [0.568, -25.73, 0.609, -18.67]}, {"time": 0.7333, "value": -18.67}], "translate": [{"x": 14.95, "y": -54.65, "curve": [0.064, 14.95, 0.089, 9.62, 0.064, -54.65, 0.089, -20.06]}, {"time": 0.1667, "x": 9.62, "y": -20.06, "curve": [0.281, 9.62, 0.327, -9.92, 0.281, -20.06, 0.327, -31.4]}, {"time": 0.4667, "x": -9.92, "y": -31.4, "curve": "stepped"}, {"time": 0.7333, "x": -9.92, "y": -31.4}]}, "L_arm_2": {"rotate": [{"value": 132.11, "curve": [0.108, 132.11, 0.325, 151.32]}, {"time": 0.4333, "value": 151.32, "curve": [0.483, 151.32, 0.583, 144.52]}, {"time": 0.6333, "value": 144.52, "curve": [0.683, 144.52, 0.783, 139.17]}, {"time": 0.8333, "value": 139.17}], "translate": [{"x": -52.74, "y": 8.72, "curve": [0.108, -52.74, 0.325, -54.48, 0.108, 8.72, 0.325, 4.65]}, {"time": 0.4333, "x": -54.48, "y": 4.65, "curve": [0.483, -54.48, 0.583, -70.38, 0.483, 4.65, 0.583, 27.76]}, {"time": 0.6333, "x": -70.38, "y": 27.76, "curve": [0.683, -70.38, 0.783, -54.48, 0.683, 27.76, 0.783, 4.65]}, {"time": 0.8333, "x": -54.48, "y": 4.65}]}, "L_hand_6": {"rotate": [{"value": -55.93, "curve": [0.042, -55.93, 0.125, -12.69]}, {"time": 0.1667, "value": -12.69, "curve": [0.258, -12.69, 0.324, -12.07]}, {"time": 0.4, "value": -12.07, "curve": [0.502, -12.07, 0.542, -50.47]}, {"time": 0.6667, "value": -50.47, "curve": [0.743, -50.47, 0.773, -45.31]}, {"time": 0.8667, "value": -45.31}], "translate": [{"x": -31.68, "y": -25, "curve": [0.042, -31.68, 0.125, -10.52, 0.042, -25, 0.125, -6.79]}, {"time": 0.1667, "x": -10.52, "y": -6.79, "curve": [0.258, -10.52, 0.324, -1.91, 0.258, -6.79, 0.324, 0.62]}, {"time": 0.4, "x": -1.91, "y": 0.62, "curve": [0.502, -1.91, 0.542, 1.83, 0.502, 0.62, 0.542, -7.01]}, {"time": 0.6667, "x": 1.83, "y": -7.01, "curve": "stepped"}, {"time": 0.8667, "x": 1.83, "y": -7.01}]}, "L_hand_5": {"rotate": [{"value": -62.17, "curve": [0.056, -67.52, 0.111, -78.22]}, {"time": 0.1667, "value": -78.22, "curve": [0.233, -78.22, 0.3, -15.47]}, {"time": 0.3667, "value": -15.47, "curve": [0.444, -15.47, 0.522, -73.18]}, {"time": 0.6, "value": -78, "curve": [0.689, -83.5, 0.778, -80.29]}, {"time": 0.8667, "value": -82.13}], "translate": [{"x": -0.79, "y": -11.59, "curve": [0.056, 0.54, 0.111, 1.61, 0.056, -7.37, 0.111, -0.21]}, {"time": 0.1667, "x": 3.2, "y": 1.07, "curve": [0.233, 5.11, 0.3, 7.68, 0.233, 2.6, 0.3, 2.6]}, {"time": 0.3667, "x": 9.72, "y": 2.6, "curve": [0.444, 12.1, 0.522, 15.65, 0.444, 2.6, 0.522, 2.6]}, {"time": 0.6, "x": 20.02, "y": -2.44, "curve": [0.689, 25.01, 0.778, 26.24, 0.689, -8.19, 0.778, -17.72]}, {"time": 0.8667, "x": 34.06, "y": -29.95}]}, "R_hand_1": {"rotate": [{"value": 7.97, "curve": [0.064, 7.97, 0.089, -42.29]}, {"time": 0.1667, "value": -42.29, "curve": [0.281, -42.29, 0.327, 53.9]}, {"time": 0.4667, "value": 53.9, "curve": [0.568, 53.9, 0.609, 3.5]}, {"time": 0.7333, "value": 3.5}], "translate": [{"x": 3.53, "y": 1.05, "curve": [0.064, 3.53, 0.089, 3.05, 0.064, 1.05, 0.089, 21.48]}, {"time": 0.1667, "x": 3.05, "y": 21.48, "curve": [0.281, 3.05, 0.327, 39.9, 0.281, 21.48, 0.327, -17.54]}, {"time": 0.4667, "x": 39.9, "y": -17.54, "curve": "stepped"}, {"time": 0.7333, "x": 2.03, "y": 11.13}]}, "R_hand_3": {"rotate": [{"value": -2.05, "curve": [0.042, -2.05, 0.125, -19.03]}, {"time": 0.1667, "value": -19.03, "curve": [0.258, -19.03, 0.324, -7.22]}, {"time": 0.4, "value": -7.22, "curve": [0.502, -7.22, 0.542, -36.4]}, {"time": 0.6667, "value": -36.4, "curve": [0.743, -36.4, 0.773, -31.36]}, {"time": 0.8667, "value": -31.36}], "translate": [{"x": -5.01, "y": 2.53, "curve": [0.042, -5.01, 0.125, -1.45, 0.042, 2.53, 0.125, 0.73]}, {"time": 0.1667, "x": -1.45, "y": 0.73, "curve": [0.258, -1.45, 0.324, 0, 0.258, 0.73, 0.324, 0]}, {"time": 0.4, "curve": [0.502, 0, 0.542, -5.01, 0.502, 0, 0.542, 2.53]}, {"time": 0.6667, "x": -5.01, "y": 2.53, "curve": "stepped"}, {"time": 0.8667, "x": -5.01, "y": 2.53}]}, "R_leg2": {"rotate": [{"value": 5.75, "curve": [0.016, 5.75, 0.038, -22.56]}, {"time": 0.1333, "value": -22.56, "curve": [0.24, -22.56, 0.318, -2.94]}, {"time": 0.4, "value": -2.94, "curve": [0.433, -2.94, 0.506, -7.85]}, {"time": 0.6, "value": -7.85, "curve": [0.759, -7.85, 0.816, 5.75]}, {"time": 0.8333, "value": 5.75}], "translate": [{"x": -0.6, "y": 0.6, "curve": [0.016, -0.6, 0.038, -14.79, 0.016, 0.6, 0.038, 15.94]}, {"time": 0.1333, "x": -14.79, "y": 15.94, "curve": [0.24, -14.79, 0.318, 17.28, 0.24, 15.94, 0.318, 15.98]}, {"time": 0.4, "x": 17.28, "y": 15.98, "curve": [0.433, 17.28, 0.506, -0.6, 0.433, 15.98, 0.506, 0.6]}, {"time": 0.6, "x": -0.6, "y": 0.6, "curve": "stepped"}, {"time": 0.8333, "x": -0.6, "y": 0.6}]}, "R_brow": {"rotate": [{"curve": [0.078, 9.49, 0.156, 28.46]}, {"time": 0.2333, "value": 28.46, "curve": [0.344, 28.46, 0.456, 10.18]}, {"time": 0.5667, "value": 4.25, "curve": [0.633, 0.69, 0.7, 1.42]}, {"time": 0.7667}], "translate": [{"x": 7.25, "y": -0.95, "curve": [0.022, 39.25, 0.044, 103.25, 0.022, -5.31, 0.044, -11.03]}, {"time": 0.0667, "x": 103.25, "y": -14.04, "curve": [0.1, 103.25, 0.133, 33.62, 0.1, -18.57, 0.133, -23.58]}, {"time": 0.1667, "x": 7.56, "y": -23.58, "curve": [0.189, -9.81, 0.211, -27.05, 0.189, -23.58, 0.211, 9.61]}, {"time": 0.2333, "x": -27.05, "y": 16.23, "curve": [0.3, -27.05, 0.367, 23.87, 0.3, 36.08, 0.367, 55.82]}, {"time": 0.4333, "x": 40.35, "y": 55.82, "curve": [0.478, 51.33, 0.522, 55.31, 0.478, 55.82, 0.522, 31.84]}, {"time": 0.5667, "x": 55.31, "y": 24.27, "curve": [0.633, 55.31, 0.7, 23.27, 0.633, 12.92, 0.7, 7.46]}, {"time": 0.7667, "x": 7.25, "y": -0.95}]}, "L_brow": {"rotate": [{"curve": [0.078, -10.07, 0.156, -30.22]}, {"time": 0.2333, "value": -30.22, "curve": [0.344, -30.22, 0.456, -3.7]}, {"time": 0.5667, "value": -1.39, "curve": [0.633, 0, 0.7, -0.46]}, {"time": 0.7667}], "translate": [{"x": 7.25, "y": -0.95, "curve": [0.022, 39.37, 0.044, 103.61, 0.022, -9.74, 0.044, -15.75]}, {"time": 0.0667, "x": 103.61, "y": -27.32, "curve": [0.1, 103.61, 0.133, 34.36, 0.1, -44.69, 0.133, -87.76]}, {"time": 0.1667, "x": 4.74, "y": -87.76, "curve": [0.189, -15, 0.211, -44.46, 0.189, -87.76, 0.211, -74.23]}, {"time": 0.2333, "x": -44.46, "y": -61.47, "curve": [0.267, -44.46, 0.3, -14.14, 0.267, -42.34, 0.3, -12.53]}, {"time": 0.3333, "x": 2.29, "y": 7.89, "curve": [0.356, 13.25, 0.422, 46.29, 0.356, 21.51, 0.422, 31.18]}, {"time": 0.4333, "x": 48.33, "y": 28.42, "curve": [0.478, 56.48, 0.522, 68.28, 0.478, 17.37, 0.522, -0.6]}, {"time": 0.5667, "x": 68.28, "y": -0.74, "curve": [0.633, 68.28, 0.7, 27.59, 0.633, -0.95, 0.7, -0.88]}, {"time": 0.7667, "x": 7.25, "y": -0.95}]}, "R_arm_1": {"rotate": [{"value": 6.14, "curve": [0.108, 6.14, 0.325, 129.84]}, {"time": 0.4333, "value": 129.84, "curve": [0.483, 129.84, 0.583, 135.8]}, {"time": 0.6333, "value": 135.8, "curve": [0.683, 135.8, 0.783, 124.12]}, {"time": 0.8333, "value": 124.12}], "translate": [{"curve": [0.108, 0, 0.325, -34.03, 0.108, 0, 0.325, 23.46]}, {"time": 0.4333, "x": -34.03, "y": 23.46, "curve": [0.483, -34.03, 0.583, -57.36, 0.483, 23.46, 0.583, 40.33]}, {"time": 0.6333, "x": -57.36, "y": 40.33, "curve": [0.683, -57.36, 0.783, -34.03, 0.683, 40.33, 0.783, 23.46]}, {"time": 0.8333, "x": -34.03, "y": 23.46}]}, "R_leg": {"rotate": [{"value": -3.22, "curve": [0.01, -3.22, 0.038, 7.6]}, {"time": 0.1333, "value": 7.6, "curve": [0.24, 7.6, 0.318, 4.15]}, {"time": 0.4, "value": 4.15, "curve": [0.433, 4.15, 0.506, 12.94]}, {"time": 0.6, "value": 12.94, "curve": [0.759, 12.94, 0.816, -3.22]}, {"time": 0.8333, "value": -3.22}], "translate": [{"x": 4.77, "y": -0.6, "curve": [0.01, 4.77, 0.038, 39.31, 0.01, -0.6, 0.038, 6.71]}, {"time": 0.1333, "x": 39.31, "y": 6.71, "curve": [0.24, 39.31, 0.318, 5.23, 0.24, 6.71, 0.318, 19.91]}, {"time": 0.4, "x": 5.23, "y": 19.91, "curve": [0.433, 5.23, 0.506, 4.77, 0.433, 19.91, 0.506, -0.6]}, {"time": 0.6, "x": 4.77, "y": -0.6, "curve": "stepped"}, {"time": 0.8333, "x": 4.77, "y": -0.6}]}, "R_hand_2": {"rotate": [{"value": -7.76, "curve": [0.056, -31.15, 0.111, -77.94]}, {"time": 0.1667, "value": -77.94, "curve": [0.233, -77.94, 0.3, -16.52]}, {"time": 0.3667, "value": -16.52, "curve": [0.4, -16.52, 0.433, -16.52]}, {"time": 0.4667, "value": -19.73, "curve": [0.511, -24.02, 0.556, -60.11]}, {"time": 0.6, "value": -60.11, "curve": [0.667, -60.11, 0.733, -48.76]}, {"time": 0.8, "value": -43.09}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3667, "curve": [0.4, 0, 0.433, 29.51, 0.4, 0, 0.433, 5.34]}, {"time": 0.4667, "x": 29.51, "y": 5.34, "curve": [0.511, 29.51, 0.556, 0, 0.511, 5.34, 0.556, 0]}, {"time": 0.6, "curve": "stepped"}, {"time": 0.8}]}, "main_C": {"rotate": [{"curve": "stepped"}, {"time": 0.8667}], "translate": [{"curve": "stepped"}, {"time": 0.8667}]}, "R_finger_3": {"rotate": [{"value": -0.11, "curve": [0.117, -0.11, 0.35, 15.81]}, {"time": 0.4667, "value": 15.81, "curve": [0.5, 15.81, 0.567, -20.59]}, {"time": 0.6, "value": -20.59, "curve": [0.642, -20.59, 0.725, 0]}, {"time": 0.7667}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.7667}]}, "R_finger_2": {"rotate": [{"value": -0.11, "curve": [0.117, -0.11, 0.35, 15.81]}, {"time": 0.4667, "value": 15.81, "curve": [0.5, 15.81, 0.567, -20.59]}, {"time": 0.6, "value": -20.59, "curve": [0.642, -20.59, 0.725, 0]}, {"time": 0.7667}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.7667}]}, "R_finger_1": {"rotate": [{"value": -0.11, "curve": [0.117, -0.11, 0.35, 15.81]}, {"time": 0.4667, "value": 15.81, "curve": [0.5, 15.81, 0.567, -20.59]}, {"time": 0.6, "value": -20.59, "curve": [0.642, -20.59, 0.725, 0]}, {"time": 0.7667}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.7667}]}, "L_fingeL_6": {"rotate": [{"value": -0.11, "curve": [0.058, -0.11, 0.175, 0]}, {"time": 0.2333, "curve": [0.293, 0, 0.378, 7.44]}, {"time": 0.4667, "value": 15.81, "curve": [0.5, 15.81, 0.567, -20.59]}, {"time": 0.6, "value": -20.59, "curve": [0.642, -20.59, 0.725, 0]}, {"time": 0.7667}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.7667}]}, "L_fingeL_5": {"rotate": [{"value": -0.11, "curve": [0.117, -0.11, 0.35, 15.81]}, {"time": 0.4667, "value": 15.81, "curve": [0.5, 15.81, 0.567, -20.59]}, {"time": 0.6, "value": -20.59, "curve": [0.642, -20.59, 0.725, 0]}, {"time": 0.7667}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.7667}]}, "L_fingeL_4": {"rotate": [{"value": -0.11, "curve": [0.117, -0.11, 0.35, 15.81]}, {"time": 0.4667, "value": 15.81, "curve": [0.5, 15.81, 0.567, -20.59]}, {"time": 0.6, "value": -20.59, "curve": [0.642, -20.59, 0.725, 0]}, {"time": 0.7667}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.7667}]}, "mouth": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.8667}]}, "R_eye": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.5333}], "translate": [{"x": -1.96, "y": 0.41, "curve": [0.011, -10.08, 0.022, -24.02, 0.011, 4.77, 0.022, 13.5]}, {"time": 0.0333, "x": -26.32, "y": 13.5, "curve": [0.1, -40.13, 0.167, -50.3, 0.1, 13.5, 0.167, 5.87]}, {"time": 0.2333, "x": -50.3, "y": -0.23, "curve": [0.289, -50.3, 0.344, -39.73, 0.289, -5.32, 0.344, -20.07]}, {"time": 0.4, "x": -28, "y": -20.07, "curve": [0.411, -25.65, 0.422, -8.08, 0.411, -20.07, 0.422, -18.68]}, {"time": 0.4333, "x": -8.08, "y": -17.45, "curve": [0.444, -8.08, 0.456, -10.15, 0.444, -16.23, 0.456, -14.24]}, {"time": 0.4667, "x": -10.89, "y": -12.72, "curve": [0.489, -12.37, 0.511, -14.75, 0.489, -9.7, 0.511, -6.01]}, {"time": 0.5333, "x": -14.75, "y": -3.82, "curve": [0.578, -14.75, 0.622, -14.75, 0.578, 0.55, 0.622, 6.06]}, {"time": 0.6667, "x": -12.93, "y": 6.93, "curve": [0.733, -10.21, 0.8, -3.85, 0.733, 8.24, 0.8, 7.8]}, {"time": 0.8667, "x": 0.7, "y": 8.24}]}, "L_eye": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.5333}], "translate": [{"x": -1.96, "y": 0.41, "curve": [0.011, -10.08, 0.022, -24.02, 0.011, 4.77, 0.022, 13.5]}, {"time": 0.0333, "x": -26.32, "y": 13.5, "curve": [0.1, -40.13, 0.167, -50.3, 0.1, 13.5, 0.167, 5.87]}, {"time": 0.2333, "x": -50.3, "y": -0.23, "curve": [0.289, -50.3, 0.344, -39.73, 0.289, -5.32, 0.344, -20.07]}, {"time": 0.4, "x": -28, "y": -20.07, "curve": [0.411, -25.65, 0.422, -8.08, 0.411, -20.07, 0.422, -18.68]}, {"time": 0.4333, "x": -8.08, "y": -17.45, "curve": [0.444, -8.08, 0.456, -10.15, 0.444, -16.23, 0.456, -14.24]}, {"time": 0.4667, "x": -10.89, "y": -12.72, "curve": [0.489, -12.37, 0.511, -14.75, 0.489, -9.7, 0.511, -6.01]}, {"time": 0.5333, "x": -14.75, "y": -3.82, "curve": [0.578, -14.75, 0.622, -14.75, 0.578, 0.55, 0.622, 6.06]}, {"time": 0.6667, "x": -12.93, "y": 6.93, "curve": [0.733, -10.21, 0.8, -3.85, 0.733, 8.24, 0.8, 7.8]}, {"time": 0.8667, "x": 0.7, "y": 8.24}]}, "face_h": {"rotate": [{}], "translate": [{"curve": [0.028, -0.24, 0.094, -0.76, 0.028, -22.28, 0.094, -70.07]}, {"time": 0.1667, "x": -0.76, "y": -70.07, "curve": [0.225, -0.76, 0.342, -0.31, 0.225, -70.07, 0.342, 70.08]}, {"time": 0.4, "x": -0.31, "y": 70.08, "curve": [0.458, -0.31, 0.575, 0, 0.458, 70.08, 0.575, 0]}, {"time": 0.6333}]}, "face_v": {"rotate": [{}], "translate": [{"x": 32.5, "curve": [0.011, 62.21, 0.037, 125.93, 0.011, -0.32, 0.037, -1.01]}, {"time": 0.0667, "x": 125.93, "y": -1.01, "curve": [0.108, 125.93, 0.192, -4.21, 0.108, -1.01, 0.192, -1.58]}, {"time": 0.2333, "x": -4.21, "y": -1.58, "curve": [0.412, -4.21, 0.483, 72.52, 0.412, -1.58, 0.483, -1.27]}, {"time": 0.5667, "x": 72.52, "y": -1.27, "curve": [0.633, 72.52, 0.767, 32.5, 0.633, -1.27, 0.767, 0]}, {"time": 0.8333, "x": 32.5}]}, "HEAD": {"rotate": [{"value": 3.59, "curve": [0.01, 3.59, 0.073, -5.82]}, {"time": 0.1667, "value": -5.82, "curve": [0.297, -5.82, 0.379, -0.53]}, {"time": 0.5, "value": -0.53, "curve": [0.61, -0.53, 0.704, -1.35]}, {"time": 0.8, "value": -1.35}], "translate": [{"curve": [0.01, 0, 0.073, -33.41, 0.01, 0, 0.073, -0.15]}, {"time": 0.1667, "x": -33.41, "y": -0.15, "curve": [0.297, -33.41, 0.379, 12.13, 0.297, -0.15, 0.379, -0.38]}, {"time": 0.5, "x": 12.13, "y": -0.38, "curve": [0.61, 12.13, 0.704, -24.48, 0.61, -0.38, 0.704, 0.06]}, {"time": 0.8, "x": -24.48, "y": 0.06}]}, "CHEST": {"rotate": [{"value": 1.95, "curve": [0.01, 1.95, 0.073, -0.73]}, {"time": 0.1667, "value": -0.73, "curve": [0.271, -0.73, 0.337, -0.86]}, {"time": 0.4333, "value": -0.86, "curve": [0.544, -0.86, 0.637, -2.99]}, {"time": 0.7333, "value": -2.99}], "translate": [{"curve": [0.01, 0, 0.073, -68.01, 0.01, 0, 0.073, -1.96]}, {"time": 0.1667, "x": -68.01, "y": -1.96, "curve": [0.271, -68.01, 0.337, 56.35, 0.271, -1.96, 0.337, -6.12]}, {"time": 0.4333, "x": 56.35, "y": -6.12, "curve": [0.544, 56.35, 0.637, -12.19, 0.544, -6.12, 0.637, 1.15]}, {"time": 0.7333, "x": -12.19, "y": 1.15}]}, "SPINE": {"rotate": [{"curve": [0.01, 0, 0.073, -1.44]}, {"time": 0.1667, "value": -1.44, "curve": [0.245, -1.44, 0.294, -4.12]}, {"time": 0.3667, "value": -4.12, "curve": [0.477, -4.12, 0.571, -4.94]}, {"time": 0.6667, "value": -4.94}], "translate": [{"curve": [0.01, 0, 0.073, -16.29, 0.01, 0, 0.073, 0]}, {"time": 0.1667, "x": -16.29, "curve": [0.245, -16.29, 0.294, 0, 0.245, 0, 0.294, 0]}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}]}, "R_foot_finger_5": {"rotate": [{"value": 9.43, "curve": [0.023, 9.43, 0.029, -14.74]}, {"time": 0.1, "value": -14.74, "curve": [0.175, -14.74, 0.25, 21.44]}, {"time": 0.3, "value": 21.44, "curve": [0.35, 21.44, 0.45, 5.82]}, {"time": 0.5, "value": 5.82, "curve": [0.542, 5.82, 0.625, 9.43]}, {"time": 0.6667, "value": 9.43}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.6667}]}, "R_foot2": {"rotate": [{"value": -2.29, "curve": [0.03, -2.29, 0.038, 6.26]}, {"time": 0.1333, "value": 6.26, "curve": [0.194, 6.26, 0.209, -38.02]}, {"time": 0.4, "value": -38.02, "curve": [0.502, -38.02, 0.557, -2.29]}, {"time": 0.6667, "value": -2.29, "curve": "stepped"}, {"time": 0.8667, "value": -2.29}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8667}]}, "R_foot_finger_2": {"rotate": [{"value": 10.54, "curve": [0.023, 10.54, 0.029, -28.65]}, {"time": 0.1, "value": -28.65, "curve": [0.175, -28.65, 0.25, 21.16]}, {"time": 0.3, "value": 21.16, "curve": [0.35, 21.16, 0.45, 3.59]}, {"time": 0.5, "value": 3.59, "curve": [0.542, 3.59, 0.625, 10.54]}, {"time": 0.6667, "value": 10.54}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.6667}]}, "R_foot": {"rotate": [{"value": 3.59, "curve": [0.03, 3.59, 0.038, 15.56]}, {"time": 0.1333, "value": 15.56, "curve": [0.194, 15.56, 0.209, -28.9]}, {"time": 0.4, "value": -28.9, "curve": [0.502, -28.9, 0.557, 3.59]}, {"time": 0.6667, "value": 3.59, "curve": "stepped"}, {"time": 0.8667, "value": 3.59}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8667}]}, "tail base": {"rotate": [{"value": -14.85}, {"time": 0.3333, "value": 7.88}, {"time": 0.8667, "value": -14.85}], "translate": [{"x": 1.42, "y": 7.8}, {"time": 0.2, "x": -1.03, "y": -6.72}, {"time": 0.8667, "x": 1.42, "y": 7.8}]}}, "transform": {"L_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}], "R_arm": [{"mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}]}, "path": {"L_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0}]}, "R_hand_ik": {"mix": [{"mixRotate": 0, "mixX": 0}]}}, "attachments": {"default": {"head": {"monkey new/head": {"deform": [{"time": 0.0333, "curve": [0.044, 0, 0.058, 1]}, {"time": 0.0667, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": "stepped"}, {"time": 0.4, "offset": 208, "vertices": [-46.31731, -7.26709, -46.31733, -7.2671, -50.21914, -7.26878, -50.2188, -7.26878, -54.84284, -3.72085, -54.84284, -3.72088, -50.61442, -2.78695, -50.61422, -2.78698, -53.06747, 11.72988, -53.06741, 11.72992, -60.05763, 13.04319, -60.05766, 13.04322, -56.50953, 17.59355, -56.50937, 17.59357, -53.04762, 17.17405, -53.04742, 17.17406, -46.61148, 19.18452, -46.61099, 19.18455, -36.78981, 16.96549, -36.78965, 16.96551, -30.0877, 12.51714, -30.0876, 12.51713, -36.29762, -8.75583, -36.29745, -8.7558, -40.95174, -9.52553, -40.95159, -9.52552, -25.69596, -9.18568, -25.69604, -9.18564, -27.48748, -10.15274, -27.4875, -10.15273, -11.14104, -8.78313, -11.14072, -8.7831, -11.05099, -8.0033, -11.05081, -8.00331, 6.89086, -10.33507, 6.89067, -10.33503, 9.63716, -10.83033, 9.63737, -10.83034, 34.37872, -15.03059, 34.37906, -15.03055, 34.81794, -16.34447, 34.81796, -16.34441, 54.08563, -11.91356, 54.08581, -11.91355, 53.38754, -11.33307, 53.38778, -11.33301, 61.56189, -6.84968, 61.562, -6.84969, 64.21577, -7.13366, 64.21584, -7.13368, 57.92329, 0.87108, 57.92357, 0.87109, 59.09755, -0.15404, 59.09742, -0.15405, 39.45497, 5.78334, 39.45515, 5.78337, 39.89874, 5.69842, 39.89913, 5.69842, 12.38598, 9.65203, 12.38626, 9.65201, 8.66566, 9.12228, 8.66587, 9.12228, -11.9415, 10.33869, -11.94141, 10.3387, -12.75207, 9.60578, -12.75209, 9.60578, -43.5979, 18.29213, -43.59768, 18.29216, -31.6831, 12.85789, -31.68298, 12.85791, 2.83995, 1.69003, 2.84011, 1.69002, -0.667, 3.75062, -0.66683, 3.7506, -15.76434, -2.63279, -15.76413, -2.63279, -20.75528, -2.83704, -20.75487, -2.83705, -30.17004, -3.96857, -30.16949, -3.96863, -39.24649, -3.47177, -39.24607, -3.47178, -47.58857, -0.42949, -47.58801, -0.42952, -51.15817, 2.70295, -51.15758, 2.7029, -53.09289, 7.88166, -53.09252, 7.88163, -52.45679, 11.58937, -52.45663, 11.58936, -50.69321, 15.03995, -50.69284, 15.03991, -45.38278, 14.54405, -45.38237, 14.54401, -38.68629, 15.1817, -38.68611, 15.18168, -33.83895, 10.34922, -33.83865, 10.34921, -25.17306, 9.53549, -25.17274, 9.53542, -14.08041, 6.61527, -14.08017, 6.61523, -10.08198, 7.83799, -10.08159, 7.83798, -18.58867, 12.47459, -18.58854, 12.47456, -31.59329, 16.12888, -31.59288, 16.12887, -45.17102, 17.39317, -45.17063, 17.39319, -50.13291, 18.87589, -50.13274, 18.87594, -53.76559, 5.53073, -53.76568, 5.53071, -61.60112, 5.52594, -61.60088, 5.52593, -35.8344, 16.37127, -35.83405, 16.37128, 0.72028, 4.34412, 0.72038, 4.34409, 2.37323, 6.18657, 2.37334, 6.18653, 14.33289, 3.47917, 14.33297, 3.47914, 15.46755, 5.27371, 15.46787, 5.27369, 26.50284, 3.56193, 26.50291, 3.56194, 26.95079, 3.68139, 26.95111, 3.68137, 36.88379, 2.40436, 36.88412, 2.40433, 37.14984, 1.38327, 37.15002, 1.38327, 46.42889, -1.61983, 46.42907, -1.61987, 49.11413, -1.34468, 49.1145, -1.34473, 50.48672, -2.3524, 50.48705, -2.35241, 53.34974, -1.75658, 53.35003, -1.75663, 52.20497, -3.55934, 52.20517, -3.55935, 51.98098, -4.21307, 51.9813, -4.21311, 45.52065, -2.34072, 45.52116, -2.34079, 45.22848, -6.24993, 45.2287, -6.24994, 30.77176, -2.90666, 30.77233, -2.90666, 30.18584, -9.73698, 30.1861, -9.73697, 11.25042, -7.04061, 11.2506, -7.0406, 10.49023, -9.01541, 10.4904, -9.01539, -5.41855, -3.64883, -5.41833, -3.64883, -5.45666, -6.70423, -5.45633, -6.70422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -57.37243, 7.50992, -57.37207, 7.5099, -51.65936, 1.39343, -51.65913, 1.39339, -45.49649, -2.41189, -45.49624, -2.4119, -39.79971, -4.32446, -39.79926, -4.32447, -26.67602, -5.43132, -26.67586, -5.43131, -57.35662, 12.65292, -57.35624, 12.6529, -52.60176, 19.0652, -52.60146, 19.06521, -48.71664, 20.389, -48.71605, 20.38899, -40.64072, 17.86741, -40.64056, 17.86742], "curve": [0.403, 0, 0.422, 1]}, {"time": 0.4333}]}}, "monkey_mouth o": {"monkey new/monkey_mouth o": {"deform": [{"time": 0.0667, "vertices": [14.7974, 1.08812, 14.79752, 1.08807, -18.01316, 9.22122, -18.01312, 9.22119, -22.72029, 2.00404, -22.72026, 2.00402, 19.85403, 5.88119, 19.85405, 5.88116, 33.11479, 6.23803, 33.11488, 6.23799, 29.31389, -13.77968, 29.31395, -13.7797, 8.68729, -5.34565, 8.68732, -5.34567, -28.96066, 1.82638, -28.96056, 1.82635, -15.58267, 3.05798, -15.58219, 3.05781, -53.54457, 12.00336, -53.54446, 12.00332, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12.49281, 53.88238, 12.49302, 53.88223, 3.22397, 28.78657, 3.224, 28.7865, 8.47284, 11.8279, 8.47298, 11.82782, 12.51833, 11.30453, 12.51844, 11.30445, -1.24611, -3.18559, -1.24601, -3.18564, -8.76258, -26.86399, -8.76248, -26.86406, -19.61997, 5.81088, -19.61992, 5.81085, -31.78688, 16.90059, -31.78672, 16.90051, -21.99565, 12.55338, -21.99554, 12.55333, -23.23563, 23.50811, -23.23547, 23.508, -3.85656, 35.93695, -3.85618, 35.93672, 7.10837, 27.50946, 7.10843, 27.50938, 8.5532, 16.17832, 8.55324, 16.17825, 7.55656, 8.27188, 7.55663, 8.27183, 0, 0, 0, 0, -11.16428, -22.60649, -11.16415, -22.60656, -20.80742, -9.57076, -20.80733, -9.57086, -30.1149, 8.70955, -30.11475, 8.70949], "curve": [0.1, 0, 0.133, 1]}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4667, "curve": [0.495, 0, 0.505, 1]}, {"time": 0.5333, "vertices": [14.7974, 1.08812, 14.79752, 1.08807, -18.01316, 9.22122, -18.01312, 9.22119, -22.72029, 2.00404, -22.72026, 2.00402, 19.85403, 5.88119, 19.85405, 5.88116, 33.11479, 6.23803, 33.11488, 6.23799, 29.31389, -13.77968, 29.31395, -13.7797, 8.68729, -5.34565, 8.68732, -5.34567, -28.96066, 1.82638, -28.96056, 1.82635, -15.58267, 3.05798, -15.58219, 3.05781, -53.54457, 12.00336, -53.54446, 12.00332, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12.49281, 53.88238, 12.49302, 53.88223, 3.22397, 28.78657, 3.224, 28.7865, 8.47284, 11.8279, 8.47298, 11.82782, 12.51833, 11.30453, 12.51844, 11.30445, -1.24611, -3.18559, -1.24601, -3.18564, -8.76258, -26.86399, -8.76248, -26.86406, -19.61997, 5.81088, -19.61992, 5.81085, -31.78688, 16.90059, -31.78672, 16.90051, -21.99565, 12.55338, -21.99554, 12.55333, -23.23563, 23.50811, -23.23547, 23.508, -3.85656, 35.93695, -3.85618, 35.93672, 7.10837, 27.50946, 7.10843, 27.50938, 8.5532, 16.17832, 8.55324, 16.17825, 7.55656, 8.27188, 7.55663, 8.27183, 0, 0, 0, 0, -11.16428, -22.60649, -11.16415, -22.60656, -20.80742, -9.57076, -20.80733, -9.57086, -30.1149, 8.70955, -30.11475, 8.70949]}]}}, "monkey_mouth smile closed": {"monkey new/monkey_mouth smile closed": {"deform": [{"curve": [0.022, 0, 0.044, 1]}, {"time": 0.0667, "vertices": [0.83086, -18.27302, 0.83088, -18.27305, 6.95934, -1.80003, 6.95923, -1.80003, -6.85576, 14.45557, -6.85583, 14.45553, -21.98729, 23.42041, -21.98737, 23.42042, -21.10371, 33.5233, -21.1037, 33.52329, -11.66862, 31.69967, -11.66865, 31.69966, 2.5776, 29.75341, 2.5776, 29.75344, 8.2017, 23.58192, 8.2017, 23.58192, 3.05927, 8.79619, 3.05927, 8.79619, -8.99277, 1.57665, -8.99277, 1.57666, 3.0862, 0.62369, 3.08617, 0.62368, -5.71101, -22.32387, -5.711, -22.32387, -0.13023, -24.51177, -0.13017, -24.51174, 4.22955, -46.16306, 4.22957, -46.16306, -29.12717, -44.23299, -29.12727, -44.23302, -19.82984, -24.38549, -19.82987, -24.3855, -30.72428, -28.00387, -30.72438, -28.00389, -29.46701, -39.93455, -29.46713, -39.93454, -29.99406, -25.31461, -29.99413, -25.31458, -24.53059, -24.55933, -24.53058, -24.55938, -12.25806, -17.6583, -12.25802, -17.65832, -6.23574, -4.21197, -6.23576, -4.21198, -6.58427, 10.26807, -6.58439, 10.26806, -11.07808, 20.25409, -11.07816, 20.2541, -23.91415, 26.02877, -23.91422, 26.02876, -25.76102, 35.2755, -25.76105, 35.27548, -18.91521, 33.14891, -18.91528, 33.14893, -13.36588, 24.57429, -13.36597, 24.57428, -6.11757, 11.40872, -6.11763, 11.40871, -3.32169, -3.27296, -3.32163, -3.27301, -7.7449, -21.13221, -7.74503, -21.13223, -21.78499, -33.02137, -21.78509, -33.02135], "curve": "stepped"}, {"time": 0.5333, "vertices": [0.83086, -18.27302, 0.83088, -18.27305, 6.95934, -1.80003, 6.95923, -1.80003, -6.85576, 14.45557, -6.85583, 14.45553, -21.98729, 23.42041, -21.98737, 23.42042, -21.10371, 33.5233, -21.1037, 33.52329, -11.66862, 31.69967, -11.66865, 31.69966, 2.5776, 29.75341, 2.5776, 29.75344, 8.2017, 23.58192, 8.2017, 23.58192, 3.05927, 8.79619, 3.05927, 8.79619, -8.99277, 1.57665, -8.99277, 1.57666, 3.0862, 0.62369, 3.08617, 0.62368, -5.71101, -22.32387, -5.711, -22.32387, -0.13023, -24.51177, -0.13017, -24.51174, 4.22955, -46.16306, 4.22957, -46.16306, -29.12717, -44.23299, -29.12727, -44.23302, -19.82984, -24.38549, -19.82987, -24.3855, -30.72428, -28.00387, -30.72438, -28.00389, -29.46701, -39.93455, -29.46713, -39.93454, -29.99406, -25.31461, -29.99413, -25.31458, -24.53059, -24.55933, -24.53058, -24.55938, -12.25806, -17.6583, -12.25802, -17.65832, -6.23574, -4.21197, -6.23576, -4.21198, -6.58427, 10.26807, -6.58439, 10.26806, -11.07808, 20.25409, -11.07816, 20.2541, -23.91415, 26.02877, -23.91422, 26.02876, -25.76102, 35.2755, -25.76105, 35.27548, -18.91521, 33.14891, -18.91528, 33.14893, -13.36588, 24.57429, -13.36597, 24.57428, -6.11757, 11.40872, -6.11763, 11.40871, -3.32169, -3.27296, -3.32163, -3.27301, -7.7449, -21.13221, -7.74503, -21.13223, -21.78499, -33.02137, -21.78509, -33.02135], "curve": [0.567, 0, 0.6, 1]}, {"time": 0.6333}]}}}}, "drawOrder": [{}, {"time": 0.0667, "offsets": [{"slot": "monkey_right_arm", "offset": 12}]}, {"time": 0.5333}, {"time": 0.8667}]}}}