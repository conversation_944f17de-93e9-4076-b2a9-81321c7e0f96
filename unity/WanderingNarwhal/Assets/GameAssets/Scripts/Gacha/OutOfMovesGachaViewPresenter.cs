using System;
using System.Collections.Generic;
using Assets.UltimateIsometricToolkit.Scripts.External;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.Gacha;
using BBB.Screens;
using BBB.Wallet;
using BebopBee.Core;
using Bebopbee.Core.Extensions.Unity;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI.Gacha.Views
{
    public class OutOfMovesGachaViewPresenter : ModalsViewPresenter, IOutOfMovesGachaViewPresenter
    {
        private const int PaidPrizeCount = 8;
        // Prize, paid or not
        public event Action<Prize, bool> PrizeToClaimRolled = delegate { };
        public event Action IAPStoreRequested = delegate { };

        [SerializeField] private GachaThemeableView _gachaThemeableView;
        [SerializeField] private GachaThemePreset _paidGachaThemePreset;
        [SerializeField] private GachaThemePreset _freeGachaThemePreset;

        [SerializeField] private GachaWheelPresenter _gachaWheelPresenter;
        [SerializeField] private OddsPanel _oddsPanel;
        [SerializeField] private GenericSpeechBubble _speechBubble;
        [SerializeField] private Button _infoButton;
        [SerializeField] private Button _storeButton;

        [SerializeField] private GameObject _purchaseHolder;
        [SerializeField] private Animator _purchaseHolderAnimator;
        [SerializeField] private GameObject _orHolder;
        [SerializeField] private Animator _orHolderAnimator;
        [SerializeField] private GameObject _secondaryButtonHolder;
        [SerializeField] private Animator _secondaryButtonHolderAnimator;

        [SerializeField] private Button _secondaryButton;
        [SerializeField] private Button _buySpinButton;
        [SerializeField] private UICurrencyInsufficientComponent _buySpinPriceComponent;
        [SerializeField] private float _showCoinsWidgetTime = 3.5f;
        [SerializeField] private GachaPrizeType _gachaPrizeTypesToConsiderWinning;

        private GachaManager _gachaManager;
        private IWalletManager _walletManager;
        private IUIWalletManager _uiWalletManager;

        private ProbWeightedList<Prize> _paidPrizes;
        private ProbWeightedList<Prize> _prizes;
        private List<Prize> _prizesList;
        private List<Prize> _paidPrizesList;
        private IDisposable _rollPriceRefreshTimer;
        private IEventDispatcher _eventDispatcher;
        private VideoAdManager _videoAdManager;
        private CurrencyIconsLoader _currencyIconsLoader;

        private Prize _selectedPrize;
        private string _levelUid;
        private bool _paidSpin;
        private bool _spinningInProcess;
        private static readonly int HideId = Animator.StringToHash("Hide");

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);
            _gachaManager = context.Resolve<GachaManager>();

            _prizes = _gachaManager.GetPrizes(GachaType.OutOfMoves);
            _prizesList = new List<Prize>();
            foreach (var prize in _prizes)
            {
                _prizesList.Add(prize);
            }

            _paidPrizes = _gachaManager.GetPrizes(GachaType.OutOfMoves, PaidPrizeCount, true);
            _paidPrizesList = new List<Prize>();
            foreach (var prize in _paidPrizes)
            {
                _paidPrizesList.Add(prize);
            }

            _walletManager = context.Resolve<IWalletManager>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _videoAdManager = context.Resolve<VideoAdManager>();
            _currencyIconsLoader = context.Resolve<CurrencyIconsLoader>();

            _buySpinButton.ReplaceOnClick(PurchaseSpin);
            _secondaryButton.ReplaceOnClick(SecondaryButtonHandler);

            _infoButton.ReplaceOnClick(_speechBubble.ToggleSpeechBubble);
            _storeButton.ReplaceOnClick(() => { IAPStoreRequested?.Invoke(); });

            _gachaWheelPresenter.Init(context);
        }

        protected override void OnShow()
        {
            base.OnShow();
            CloseButton.gameObject.SetActive(true);
            
            // force disabling to avoid complex state-based logic on disabling via animator or by gameobject
            _purchaseHolder.SetActive(false);
            _secondaryButtonHolder.SetActive(false);
            _orHolder.SetActive(false);
            
            _spinningInProcess = false;
            Subscribe();
            RefreshWheel();
            RefreshPaidState();
        }

        protected override void OnHide()
        {
            base.OnHide();
            Unsubscribe();
            _speechBubble.HideSpeechBubble();
            _gachaWheelPresenter.Hide();
        }

        private void RefreshPaidState()
        {
            var time = _gachaManager.GetTimeLeftUntilFreeRoll(GachaType.OutOfMoves).ToLong();
            // by design when we allow to purchase we also should show paid odds
            var shouldShowPaidState = _paidSpin || time > 0;

            _gachaThemeableView.Apply(shouldShowPaidState ? _paidGachaThemePreset : _freeGachaThemePreset);

            _oddsPanel.Setup(shouldShowPaidState ? _paidPrizes : _prizes);
            _gachaWheelPresenter.ProvideListOfPrizes(shouldShowPaidState ? _paidPrizesList : _prizesList, 8);
        }

        private void Subscribe()
        {
            Unsubscribe();
            _gachaWheelPresenter.TriedToStopSpin += SelectPrize;
            _gachaWheelPresenter.WheelStopped += ClaimPrize;
        }

        private void Unsubscribe()
        {
            if (_gachaWheelPresenter != null)
            {
                _gachaWheelPresenter.TriedToStopSpin -= SelectPrize;
                _gachaWheelPresenter.WheelStopped -= ClaimPrize;
            }

            DisposeRollPriceRefreshTimer();
        }

        private void DisposeRollPriceRefreshTimer()
        {
            if (_rollPriceRefreshTimer != null)
            {
                _rollPriceRefreshTimer.Dispose();
                _rollPriceRefreshTimer = null;
            }
        }

        protected override void OnCloseButton()
        {
            if (!_spinningInProcess)
                base.OnCloseButton();
        }

        public void Setup(string levelUid)
        {
            _levelUid = levelUid;
        }

        private void RefreshWheel(bool delayedWidgetHide = false)
        {
            DisposeRollPriceRefreshTimer();

            var time = _gachaManager.GetTimeLeftUntilFreeRoll(GachaType.OutOfMoves).ToLong();

            if (time <= 0)
            {
                _gachaWheelPresenter.SetAvailableToSpinState();
                RefreshButtons(false);
                if (!delayedWidgetHide)
                    SetVisibleCoinsWidget(false);
            }
            else
            {
                _rollPriceRefreshTimer = Rx.InvokeRepeating(0f, 1f, RefreshGachaTimer);
                RefreshButtons(true);
                SetVisibleCoinsWidget(true);
            }
        }

        private bool ShouldShowSecondaryButton()
        {
            return _videoAdManager.IsAdAvailable(Engagement.Ad.Oom, VideoAdManager.FreeOutOfMovesSpin);
        }

        private void RefreshButtons(bool showButtons)
        {
            if (showButtons)
            {
                _purchaseHolder.SetActive(true);
            }
            else
            {
                DisableViaAnimator(_purchaseHolder, _purchaseHolderAnimator);
            }

            var showSecondaryButton = showButtons && ShouldShowSecondaryButton();
            if (showSecondaryButton)
            {
                _secondaryButtonHolder.SetActive(true);
                _orHolder.SetActive(true);
            }
            else
            {
                DisableViaAnimator(_orHolder, _orHolderAnimator);
                DisableViaAnimator(_secondaryButtonHolder, _secondaryButtonHolderAnimator);
            }

            return;

            void DisableViaAnimator(GameObject gameObject, Animator animator)
            {
                if (gameObject.activeSelf && gameObject.activeInHierarchy)
                {
                    animator.SetTrigger(HideId);
                }
                else
                {
                    gameObject.SetActive(false);
                }
            }
        }

        private void RefreshGachaTimer(long obj)
        {
            var time = _gachaManager.GetTimeLeftUntilFreeRoll(GachaType.OutOfMoves).ToLong();

            if (time <= 0)
            {
                RefreshWheel();
                RefreshPaidState();
            }
            else
            {
                var (currencies, _) = _gachaManager.GetRollPrice(GachaType.OutOfMoves);

                _gachaWheelPresenter.SetCooldownState(time);

                if (currencies.IsNullOrEmpty())
                {
                    BDebug.LogError(LogCat.Gacha, "Price is null, the spin should be free or the price should be configured, check GachaManager.GetRollPrice");
                }
                else
                {
                    foreach (var kvp in currencies)
                    {
                        _currencyIconsLoader.LoadAndGetCurrencySpriteAsync(kvp.Key).ContinueWith(sprite =>
                        {
                            _buySpinPriceComponent.Setup(sprite, kvp.Value.ToInt());
                            _buySpinPriceComponent.SetupInsufficient(_walletManager.Balance.GetBalance(), kvp.Key);
                        });
                        
                        return;
                    }
                }
            }
        }

        private void SelectPrize()
        {
            _speechBubble.HideSpeechBubble();

            var prizeList = _paidSpin && _paidPrizes != null ? _paidPrizes : _prizes;
            _selectedPrize = prizeList.GetRandom();
            var selectedSlot = prizeList.IndexOf(_selectedPrize);

            _gachaWheelPresenter.SetWaitingForTheEndOfTheSpin();
            _gachaWheelPresenter.SpinToSlot(selectedSlot, (_gachaPrizeTypesToConsiderWinning & _selectedPrize.Type) > 0);
            _gachaManager.Roll(GachaType.OutOfMoves);

            _spinningInProcess = true;
            CloseButton.gameObject.SetActive(false);
        }

        private void ClaimPrize()
        {
            RefreshButtons(_selectedPrize.Type != GachaPrizeType.Moves);
            PrizeToClaimRolled(_selectedPrize, _paidSpin);

            _paidSpin = false;

            if (_selectedPrize.Type != GachaPrizeType.Moves)
            {
                SetVisibleCoinsWidget(true);
                _spinningInProcess = false;
                CloseButton.gameObject.SetActive(true);

                RefreshWheel();
            }
        }

        private void PurchaseSpin()
        {
            _speechBubble.HideSpeechBubble();
            var (currencies, price) = _gachaManager.GetRollPrice(GachaType.OutOfMoves);

            if (currencies == null)
            {
                Debug.LogError("[Gacha] Price is null");
                return;
            }

            var transaction = new Transaction()
                .Spend(currencies)
                .SetAnalyticsData(CurrencyFlow.Wheel.Name, CurrencyFlow.Wheel.OOM_Paid, _levelUid)
                .AddTag(TransactionTag.OutOfMoves);

            if (_walletManager.TransactionController.TryToMakeTransaction(transaction))
            {
                SetVisibleCoinsWidget(false, _showCoinsWidgetTime);
                _paidSpin = true;
                _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.OutOfMoves);
                _gachaManager.ResetRoll(GachaType.OutOfMoves);
                RefreshWheel(true);
                var gachaSpinPurchasedEvent = _eventDispatcher.GetMessage<GachaSpinPurchasedEvent>();
                gachaSpinPurchasedEvent.Set(_paidSpin, price);
                _eventDispatcher.TriggerEvent(gachaSpinPurchasedEvent);
                RefreshPaidState();
            }
            else
            {
                InvokeIAPStoreRequested();
            }
        }

        private void InvokeIAPStoreRequested()
        {
            IAPStoreRequested?.Invoke();
        }

        private void SecondaryButtonHandler()
        {
            _videoAdManager.TryToPlayVideoAd(Engagement.Ad.Oom, VideoAdManager.FreeOutOfMovesSpin, () =>
            {
                _speechBubble.HideSpeechBubble();
                _gachaManager.ResetRoll(GachaType.OutOfMoves);
                _paidSpin = false;
                RefreshPaidState();
                RefreshWheel();
            }, null);
        }

        private void SetVisibleCoinsWidget(bool visible, float delay = 0f)
        {
            var changeTemporaryVisibilityEvent = _eventDispatcher.GetMessage<TopBarController.ChangeTemporaryVisibilityEvent>();
            changeTemporaryVisibilityEvent.Widgets = TopBarController.Widgets.Coins;
            changeTemporaryVisibilityEvent.Visible = visible;
            changeTemporaryVisibilityEvent.Delay = delay;
            _eventDispatcher.TriggerEvent(changeTemporaryVisibilityEvent);
        }

        [ContextMenu("ApplyPaid")]
        private void ApplyPaid()
        {
            _gachaThemeableView.Apply(_paidGachaThemePreset);
        }

        [ContextMenu("ApplyFree")]
        private void ApplyFree()
        {
            _gachaThemeableView.Apply(_freeGachaThemePreset);
        }
    }
}