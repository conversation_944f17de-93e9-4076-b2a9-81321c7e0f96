using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.UI.Level.Scripts.Boosts;
using BBB.Wallet;
using BebopBee.Core.UI;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.IAP.Baskets;
using GameAssets.Scripts.SocialScreens.Teams;
using UnityEngine;
using IapManager = BBB.IAP.IapManager;

namespace GameAssets.Scripts.Promotions.Banners
{
    public class BannerManager : IContextInitializable, IContextReleasable
    {
        private const string GachaBannerUid = "out_of_moves_gacha";
        private const string AskForLifeBannerUid = "ask_for_lives";

        private BannersConfig _bannersConfig;
        private IBannerContainer _cachedBannerContainer;

        private ISocialManager _socialManager;
        private IapManager _iapManager;
        private IapPurchaseProcessor _iapPurchaseProcessor;
        private PromotionManager _promotionManager;
        private IEventDispatcher _eventDispatcher;
        private IAPBasketManager _basketManager;
        private IModalsBuilder _modalsBuilder;

        private Banner _askForLivesBanner;
        private Banner _outOfMovesGachaBanner;

        private Action _outOfMovesGachaCallback;
        private Action _purchaseFlowStartedCallback;
        private Action _askForLivesCallback;
        private Action<bool> _purchaseFlowCompletedCallback;

        public void InitializeByContext(IContext context)
        {
            _promotionManager = context.Resolve<PromotionManager>();
            _socialManager = context.Resolve<ISocialManager>();
            _iapManager = context.Resolve<IapManager>();
            _iapPurchaseProcessor = context.Resolve<IapPurchaseProcessor>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _basketManager = context.Resolve<IAPBasketManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();

            var genericResourceProvider = context.Resolve<GenericResourceProvider>();
            _bannersConfig = genericResourceProvider.GetPreloaded<BannersConfig>(GenericResKeys.BannersConfig);
            _bannersConfig.Init();

            GenerateBuiltInBanners();
        }

        public void FillContainerWithBanners(IBannerContainer bannerContainer)
        {
            var banners = new List<Banner>();
            _cachedBannerContainer = bannerContainer;
            switch (bannerContainer.Placement)
            {
                case BannerPlacement.OutOfLives:
                {
                    if (ConnectivityStatusManager.ConnectivityReachable)
                    {
                        if (_socialManager.IsSocialUnlocked() && _socialManager.CanAskForLives())
                        {
                            banners.Add(_askForLivesBanner);
                        }
                    }

                    break;
                }
                case BannerPlacement.OutOfMoves:
                    banners.Add(_outOfMovesGachaBanner);
                    break;
            }

            if (ConnectivityStatusManager.ConnectivityReachable)
            {
                foreach (var promotion in _promotionManager.ActivePromotions.Values)
                {
                    if (banners.Count >= bannerContainer.TargetBannersNumber)
                        break;

                    if (!promotion.ShouldShowBanner)
                        continue;

                    var prefab = promotion.GetBannerPrefab();
                    var bannerView = prefab.GetComponent<BannerView>();
                    if (bannerView == null || !bannerView.ShouldBeShown() || (!bannerView.SpecifiedPlacements.IsNullOrEmpty() && !bannerView.SpecifiedPlacements.Contains(bannerContainer.Placement)))
                        continue;

                    promotion.Banner ??= new Banner(() => { _promotionManager.InvokeBannerActions(promotion, bannerContainer.Placement); }, prefab, promotion);
                    banners.Add(promotion.Banner);
                }
            }

            bannerContainer.AddBanners(banners);
        }

        public void SetupOutOfMovesCallbacks(Action outOfMovesGachaCallback, Action showMatch3BoardCallback)
        {
            _outOfMovesGachaCallback = outOfMovesGachaCallback;
        }

        public void SetupAskForLivesCallback(Action askForLivesCallback)
        {
            _askForLivesCallback = askForLivesCallback;
        }

        public void SetupIAPPurchaseCallback(Action purchaseFlowStartedCallback, Action<bool> purchaseFlowCompletedCallback)
        {
            _purchaseFlowStartedCallback = purchaseFlowStartedCallback;
            _purchaseFlowCompletedCallback = purchaseFlowCompletedCallback;
        }

        private void GenerateBuiltInBanners()
        {
            _askForLivesBanner = new Banner(() =>
            {
                _socialManager.ShowSocialModal();
                InvokeAskForLivesCallback();
            }, GetBannerPrefabFromConfig(AskForLifeBannerUid));
            _outOfMovesGachaBanner = new Banner(InvokeOutOfMovesGachaCallback, GetBannerPrefabFromConfig(GachaBannerUid));
        }

        private GameObject GetBannerPrefabFromConfig(string bannerUid)
        {
            if (_bannersConfig.BannerPrefabsByUid.TryGetValue(bannerUid, out var prefab))
                return prefab;

            Debug.LogError($"Couldn't find banner prefab with uid: {bannerUid}");
            return null;
        }

        public void DoBasketPurchase(IAPBasket basket, string storeItemUid, string promotionUid)
        {
            var basketModal = _modalsBuilder.CreateModalView<BasketController>(ModalsType.Basket);

            var productData = _iapManager.GetPurchasableItemByConfigId(basket.MarketItemUid);
            if (productData == null)
            {
                BDebug.LogError(LogCat.Iap, $"Couldn't find product {basket.MarketItemUid} for basket {basket.Uid}");
                return;
            }

            var iapPurchased = false;
            InvokePurchaseFlowStartedCallback();
            basketModal.SetupInfo(basket, productData.LocalizedPriceString, () =>
            {
                iapPurchased = true;
                DoBannerPurchase(storeItemUid, promotionUid, new IapPurchaseParams()
                {
                    PurchaseCompletedCallback = () => { _basketManager.RegisterAsPurchased(basket.Uid); },
                    ShowGenericRewardScreen = false,
                    SkipClaim = true,
                });
                basketModal.Hide();
            }, CloseHandler);

            basketModal.ShowModal(ShowMode.Immediate);
            return;

            void CloseHandler()
            {
                if (!iapPurchased)
                {
                    InvokePurchaseFlowFailedCallback();
                }
            }
        }

        public void DoBannerPurchase(string storeItemUid, string promotionUid, IapPurchaseParams iapPurchaseParams = null)
        {
            iapPurchaseParams ??= new IapPurchaseParams();
            InvokePurchaseFlowStartedCallback();

            PurchasePath.Append(PurchaseStep.PromotionBanner);
            PurchasePath.Append(promotionUid);

            var iapStoreMarketItemConfig = _iapManager.GetIapConfig(storeItemUid);
            _iapPurchaseProcessor.Purchase(iapStoreMarketItemConfig.Uid, new IapPurchaseParams()
            {
                PurchaseSource = PurchaseSource.PromotionBanner,
                PurchaseCompletedCallback = () =>
                {
                    iapPurchaseParams.PurchaseCompletedCallback?.Invoke();
                    _promotionManager.Refresh();

                    if (iapStoreMarketItemConfig.RewardOrdered == null) return;
                    for (var i = 0; i < iapStoreMarketItemConfig.RewardOrdered?.RewardLength; i++)
                    {
                        var booster = iapStoreMarketItemConfig.RewardOrdered.Value.Reward(i);
                        if (booster?.Uid is InventoryBoosters.ShovelBooster or
                            InventoryBoosters.VerticalBooster or
                            InventoryBoosters.HorizontalBooster)
                        {
                            var boosterBoughtEvent = _eventDispatcher.GetMessage<BoosterBoughtEvent>();
                            boosterBoughtEvent.Set(booster.Value.Uid);
                            _eventDispatcher.TriggerEventNextFrame(boosterBoughtEvent);
                        }
                    }
                },
                FlowCompletedCallback = () =>
                {
                    iapPurchaseParams.FlowCompletedCallback?.Invoke();
                    InvokePurchaseFlowCompletedCallback();
                },
                FlowFailedCallback = () =>
                {
                    iapPurchaseParams.FlowFailedCallback?.Invoke();

                    BDebug.LogError(LogCat.Iap, $"{iapStoreMarketItemConfig.Uid} purchase failed");
                    InvokePurchaseFlowFailedCallback();
                },

                ShowGacha = iapPurchaseParams.ShowGacha,
                ShowGenericRewardScreen = iapPurchaseParams.ShowGenericRewardScreen,
                SkipClaim = iapPurchaseParams.SkipClaim,
                TransactionTag = iapPurchaseParams.TransactionTag,
                RewardViewOverride = iapPurchaseParams.RewardViewOverride,
                ShowMode = ShowMode.Immediate,
            });
        }

        private void InvokeOutOfMovesGachaCallback()
        {
            LogBannerClickedAnalytics(GachaBannerUid);
            _outOfMovesGachaCallback?.Invoke();
        }

        private void InvokePurchaseFlowStartedCallback()
        {
            _purchaseFlowStartedCallback?.Invoke();
        }

        private void InvokePurchaseFlowCompletedCallback()
        {
            _purchaseFlowCompletedCallback?.Invoke(true);
        }

        private void InvokePurchaseFlowFailedCallback()
        {
            _purchaseFlowCompletedCallback?.Invoke(false);
        }

        private void InvokeAskForLivesCallback()
        {
            LogBannerClickedAnalytics(AskForLifeBannerUid);
            _askForLivesCallback?.Invoke();
        }

        public void LogBannerClickedAnalytics(string storeUid)
        {
            if (storeUid.IsNullOrEmpty()) return;
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.Banners.CategoryName,
                DauInteractions.TapOnBanner.GetAnalyticsName(_cachedBannerContainer.Placement) +
                DauInteractions.Banners.Clicked, storeUid));
        }

        public void ReleaseByContext(IContext context)
        {
            _outOfMovesGachaCallback = null;
            _purchaseFlowStartedCallback = null;
            _askForLivesCallback = null;
            _purchaseFlowCompletedCallback = null;
        }
    }
}