using System;
using System.Collections.Generic;
using BBB.DI;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Promotions.Components
{
    public class PromotionActionButton : PromotionComponent
    {
        [Serializable]
        public class Action
        {
            public string ActionName;
            public List<ActionParam> ActionParams;
        }

        [Serializable]
        public class ActionParam
        {
            public string Key;
            public string Value;
        }

        [SerializeField] private Button _button;
        [SerializeField] private bool _closeOnInteraction = true;
        [SerializeField] private List<Action> _actions;
        private SimpleActionController _simpleActionController;

        private void OnValidate()
        {
            if (_button == null)
                _button = GetComponent<Button>();
        }

        protected override void InitWithContextInternal(IContext context)
        {
            _button.ReplaceOnClick(ButtonHandler);
            _simpleActionController = context.Resolve<SimpleActionController>();
        }

        private void ButtonHandler()
        {
            if (_closeOnInteraction)
            {
                TriggerCloseCallback();
            }

            HandleButtonActionsAsync().Forget();
        }

        private async UniTaskVoid HandleButtonActionsAsync()
        {
            foreach (var action in _actions)
            {
                var actionParamsDict = new Dictionary<string,string>();
                foreach (var kv in action.ActionParams)
                {
                    actionParamsDict[kv.Key] = kv.Value;
                }

                await _simpleActionController.ExecuteActionAsync(action.ActionName, actionParamsDict);
            }
        }
    }
}