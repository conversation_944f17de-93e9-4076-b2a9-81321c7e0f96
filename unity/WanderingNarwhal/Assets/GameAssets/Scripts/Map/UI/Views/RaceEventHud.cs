using System;
using System.Collections.Generic;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.RaceEvents;
using BBB.RaceEvents.UI;
using BBB.Wallet;
using BebopBee;
using BebopBee.Core;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Core.CustomAnimators;
using GameAssets.Scripts.UI.OverlayDialog;
using HedgehogTeam.EasyTouch;
using Spine.Unity;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI
{
    public class RaceEventHud : GenericHudComponent
    {
        private const string HudIconSpineAnimPrefabName = "HudIconSpineAnim";

        [SerializeField] private string AlmostWonSpeechBubbleTextID;
        [SerializeField] private string SingleWinSpeechBubbleTextID;
        [SerializeField] private Image[] _ribbonImages;
        [SerializeField] private Color _finishedColor;
        [SerializeField] private Color _defaultColor;

        [SerializeField] private TextMeshProUGUI _placeNumber;
        [SerializeField] private GameObject _placeHolder;
        [SerializeField] private string _raceEventUid;
        [SerializeField] private string _idleAnimName = "Idle";
        [SerializeField] private string _impactAnimName = "Idle";
        [SerializeField] private EventPictureRenderer _eventPictureRenderer;

        [SerializeField] private RaceEventDebugPanel _raceEventDebugPanel;
        [SerializeField] private GameObject _rankUpdateFX;
        [SerializeField] private float _speechBubbleDelay = 0.5f;

        [SerializeField] private ScaleUpAnimator _speechBubbleScaleUpAnimator;
        [SerializeField] private LocalizedTextPro _speechBubbleLocalizedText;
        [Header("Score Multiplier")]
        [SerializeField] private GameObject _scoreMultiplierHolder;
        [SerializeField] private Animation _scoreMultiplierAnimation;

        private int _lastScoreOnSpeechBubbleShow = -1;

        private IRaceEventManager _raceEventManager;
        private INotifierStatus _notifier;

        private bool _shouldShowSpeechBubble;
        private RaceEvent _raceEvent;

        // Design decision to show it all the time, but on click - there is fleeting text
        protected override bool ConnectionDependent => false;
        protected override INotifierStatus Notifier => NotificationManager.GetRaceEventNotifier(_raceEventUid);
        protected override bool ShouldShowTimer { get; set; }
        protected override bool ShouldShowDebugButton => true;
        protected override string CurrencyUid => InventoryItems.GetRaceEventStageCurrency(_raceEvent.GetRaceStageUid());
        protected override string HudBlockUid => _raceEventUid;

        protected override TimeSpan GetRemainingTime(string uid)
        {
            Debug.LogError(_raceEventUid + " " + _raceEvent.RaceEventType);
            return _raceEvent.GetTimeLeft(uid);
        }

        protected override void OnInit(IContext context)
        {
            _raceEventManager = context.Resolve<IRaceEventManager>();

            var bbbDebug = AppDefinesConverter.BbbDebug;
            if (bbbDebug)
            {
                _raceEventDebugPanel.Init(context);
                _raceEventDebugPanel.Refresh(_raceEventUid);
            }

            _notifier = NotificationManager.GetRaceEventNotifier(_raceEventUid);
        }

        protected override void ButtonAction()
        {
            var notifierStatus = NotificationManager.GetRaceEventNotifier(_raceEventUid).GetStatus();
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.TapOnHud.Name, DauInteractions.TapOnHud.GameEvent, _raceEventUid, notifierStatus));

            if (!ConnectivityStatusManager.ConnectivityReachable && _raceEvent.Joined)
            {
                ShowOfflineText();
                return;
            }

            _raceEventManager.TryToShowRace(_raceEvent);
        }

        protected override void DebugButtonHandler()
        {
            _raceEventDebugPanel.gameObject.SetActive(!_raceEventDebugPanel.gameObject.activeSelf);
        }

        protected override bool ShouldBeShown()
        {
            var currentScreen = GetCurrentScreenType();
            if (currentScreen != ScreenType.EpisodeScreen && currentScreen != ScreenType.SideMapScreen)
                return false;

            var raceEvent = _raceEventManager.GetRaceEvent(_raceEventUid);
            return raceEvent != null && raceEvent.ShouldShowIcon();
        }

        protected override void OnShow()
        {
            _raceEvent = _raceEventManager.GetRaceEvent(_raceEventUid);
            Debug.LogError(_raceEventUid + " " + _raceEvent.RaceEventType);
            _rankUpdateFX.SetActive(false);
            RefreshRewardState();

            foreach (var image in _ribbonImages)
            {
                if (image != null)
                {
                    image.color = _raceEvent.GetTimeLeft().TotalSeconds <= 0 ? _finishedColor : _defaultColor;
                }
            }

            UpdateViewWithRemoteData(_raceEvent);

            if (_raceEvent.AlmostWon())
            {
                var score = _raceEvent.CurrentScore;
                if (score != _lastScoreOnSpeechBubbleShow && _shouldShowSpeechBubble)
                {
                    Rx.Invoke(_speechBubbleDelay, _ =>
                    {
                        _speechBubbleLocalizedText.ClearText();
                        var levelsToWin = _raceEvent.GetCurrentRaceScoreGoal() - score;
                        _speechBubbleLocalizedText.SetTextId(levelsToWin == 1 ? SingleWinSpeechBubbleTextID : AlmostWonSpeechBubbleTextID, levelsToWin);

                        _speechBubbleScaleUpAnimator.PlayForth();

                        _lastScoreOnSpeechBubbleShow = score;
                    });
                }
            }

            _shouldShowSpeechBubble = false;

            _eventPictureRenderer.RefreshFromConfig<RaceEventVisualConfig>(_raceEventUid, HudIconSpineAnimPrefabName);
            _eventPictureRenderer.SetSpineAnimClipIfCan(_raceEventUid, HudIconSpineAnimPrefabName, _idleAnimName);
        }

        protected override void OnHide()
        {
            _speechBubbleScaleUpAnimator.SetStartScaleInstantly();
            _raceEvent = null;
        }

        protected override void Subscribe()
        {
            base.Subscribe();

            _raceEvent.OnRemoteDataUpdated += UpdateViewWithRemoteData;
            _raceEvent.OnRaceEventReleased += RaceEventReleasedHandler;

            _notifier.StatusUpdated += RefreshRewardState;
            EasyTouch.On_SimpleTap += SimpleTapHandler;
        }

        protected override void Unsubscribe()
        {
            base.Unsubscribe();

            if (_notifier != null)
            {
                _notifier.StatusUpdated -= RefreshRewardState;
            }

            EasyTouch.On_SimpleTap -= SimpleTapHandler;

            if (_raceEvent != null)
            {
                _raceEvent.OnRemoteDataUpdated -= UpdateViewWithRemoteData;
                _raceEvent.OnRaceEventReleased -= RaceEventReleasedHandler;
            }
        }

        protected override void SubscribeLifetime()
        {
            base.SubscribeLifetime();
            EventDispatcher.AddListener<RaceEventUpdateRequest>(OnRaceEventDataUpdated);
        }

        protected override void UnsubscribeLifetime()
        {
            base.UnsubscribeLifetime();
            EventDispatcher?.RemoveListener<RaceEventUpdateRequest>(OnRaceEventDataUpdated);
        }

        private void RefreshRewardState()
        {
            var rewardCount = _notifier.GetRewardStatus();
            ShouldShowTimer = rewardCount <= 0;
            if (!ShouldShowTimer)
            {
                SetText(OpenLocUid);
            }
        }

        public override bool ShouldIgnoreSlotPriority()
        {
            var raceEvent = _raceEventManager.GetRaceEvent(_raceEventUid);
            return raceEvent != null && raceEvent.IsFinished() && raceEvent.IsInPrizePlace(true);
        }

        private void UpdateRankView(int deltaValue)
        {
            var previousPlaceNumber = _raceEvent.GetOwnPlaceBeforeDeltaScore(deltaValue);
            var placeNumber = _raceEvent.GetOwnPlace();
            SetPlace(placeNumber);
            _rankUpdateFX.SetActive(placeNumber > previousPlaceNumber);
        }

        private void SetPlace(int placeNumber)
        {
            var enabled = placeNumber > 0;
            if (enabled)
            {
                _placeNumber.text = placeNumber.ToString();
            }

            _placeHolder.SetActive(enabled);
        }

        private void SimpleTapHandler(Gesture gesture)
        {
            if (_speechBubbleScaleUpAnimator.LastPlayedForth)
            {
                _speechBubbleScaleUpAnimator.PlayBack();
            }
        }

        private void OnRaceEventDataUpdated(RaceEventUpdateRequest raceEventUpdateRequest)
        {
            if (raceEventUpdateRequest.Arg0.Uid == _raceEventUid && GenericHudManager.IsHudVisible && ShouldBeShown())
            {
                TryToShow();
            }
        }

        protected override void CurrenciesScheduledToVisualizeHandler(IReadOnlyCollection<(string currencyUid, int deltaValue)> currencies)
        {
            base.CurrenciesScheduledToVisualizeHandler(currencies);

            foreach (var (currencyUid, deltaValue) in currencies)
            {
                if (currencyUid != CurrencyUid)
                    continue;

                _shouldShowSpeechBubble = false;

                var raceEvent = _raceEventManager.GetRaceEvent(_raceEventUid);
                var placeNumber = raceEvent.GetOwnPlaceBeforeDeltaScore(deltaValue);
                SetPlace(placeNumber);
                return;
            }
        }

        protected override void StartHighlight(int deltaValue)
        {
            base.StartHighlight(deltaValue);
            _shouldShowSpeechBubble = false;

            if (_raceEvent.IsLastScoreAppend)
            {
                _scoreMultiplierHolder?.SetActive(true);
                _scoreMultiplierAnimation?.Play();
            }

            UpdateRankView(deltaValue);
            _eventPictureRenderer.SetSpineAnimClipIfCan(_raceEventUid, HudIconSpineAnimPrefabName, _impactAnimName);
        }

        protected override void EndHighlight()
        {
            base.EndHighlight();
            _shouldShowSpeechBubble = true;
        }

        private void RaceEventReleasedHandler(RaceEvent raceEvent)
        {
            if (raceEvent.Uid == _raceEventUid)
            {
                TryToHide();
            }
        }

        private void UpdateViewWithRemoteData(RaceEvent raceEvent)
        {
            if (raceEvent.Joined)
            {
                var placeNumber = raceEvent.GetOwnPlace();
                SetPlace(placeNumber);
            }
            else
            {
                SetPlace(0);
            }
        }
    }
}