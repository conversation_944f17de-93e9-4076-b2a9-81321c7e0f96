using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Net;
using System.Text;
using BBB.Core;
using BebopBee;
using Bebopbee.Core.Systems.RpcCommandManager;
using Core.Configs;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Utils;
using UnityEngine;
using UnityEngine.Networking;

namespace BBB
{
    public class FetchConfigsCommand : CommandBase
    {
        private IConfigsLoader _configLoader;
        private const int RequestTimeout = 5; //timeout after 5s and retry instead
        private readonly TimeSpan _pollInterval = TimeSpan.FromSeconds(1);
        private readonly TimeSpan _timeout = TimeSpan.FromSeconds(RequestTimeout);
        public override void Execute(DI.IContext context)
        {
            if (CurrentStatus == CommandStatus.Pending)
            {
                _configLoader = context.Resolve<IConfigsLoader>();
                CurrentStatus = CommandStatus.Success;
                CheckForNewConfigsAsync().Forget();
            }
            else
            {
                base.Execute(context);
            }
        }
        private async UniTask CheckForNewConfigsAsync()
        {
            var version = PlatformUtil.GetAppVersion();
            var platform = PlatformUtil.GetPlatform();
            var url = $"{GameConstants.ConfigsCacheUrl}manifests/{version}-{platform}.manifest?t={DateTime.Now.Ticks}";
            using var webRequest = UnityWebRequest.Get(url);
            webRequest.timeout = RequestTimeout;
            webRequest.SetRequestHeader("Accept-Encoding", "identity");
            try
            {
                if (!ConnectivityStatusManager.ConnectivityReachable)
                {
                    
                    await UniTask.WhenAny(WaitForConnectivity(_pollInterval), UniTask.Delay(_timeout));

                    if (!ConnectivityStatusManager.ConnectivityReachable)
                    {
                        throw new TimeoutException($"Network wasn't reachable for {RequestTimeout}s");
                    }
                }

                await webRequest.SendWebRequest();
            }
            catch (Exception ex)
            {
                var errorMessage = $"[Config] Error fetching config file from: {url}, {ex.Message}";

                if (ex is TimeoutException or WebException { Status: WebExceptionStatus.Timeout } 
                    or UnityWebRequestException { Result: UnityWebRequest.Result.ConnectionError or UnityWebRequest.Result.DataProcessingError })
                {
                    BDebug.LogWarning(LogCat.Config, errorMessage);
                }
                else
                {
                    BDebug.LogError(LogCat.Config, errorMessage);
                }
                return;
            }

            if (webRequest.result == UnityWebRequest.Result.Success)
            {
                try
                {
                    // File is compressed by Python using zlib which adds 2 bytes at the beginning and 4 bytes at the end as header and checksum 
                    using var inputStream = new MemoryStream(webRequest.downloadHandler.data, 2, webRequest.downloadHandler.data.Length - 6);
                    await using var deflateStream = new DeflateStream(inputStream, CompressionMode.Decompress);
                    using var outputStream = new MemoryStream();
                    await deflateStream.CopyToAsync(outputStream);
                    var latestConfigs = Encoding.UTF8.GetString(outputStream.ToArray());
                    if (MiniJSON.Json.Deserialize(latestConfigs) is Dictionary<string, object> configDict)
                    {
                        BDebug.Log(LogCat.Config, "Config Manifest downloaded checking if new configs are available");
                        CheckIfNewConfigsAvailable(configDict);
                    }
                }
                catch (Exception e)
                {
                    Debug.LogException(e);
                }
            }
            else
            {
                BDebug.LogError(LogCat.Config, webRequest.error);
            }
        }

        private async void CheckIfNewConfigsAvailable(Dictionary<string, object> configDict)
        {
            var configHashes = new Dictionary<string, string>();
            foreach (var confType in _configLoader.GetConfigsToLoad())
            {
                var configName = confType.GetName();
                if (configDict.ContainsKey(configName))
                {
                    var manifest = _configLoader.GetManifestConfigInfo(configName);
                    var value = configDict[confType.GetName()] as Dictionary<string, object>;
                    if (manifest == null || manifest.Hash != value.GetSafe("hash") as string)
                    {
                        configHashes[configName] = value.GetSafe("hash") as string;
                    }
                }
            }

            var textConfigName = LanguageHelper.GetSystemLanguageTextConfigName();
            var textConfigManifest = _configLoader.GetManifestConfigInfo(textConfigName);
            var textConfigValue = configDict[textConfigName] as Dictionary<string, object>;
            if (textConfigManifest == null || textConfigManifest.Hash != textConfigValue.GetSafe("hash") as string)
            {
                configHashes[textConfigName] = textConfigValue.GetSafe("hash") as string;
            }

            if (configHashes.Count > 0)
            {
                BDebug.Log(LogCat.Config, $"FetchConfigsCommand: {BBB.MiniJSON.Json.Serialize(configHashes)}");
                await RpcCommands.SendAsync<Bebopbee.Core.Systems.RpcCommandManager.Core.FetchConfigsCommand>(configHashes);
            }
            else
            {
                BDebug.Log(LogCat.Config, $"No new configs available");
            }
            
            CurrentStatus = CommandStatus.Success;
        }

        private static async UniTask WaitForConnectivity(TimeSpan pollingInterval)
        {
            while (!ConnectivityStatusManager.ConnectivityReachable)
            {
                await UniTask.Delay(pollingInterval);
            }
        }
    }
}