using System.Collections.Generic;
using BBB.M3Editor;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class DebugReplayLevelPanelController : DebugPanelController
    {
        [SerializeField] private Button _btnPasteData;
        [SerializeField] private Button _playAllButton;
        [SerializeField] private Button _playOneStepButton;
        [SerializeField] private Text _pastedText;
        
        [SerializeField] private ReplayListItem _itemTemplate;
        [SerializeField] private RectTransform _listContent;

        private List<ReplayListItem> _listInstances = new();
        private ReplayLevelSystem _replaySystem = new();
        
        private ReplayListItem _selectedItem;
        private bool _isPlaying;
        private bool _isPlayingOneStep;
        private bool _resumeNextStep;

        protected override void OnInit()
        {
            base.OnInit();
            _btnPasteData.ReplaceOnClick(OnPasteData);
        }

        private void OnPasteData()
        {
            var te = new TextEditor();
            te.Paste();
            _replaySystem.SetFromBinaryData(te.text);
            _pastedText.text = _replaySystem.ToString(); 
            PopulateTurnList();
            _playAllButton.interactable = true;
        }

        private void PopulateTurnList()
        {
            _replaySystem.PopulateListView(_itemTemplate, _listInstances, _listContent, OnItemSelected);
            _playAllButton.interactable = !_replaySystem.Records.IsNullOrEmpty();
            _playOneStepButton.interactable = !_replaySystem.Records.IsNullOrEmpty();
        }
        private void OnItemSelected(ReplayListItem item)
        {
            if (_isPlaying) return;
            
            var index = _listInstances.IndexOf(item);
            SetSelectedItem(index);
        }

        private void SetSelectedItem(int index)
        {
            if (index < 0) return;
            if (_selectedItem != null)
            {
                _selectedItem.SetSelected(false);
            }
            _selectedItem = _listInstances[index];
            _selectedItem.SetSelected(true);
        }
    }
}