using System.Linq;
using BBB.BrainCloud;
using UnityEngine;
using UnityEngine.UI;
using BBB.Wallet;
using BBB.Core.Wallet;

namespace BBB
{
    public class DebugWalletPanelController : DebugPanelController
    {
        [SerializeField] private DebugAddSubItem _earnedLifes;
        [SerializeField] private DebugAddSubItem _earnedCoins;
        [SerializeField] private DebugAddSubItem _earnedVipPoints;

        [SerializeField] private InputField _earnedLifesInput;
        [SerializeField] private InputField _earnedCoinsInput;
        [SerializeField] private InputField _earnedVipPointsInput;
        [SerializeField] private InputField _extraRewards;
        [SerializeField] private InputField _extraRewardsAmount;

        [SerializeField] private Text _minutes;

        [SerializeField] private Toggle _lineBreaker;
        [SerializeField] private Toggle _bomb;
        [SerializeField] private Toggle _bolt;

        [SerializeField] private Button _addExtra;
        [SerializeField] private Button _nullifyAllBoostersAndCurrencies;
        [SerializeField] private Button _removeInfLives;
        [SerializeField] private Button _timedInfiniteBoosters;

        private IWalletManager _walletManager => DebugScreenController.WalletManager;
        private IInventory _inventory => DebugScreenController.PlayerManager.PlayerInventory;
        private ILivesManager _livesManager => DebugScreenController.LivesManager;
        private IPlayer _player => DebugScreenController.PlayerManager.Player;
        private IBoosterManager _boosterManager => DebugScreenController.BoosterManager;
        private BrainCloudManager _brainCloudManager => DebugScreenController.BrainCloudManager;

        void Start()
        {
            _earnedLifes.onAdd += () => { MakeAddTransaction(WalletCurrencies.LifeCurrency); };
            _earnedLifes.onSub += () => { MakeSubTransaction(WalletCurrencies.LifeCurrency); };

            _earnedCoins.onAdd += () => MakeAddTransaction(WalletCurrencies.RegularCurrency);
            _earnedCoins.onSub += () => MakeSubTransaction(WalletCurrencies.RegularCurrency);

            _earnedVipPoints.onAdd += () => MakeAddTransaction(WalletCurrencies.VipCurrency);
            _earnedVipPoints.onSub += () => MakeSubTransaction(WalletCurrencies.VipCurrency);

            _earnedLifes.getValue += () => _walletManager.Balance.GetBalance().GetEarned(WalletCurrencies.LifeCurrency).ToCurrency();
            _earnedCoins.getValue += () => _walletManager.Balance.GetBalance().GetEarned(WalletCurrencies.RegularCurrency).ToCurrency();
            _earnedVipPoints.getValue += () => _walletManager.DebugResourceWalletBalance.GetEarned(WalletCurrencies.VipCurrency).ToCurrency();

            _earnedLifesInput.onEndEdit.AddListener(EarnedLifesEndEdit);
            _earnedCoinsInput.onEndEdit.AddListener(EarnedCoinsEndEdit);
            _earnedVipPointsInput.onEndEdit.AddListener(EarnedVIPEndEdit);

            _addExtra.ReplaceOnClick(AddExtraHandler);
            _nullifyAllBoostersAndCurrencies.ReplaceOnClick(ResetAllBoosters);

            if (_removeInfLives != null)
            {
                _removeInfLives.onClick.AddListener(() =>
                {
                    if (_livesManager.IsInfiniteLivesActive)
                    {
                        _player.InfiniteLivesEndTimestamp = 0;
                    }
                });
            }

            if (_timedInfiniteBoosters != null)
            {
                _timedInfiniteBoosters.onClick.AddListener(() =>
                {
                    if (_lineBreaker.isOn)
                    {
                        _boosterManager.SetInfiniteBoosterTimePeriod("linecrush", GetIntValueOfText(_minutes), true);
                    }

                    if (_bomb.isOn)
                    {
                        _boosterManager.SetInfiniteBoosterTimePeriod("bomb", GetIntValueOfText(_minutes), true);
                    }

                    if (_bolt.isOn)
                    {
                        _boosterManager.SetInfiniteBoosterTimePeriod("lightningstrike", GetIntValueOfText(_minutes), true);
                    }
                });
            }
        }

        private void ResetAllBoosters()
        {
            var balance = _walletManager.Balance.GetBalance();
            foreach (var kv in balance.EarnedDictionary)
            {
                var income = kv.Value + balance.GetBought(kv.Key);
                var outcome = balance.GetSpent(kv.Key);

                if (income > outcome)
                {
                    var toSpent = income - outcome;
                    _walletManager.TransactionController.MakeTransaction(new Transaction().Earn(kv.Key, -toSpent).AddTag(TransactionTag.Debug).SetDebug());
                }
            }

            _inventory.Boosters.Clear();
            _inventory.InfiniteBoostersTimeStamps.Clear();
            _inventory.EquippedAutoBoosters.Clear();

            if (_livesManager.IsInfiniteLivesActive)
            {
                _player.InfiniteLivesEndTimestamp = 0;
            }

            RefreshAll();
        }

        private int GetIntValueOfText(Text text)
        {
            int value;
            int.TryParse(text.text, out value);
            return value;
        }

        private void AddExtraHandler()
        {
            var currencyType = _extraRewards.text;
            if (!int.TryParse(_extraRewardsAmount.text, out var amount))
                amount = 1;
            _walletManager.TransactionController.MakeTransaction(new Transaction().Earn(currencyType, amount).AddTag(TransactionTag.Debug).SetDebug());
            RefreshAll();
        }

        private void MakeAddTransaction(string currencyType)
        {
            _walletManager.TransactionController.MakeTransaction(new Transaction().Earn(currencyType, 1).AddTag(TransactionTag.Debug).SetDebug());
            RefreshAll();
            if (currencyType == WalletCurrencies.VipCurrency)
                _brainCloudManager.DebugAddCurrency(WalletCurrencies.VipCurrency, 1, false);
        }

        private void MakeSubTransaction(string currencyType)
        {
            _walletManager.TransactionController.MakeTransaction(new Transaction().Earn(currencyType, -1).AddTag(TransactionTag.Debug).SetDebug());
            RefreshAll();
            if (currencyType == WalletCurrencies.VipCurrency)
                _brainCloudManager.DebugAddCurrency(WalletCurrencies.VipCurrency, -1, false);
        }

        void RefreshAll()
        {
            DebugScreenController.PlayerManager.MarkDirty();
            DebugScreenController.UIWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.Debug);

            _earnedLifes.Refresh();
            _earnedCoins.Refresh();
            _earnedVipPoints.Refresh();
        }

        void EndEditHandler(string value, string currency)
        {
            if (int.TryParse(value, out var number))
            {
                if (number >= 0)
                {
                    var val = number - (int)_walletManager.Balance.GetBalance().GetCurrencyDelta(currency);
                    _walletManager.TransactionController.MakeTransaction(new Transaction().Earn(currency, val).AddTag(TransactionTag.Debug).SetDebug());
                    RefreshAll();
                }
            }
        }

        void EarnedCoinsEndEdit(string value)
        {
            EndEditHandler(value, WalletCurrencies.RegularCurrency);
        }

        void EarnedLifesEndEdit(string value)
        {
            EndEditHandler(value, WalletCurrencies.LifeCurrency);
        }

        private void EarnedVIPEndEdit(string value)
        {
            EndEditHandler(value, WalletCurrencies.VipCurrency);
            if (int.TryParse(value, out var number))
                _brainCloudManager.DebugAddCurrency(WalletCurrencies.VipCurrency, number, true);
        }
    }
}