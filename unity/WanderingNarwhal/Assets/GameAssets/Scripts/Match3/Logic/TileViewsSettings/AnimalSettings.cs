using System;
using BBB;
using BBB.Audio;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    [Obsolete("Use AnimalSettingsScriptableObject instead.")]
    public class AnimalSettings : BbbMonoBehaviour
    {
        [SerializeField] private AnimationCurve _animalGoalAnimCurve;
        [SerializeField] private AnimationCurve _animalGoalEndAnimCurve;
        [SerializeField] private AnimationCurve _animalGoalScaleCurve;
        [SerializeField] private float _animalFlyDurationFirstFirst = 2f;
        [SerializeField] private float _animalFlyDurationSecond = 2f;
        [SerializeField] private float _animalSoundDelay = 0.5f;
        [SerializeField] private string _startSoundUid = Match3SoundIds.AnimalReleasedFromFrame;
        [SerializeField] private string _endSoundUid = Match3SoundIds.TileKindLanding;
    }
}