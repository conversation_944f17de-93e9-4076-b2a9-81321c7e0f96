using BBB.Core;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB
{
    public class TripleLineBreakerFX : ActivePoolItem
    {
        [SerializeField]
        private BombRenderer _bombIcon;

        [SerializeField]
        private LineBreakerRenderer _lineBreakerIcon;
        
        private Animator _animator;

        private void Awake()
        {
            _animator = GetComponent<Animator>();
        }

        public void Init(M3Settings m3Settings, TileSpeciality specialityLineBreaker)
        {
            if (_bombIcon)
            {
                _bombIcon.Setup(m3Settings, TileSpeciality.Bomb);
            }

            if (_lineBreakerIcon)
            {
                _lineBreakerIcon.Setup(m3Settings, specialityLineBreaker);
            }
        }

        public override void OnRelease()
        {
            base.OnRelease();
            if (!Initialized && _animator != null)
                _animator.Rebind();
        }
    }
}
