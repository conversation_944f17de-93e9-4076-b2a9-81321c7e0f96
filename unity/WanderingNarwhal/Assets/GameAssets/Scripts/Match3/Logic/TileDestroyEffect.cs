using UnityEngine;

namespace BBB
{
    public class TileDestroyEffect : B<PERSON>M<PERSON>Behaviour, IPoolItem
    {
        [SerializeField] private SpriteRenderer _splashImage;
        [SerializeField] private Animator _animator;
        [SerializeField] private ParticleSystemRenderer _psWithVariableMaterialRenderer;
        [SerializeField] private ParticleSystem _ps;

        public void SetExplosionPieceMaterial(Material material)
        {
            _psWithVariableMaterialRenderer.material = material;
        }

        void IPoolItem.OnInstantiate()
        {
            gameObject.SetActive(false);
            _animator.Rebind();
        }

        void IPoolItem.OnSpawn()
        {
            gameObject.SetActive(true);
            _ps.Play();
        }

        void IPoolItem.OnRelease()
        {
            gameObject.SetActive(false);
            _animator.Rebind();
            _animator.Update(0f);
            _ps.Clear();
        }
    }
}
