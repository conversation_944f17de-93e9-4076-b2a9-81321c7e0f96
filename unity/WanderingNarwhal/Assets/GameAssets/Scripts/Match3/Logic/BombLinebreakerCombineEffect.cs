using BBB;
using BBB.Core;
using BBB.Match3.Renderer;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class BombLinebreakerCombineEffect : ActivePoolItem
    {
        [SerializeField] private Transform _firstTileHolder;
        [SerializeField] private Transform _secondTileHolder;

        private Animator _animator;
        private Transform _initialFirstTileParent;
        private Transform _initialSecondTileParent;

        private Transform _firstTileViewTf;
        private Transform _secondTileViewTf;

        private TileView _firstTileView;
        private TileView _secondTileView;

        private Vector3 _initialLocalPositionFirst;
        private Vector3 _initialLocalPositionSecond;

        public void Setup(TileView firstTileView, TileView secondTileView, float time)
        {
            _firstTileView = firstTileView;
            _secondTileView = secondTileView;

            _firstTileViewTf = firstTileView.transform;
            _secondTileViewTf = secondTileView.transform;

            _initialLocalPositionFirst = _firstTileViewTf.localPosition;
            _initialLocalPositionSecond = _secondTileViewTf.localPosition;

            _initialFirstTileParent = _firstTileViewTf.parent;
            _initialSecondTileParent = _secondTileViewTf.parent;

            firstTileView.Animator.SetAnimatorEnabled(false);
            secondTileView.Animator.SetAnimatorEnabled(false);

            _firstTileViewTf.SetParent(_firstTileHolder, true);
            _secondTileViewTf.SetParent(_secondTileHolder, true);
        }

        private void OnAnimationFinished()
        {
            ReleaseViews();
            gameObject.Release();
        }

        private void ReleaseViews()
        {
            if (_firstTileViewTf)
            {
                _firstTileViewTf.SetParent(_initialFirstTileParent);
                _firstTileViewTf.localPosition = _initialLocalPositionFirst;
                _firstTileViewTf.localScale = new Vector3(1f, 1f, 1f);
                _firstTileViewTf.gameObject.SetActive(false);
                _firstTileViewTf = null;
            }

            if (_secondTileViewTf)
            {
                _secondTileViewTf.SetParent(_initialSecondTileParent);
                _secondTileViewTf.localPosition = _initialLocalPositionSecond;
                _secondTileViewTf.localScale = new Vector3(1f, 1f, 1f);
                _secondTileViewTf.gameObject.SetActive(false);
                _secondTileViewTf = null;
            }

            _initialFirstTileParent = null;
            _initialSecondTileParent = null;

            if (_firstTileView)
            {
                _firstTileView.Animator.SetAnimatorEnabled(true);
                _firstTileView = null;
            }

            if (_secondTileView)
            {
                _secondTileView.Animator.SetAnimatorEnabled(true);
                _secondTileView = null;
            }
        }

        public override void OnInstantiate()
        {
            if (!Initialized)
                _animator = GetComponent<Animator>();
            base.OnInstantiate();
        }

        public override void OnRelease()
        {
            if (this == null) return;

            base.OnRelease();

            if (_animator && !Initialized)
            {
                _animator.Rebind();
            }
        }
    }
}