using System;
using BBB.Core;
using GameAssets.Scripts.Match3.Settings;
using JetBrains.Annotations;
using Spine.Unity;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class BoltRegularTileCombine : ActivePoolItem
    {
        private const string ActionAnimation = "action";
        [SerializeField] 
        private Animator _animator;
        [SerializeField] 
        private SkeletonGraphic _skeletonGraphic;

        private Action _callback;

        public override void OnRelease()
        {
            _animator.speed = 1f;
            base.OnRelease();
        }

        private void OnAnimEnded()
        {
            _callback.SafeInvoke();
            _callback = null;
        }
        
        [UsedImplicitly]
        private void SetSpineAnimation()
        {
            if(_skeletonGraphic == null)return;
            _skeletonGraphic.AnimationState.ClearTracks();
            _skeletonGraphic.AnimationState.SetAnimation(0, ActionAnimation, false);
        }
        
        public void PlayClipWithDuration(M3Settings m3Settings, string stateName, float boltAnimDuration, Action callback)
        {
            if (_skeletonGraphic != null)
            {
                _skeletonGraphic.Skeleton.SetSkin(m3Settings.GetDiscoBallSkin());
            }

            _animator.Play(stateName);
            var clipInfo = _animator.GetCurrentAnimatorClipInfo(0);

            if (clipInfo != null && clipInfo.Length > 0)
            {
                var currentClipLength = clipInfo[0].clip.length;
                var speedMult = currentClipLength / boltAnimDuration;
                _animator.SetFloat("speed", speedMult);
                _callback = callback;
            }
        }
    }
}