using System;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Map;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Navigation;
using FBConfig;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Renderer
{
    public class TileResourceInfo
    {
        public TileLayerState State;
        public string PrefabName;
        public int DefaultOrder;
    }

    public class Match3ResourceHelper
    {
        private readonly bool _editorMode;
        private static readonly List<int> ProcessedSpawnersTemp = new ();
        private LevelsOrderingManager _levelOrderingManager;

        public Match3ResourceHelper(bool editorMode)
        {
            _editorMode = editorMode;
        }

        /// <summary>
        /// Get list of possible fx types, that may occur on current level.
        /// </summary>
        public List<FxType> GetUsedFxTypes(ILevel level, IConfig config, SpawnerSettings[] globalSpawners)
        {
            var result = new List<FxType>();
            ProcessedSpawnersTemp.Clear();

            if (_editorMode)
            {
                foreach (FxType fxType in Enum.GetValues(typeof(FxType)))
                {
                    result.Add(fxType);
                }
            }
            else
            {
                foreach (var fx in GetGridConditionedFxTypes(level.Grid))
                {
                    if (!result.Contains(fx))
                    {
                        result.Add(fx);
                    }
                }

                foreach (var fx in GetConfigConditionedFxTypes(level.Config, config))
                {
                    if (!result.Contains(fx))
                    {
                        result.Add(fx);
                    }
                }

                foreach (var fx in GetGoalConditionedFxTypes(level.Goals))
                {
                    if (!result.Contains(fx))
                    {
                        result.Add(fx);
                    }
                }

                foreach (var fx in GetFxTypesUsedInAnyLevel())
                {
                    if (!result.Contains(fx))
                    {
                        result.Add(fx);
                    }
                }

                foreach (var cell in level.Grid.Cells)
                {
                    if (cell.SpawnerUid <= 0 || ProcessedSpawnersTemp.Contains(cell.SpawnerUid))
                        continue;
                    
                    var spawner = SpawnerSettings.FindSpawnerByUid(globalSpawners, cell.SpawnerUid);
                    if (spawner == null)
                        continue;
                    
                    foreach (var tileSetting in spawner.TilesSettings)
                    {
                        foreach (var fx in GetFxTypeFromTileState((TileState)tileSetting.Mods))
                        {
                            if (!result.Contains(fx))
                            {
                                result.Add(fx);
                            }
                        }

                        foreach (var fx in GetFxTypeFromTileAsset(tileSetting.Asset))
                        {
                            if (!result.Contains(fx))
                            {
                                result.Add(fx);
                            }
                        }
                    }
                }
            }
            return result;
        }

        private static IEnumerable<FxType> GetGoalConditionedFxTypes(GoalState goalState)
        {
            var goalCount = goalState.NonZeroTypesCount();
            if (goalCount > 1 || (goalCount == 1 && goalState.GetGoalValue(GoalType.Score) == 0))
                yield return FxType.DoFlyResource;
        }

        private IEnumerable<FxType> GetConfigConditionedFxTypes(FBConfig.ProgressionLevelConfig levelConfig, IConfig config)
        {
            var eligibleBoosts = levelConfig.TrueEligibleBoosts();
            if (eligibleBoosts == null)
                yield break;
            
            var boostConfigDict = config.Get<BoosterConfig>();
            foreach (var boostUid in eligibleBoosts)
            {
                var enumValue = boostConfigDict.TryGetValue(boostUid, out var value) ? value.EnumName : null;
                if (string.IsNullOrWhiteSpace(enumValue))
                    continue;

                var boosterItem = enumValue.TryParseToEnum<BoosterItem>();

                switch (boosterItem)
                {
                    case BoosterItem.Wind:
                        yield return FxType.WindTrail;
                        break;
                    case BoosterItem.Shovel:
                        yield return FxType.Shovel;
                        break;
                    case BoosterItem.Balloon:
                        yield return FxType.Balloon;
                        break;
                    case BoosterItem.Vertical:
                        yield return FxType.VerticalBooster;
                        break;
                    case BoosterItem.Horizontal:
                        yield return FxType.HorizontalBooster;
                        break;
                }
            }
        }

        private static IEnumerable<FxType> GetGridConditionedFxTypes(Grid grid)
        {
            foreach (var cell in grid.Cells)
            {
                foreach (var fxType in cell.State.ToFxTypes())
                    yield return fxType;

                if (ReferenceEquals(cell.Tile, null))
                    continue;
                
                var tile = cell.Tile;

                foreach (var fx in GetFxTypeFromTileState(tile.State))
                {
                    yield return fx;
                }

                foreach (var fx in GetFxTypeFromSpeciality(tile.Speciality))
                {
                    yield return fx;
                }
            }
        }

        private static IEnumerable<FxType> GetFxTypeFromTileState(TileState state)
        {
            if ((state & TileState.IceCubeMod) != 0)
            {
                yield return FxType.IceCubeRemoval;
                yield return FxType.IceCubeSecondLayerRemove;
                yield return FxType.IceCubeThirdLevelRemove;
            }

            if ((state & TileState.ChainMod) != 0)
            {
                yield return FxType.ChainRemove;
                yield return FxType.ChainSecondLayerRemove;
                yield return FxType.ChainThirdLayerRemove;
            }

            if ((state & TileState.SandMod) != 0)
            {
                yield return FxType.SandRemove;
            }

            if ((state & TileState.AnimalMod) != 0)
                yield return FxType.AnimalRelease;

            if ((state & TileState.VaseMod) != 0)
            {
                yield return FxType.VaseDestroy;
                yield return FxType.VaseSecondLayerRemove;
                yield return FxType.VaseThirdLayerRemove;
            }

            if ((state & TileState.EggMod) != 0)
            {
                yield return FxType.EggLayerRemove;
                yield return FxType.EggDestroy;
                yield return FxType.BirdDestroy;
                yield return FxType.BirdAppear;
            }
            if ((state & TileState.FlowerPotMod) != 0)
            {
                yield return FxType.FlowerPotLayerRemove;
                yield return FxType.FlowerPotDestroy;
            }

            if ((state & TileState.BirdMod) != 0)
            {
                yield return FxType.BirdDestroy;
                yield return FxType.BirdAppear;
            }

            if ((state & TileState.MoneyBagMod) != 0)
            {
                yield return FxType.MoneyBagDestroy;
                yield return FxType.MoneyBagGoal;
            }

            if ((state & TileState.PenguinMod) != 0)
            {
                yield return FxType.PenguinDestroy;
            }

            if ((state & TileState.WatermelonMod) != 0)
            {
                yield return FxType.WatermelonDestroy;
                yield return FxType.WatermelonSecondLayerRemove;
                yield return FxType.WatermelonThirdLayerRemove;
            }

            if ((state & TileState.ColorCrateMod) != 0)
            {
                yield return FxType.ColorCrateDestroy;
                yield return FxType.ColorCrateSecondLayerRemove;
                yield return FxType.ColorCrateThirdLayerRemove;
            }

            if ((state & TileState.SheepMod) != 0)
            {
                yield return FxType.SheepDestroy;
                yield return FxType.BananaAppear;
                yield return FxType.BananaDestroy;
            }

            if ((state & TileState.BananaMod) != 0)
            {
                yield return FxType.BananaAppear;
                yield return FxType.BananaDestroy;
            }

            if ((state & TileState.MonkeyMod) != 0)
            {
                yield return FxType.MonkeyDestroy;
                yield return FxType.BananaAppear;
                yield return FxType.BananaDestroy;
            }

            if ((state & TileState.SkunkMod) != 0)
            {
                yield return FxType.SkunkAttack;
            }

            if ((state & TileState.HenMod) != 0)
            {
                yield return FxType.ChickenAppear;
                yield return FxType.ChickenDestroy;
            }

            if ((state & TileState.HiveMod) != 0)
            {
                yield return FxType.BeeAppear;
                yield return FxType.BeeDestroy;
            }

            if ((state & TileState.MoleMod) != 0)
            {
                yield return FxType.MoleDestroy;
            }

            if ((state & TileState.BushMod) != 0)
            {
                yield return FxType.BushTileHideLayer1;
                yield return FxType.BushTileHideLayer2;
                yield return FxType.BushTileHideLayer3;
                yield return FxType.BushTileHideLayer4;
                yield return FxType.BushTileHideLayer5;
            }
            
            if ((state & TileState.SafeMod) != 0)
            {
                yield return FxType.SafeTileHideLayer1;
                yield return FxType.SafeTileHideLayer2;
                yield return FxType.SafeTileHideLayer3;
                yield return FxType.SafeTileHideLayer4;
            }
            if ((state & TileState.BowlingMod) != 0)
            {
                yield return FxType.BowlingPin1;
                yield return FxType.BowlingPin2;
                yield return FxType.BowlingPin3;
                yield return FxType.BowlingPin4;
                yield return FxType.BowlingPin5;
                yield return FxType.BowlingPin6;
                yield return FxType.BowlingTileDestroy;
            }
            if ((state & TileState.SodaMod) != 0)
            {
                yield return FxType.SodaBottleDestroy;
                yield return FxType.SodaTileDestroy;
            }
            if ((state & TileState.IceBarMod) != 0)
            {
                yield return FxType.IceBarLayerRemove;
                yield return FxType.IceBarTileDestroy;
            }
            if ((state & TileState.MetalBarMod) != 0)
            {
                yield return FxType.MetalBarLayerRemove;
                yield return FxType.MetalBarTileDestroy;
            }
            if ((state & TileState.DynamiteBoxMod) != 0)
            {
                yield return FxType.DynamiteBoxTileDestroy;
            }
            if ((state & TileState.GiantPinataMod) != 0)
            {
                yield return FxType.GiantPinataTileHideLayer1;
                yield return FxType.GiantPinataTileHideLayer2;
                yield return FxType.GiantPinataTileHideLayer3;
                yield return FxType.GiantPinataTileHideLayer4;
            }
            if ((state & TileState.ShelfMod) != 0)
            {
                yield return FxType.ShelfItemDestroy;
                yield return FxType.ShelfTileDestroy;
            }
            if ((state & TileState.JellyFishMod) != 0)
            {
                yield return FxType.JellyFishTileDestroy;
            }
            if ((state & TileState.GoldenScarabMod) != 0)
            {
                yield return FxType.GoldenScarabEmpty;
                yield return FxType.GoldenScarabFull;
                yield return FxType.GoldenScarabDestroy;
            }
            if ((state & TileState.GondolaMod) != 0)
            {
                yield return FxType.GondolaDestroy;
            }
        }

        private static IEnumerable<FxType> GetFxTypeFromSpeciality(TileSpeciality speciality)
        {
            switch (speciality)
            {
                case TileSpeciality.Litter:
                {
                    yield return FxType.LitterDestroy;
                    break;
                }
                case TileSpeciality.Sticker:
                {
                    yield return FxType.StickerRemove;
                    yield return FxType.StickerSecondLayerRemove;
                    yield return FxType.StickerThirdLayerRemove;
                    break;
                }
                case TileSpeciality.Pinata:
                    yield return FxType.PinataRemoval;
                    break;
                case TileSpeciality.Frame:
                    yield return FxType.FrameRemoval;
                    yield return FxType.FrameLayerRemoval;
                    break;
                case TileSpeciality.Squid:
                    yield return FxType.SquidDestroy;
                    yield return FxType.SquidGoal;
                    yield return FxType.SquidHit;
                    break;
                case TileSpeciality.Toad:
                    yield return FxType.ToadGoal;
                    break;
                case TileSpeciality.Bush:
                {
                    yield return FxType.BushTileHideLayer1;
                    yield return FxType.BushTileHideLayer2;
                    yield return FxType.BushTileHideLayer3;
                    yield return FxType.BushTileHideLayer4;
                    yield return FxType.BushTileHideLayer5;
                    break;
                }
                case TileSpeciality.MagicHat:
                    yield return FxType.MagicHatGoal;
                    break;
                case TileSpeciality.Safe:
                {
                    yield return FxType.SafeTileHideLayer1;
                    yield return FxType.SafeTileHideLayer2;
                    yield return FxType.SafeTileHideLayer3;
                    yield return FxType.SafeTileHideLayer4;
                    yield return FxType.SafeTileHideLayer5;
                    break;
                }
                case TileSpeciality.Bowling:
                    yield return FxType.BowlingPin1;
                    yield return FxType.BowlingPin2;
                    yield return FxType.BowlingPin3;
                    yield return FxType.BowlingPin4;
                    yield return FxType.BowlingPin5;
                    yield return FxType.BowlingPin6;
                    yield return FxType.BowlingTileDestroy;
                    break;
                case TileSpeciality.Soda:
                    yield return FxType.SodaBottleDestroy;
                    yield return FxType.SodaTileDestroy;
                    break;
                case TileSpeciality.IceBar:
                    yield return FxType.IceBarLayerRemove;
                    yield return FxType.IceBarTileDestroy;
                    break;
                case TileSpeciality.MetalBar:
                    yield return FxType.MetalBarLayerRemove;
                    yield return FxType.MetalBarTileDestroy;
                    break;
                case TileSpeciality.DynamiteBox:
                    yield return FxType.DynamiteBoxTileDestroy;
                    break;
                case TileSpeciality.GiantPinata:
                    yield return FxType.GiantPinataTileHideLayer1;
                    yield return FxType.GiantPinataTileHideLayer2;
                    yield return FxType.GiantPinataTileHideLayer3;
                    yield return FxType.GiantPinataTileHideLayer4;
                    yield return FxType.SlotMachineLineBreakerAppear;
                    yield return FxType.SlotMachineColumnBreakerAppear;
                    yield return FxType.SlotMachineColorBombAppear;
                    yield return FxType.SlotMachineBombAppear;
                    yield return FxType.SlotMachinePropellerAppear;
                    break;
                case TileSpeciality.Shelf:
                    yield return FxType.ShelfItemDestroy;
                    yield return FxType.ShelfTileDestroy;
                    break;
                case TileSpeciality.Gondola:
                    yield return FxType.GondolaDestroy;
                    break;
                case TileSpeciality.TukTuk:
                    yield return FxType.TukTukEffect;
                    break;
                case TileSpeciality.FireWorks:
                    yield return FxType.FireWorksFlight;
                    yield return FxType.FireWorksDestroy;
                    yield return FxType.FireWorksRemoveLayer;
                    yield return FxType.FireWorksTileDestroy;
                    break; 
                case TileSpeciality.SlotMachine:
                    yield return FxType.SlotMachineTileHideLayer1;
                    yield return FxType.SlotMachineTileHideLayer2;
                    yield return FxType.SlotMachineTileHideLayer3;
                    yield return FxType.SlotMachineTileHideLayer4;
                    yield return FxType.SlotMachineLineBreakerAppear;
                    yield return FxType.SlotMachineColumnBreakerAppear;
                    yield return FxType.SlotMachineColorBombAppear;
                    yield return FxType.SlotMachineBombAppear;
                    yield return FxType.SlotMachinePropellerAppear;
                    break;
            }
        }

        /// <summary>
        /// Tile asset to fx mapping. Used only for spawners, so it checks only spawnable tile types.
        /// </summary>
        private static IEnumerable<FxType> GetFxTypeFromTileAsset(TileAsset asset)
        {
            switch (asset)
            {
                case TileAsset.Litter:
                    yield return FxType.LitterDestroy;
                    break;
                case TileAsset.Pinata:
                    yield return FxType.PinataRemoval;
                    yield return FxType.PinataAppear;
                    break;
                case TileAsset.Watermelon:
                    yield return FxType.WatermelonDestroy;
                    yield return FxType.WatermelonSecondLayerRemove;
                    yield return FxType.WatermelonThirdLayerRemove;
                    break;
                case TileAsset.MoneyBag:
                    yield return FxType.MoneyBagDestroy;
                    yield return FxType.MoneyBagGoal;
                    break;
                case TileAsset.Penguin:
                    yield return FxType.PenguinDestroy;
                    break;
                case TileAsset.Egg:
                    yield return FxType.EggDestroy;
                    yield return FxType.EggLayerRemove;
                    yield return FxType.BirdAppear;
                    yield return FxType.BirdDestroy;
                    break;
                case TileAsset.FlowerPot:
                    yield return FxType.FlowerPotDestroy;
                    yield return FxType.FlowerPotLayerRemove;
                    break;
                case TileAsset.Bird:
                    yield return FxType.BirdAppear;
                    yield return FxType.BirdDestroy;
                    break;
                case TileAsset.Banana:
                    yield return FxType.BananaAppear;
                    yield return FxType.BananaDestroy;
                    break;
            }
        }

        private static IEnumerable<FxType> GetFxTypesUsedInAnyLevel()
        {
            yield return FxType.LineBreaker;
            yield return FxType.DoubleBomb;
            yield return FxType.PropellerDestroy;
            yield return FxType.TileRemove;
            yield return FxType.BoltTileDestroy;
            yield return FxType.ShakeOverlayEffect;
            yield return FxType.DestroyByBombBombCombo;
            yield return FxType.ScoreText;
            yield return FxType.TripleLineBreaker;
            yield return FxType.Whirlpool;
            yield return FxType.WhirlpoolSecondWave;
            yield return FxType.DoubleLineBreaker;
            yield return FxType.Match;
            yield return FxType.LightningBolt;
            yield return FxType.CircleWave;
            yield return FxType.TileStar;
            yield return FxType.Comet;
            yield return FxType.CometStart;
            yield return FxType.CometExplosion;
            yield return FxType.LightningBoltPiece;
            yield return FxType.LightningRainPiece;
            yield return FxType.LightningHit;
            yield return FxType.DoFlyStar;
            yield return FxType.Dim;
            yield return FxType.StarExplosion;
            yield return FxType.BoostRevealExplosion;
            yield return FxType.BombLinebreakerCombine;
            yield return FxType.BombBombCombine;
            yield return FxType.BoltBoosterCombine;
            yield return FxType.DiscoBallDiscoBallCombine;
            yield return FxType.SuperDiscoBallSuperDiscoBallCombine;
            yield return FxType.Anticipation;
            yield return FxType.BoosterSpawnFromBoltCombo;
            yield return FxType.BoltRegularTileCombine;
            yield return FxType.PropellerFlight;
            yield return FxType.PropellerFlightShadow;
            yield return FxType.PropellerComboFlight;
            yield return FxType.PropellerDestroy;
            yield return FxType.SmallCrossExplosion;
            yield return FxType.GrassAnticipation;
            yield return FxType.PetalRemove;
            yield return FxType.PetalAnticipation;
            yield return FxType.Swap;
            yield return FxType.DestructibleWallTripleRemove;
            yield return FxType.DestructibleWallDoubleRemove;
            yield return FxType.DestructibleWallSingleRemove;
            yield return FxType.BoosterTrail;
            yield return FxType.CollectEventTile;
            yield return FxType.DiscoRushCollect;
        }

        public List<TileResourceInfo> GetTileResourceInfos(ILevel level, SpawnerSettings[] globalSpawners)
        {
            var result = new List<TileResourceInfo>();
            ProcessedSpawnersTemp.Clear();

            if (_editorMode)
            {
                foreach (TileLayerState layerState in Enum.GetValues(typeof(TileLayerState)))
                {
                    if (layerState == TileLayerState.None)
                        continue;

                    result.Add(new TileResourceInfo
                    {
                        State = layerState,
                        PrefabName = layerState.ToPrefabName(),
                        DefaultOrder = layerState.ToSortOrder()
                    });
                }
            }
            else
            {
                var overallTileLayerState = TileLayerState.None;
                foreach (var cell in level.Grid.Cells)
                {
                    if (!ReferenceEquals(cell.Tile, null))
                    {
                        overallTileLayerState |= cell.Tile.ToLayerState();
                    }

                    if (cell.SpawnerUid > 0 && !ProcessedSpawnersTemp.Contains(cell.SpawnerUid))
                    {
                        ProcessedSpawnersTemp.Add(cell.SpawnerUid);
                        var spawner = SpawnerSettings.FindSpawnerByUid(globalSpawners, cell.SpawnerUid);
                        if (spawner == null)
                        {
                            UnityEngine.Debug.LogError(
                                $"Can't precache m3 resources because {((globalSpawners == null || globalSpawners.Length == 0) ? "Global spawners list is empty" : "Global spawners list doesn't contain spawner '" + cell.SpawnerUid + "' which is present on level grid")}");
                        }
                        else
                        {
                            foreach (var tileSpawnSetting in spawner.TilesSettings)
                            {
                                var tileTemp = TileFactory.CreateUnspecifiedTile(0, new TileOrigin(), tileSpawnSetting.Asset, (TileState)tileSpawnSetting.Mods, tileSpawnSetting.Kind, tileSpawnSetting.Params);

                                overallTileLayerState |= tileTemp.ToLayerState();
                            }
                        }
                    }
                }

                overallTileLayerState |= GetRelatedLayerState(overallTileLayerState);

                foreach (var layerState in GetUniversalLayerStates())
                {
                    overallTileLayerState |= layerState;
                }

                foreach (TileLayerState layerState in Enum.GetValues(typeof(TileLayerState)))
                {
                    if ((overallTileLayerState & layerState) != 0)
                    {
                        result.Add(new TileResourceInfo
                        {
                            State = layerState,
                            PrefabName = layerState.ToPrefabName(),
                            DefaultOrder = layerState.ToSortOrder()
                        });
                    }
                }

                result.Add(new TileResourceInfo()
                {
                    State = TileLayerState.GameEventLabel,
                    PrefabName = TileLayerState.GameEventLabel.ToPrefabName(),
                    DefaultOrder = TileLayerState.GameEventLabel.ToSortOrder(),
                });

                result.Add(new TileResourceInfo()
                {
                    State = TileLayerState.StealingHatLabel,
                    PrefabName = TileLayerState.StealingHatLabel.ToPrefabName(),
                    DefaultOrder = TileLayerState.StealingHatLabel.ToSortOrder(),
                });
            }

            return result;
        }

        private static TileLayerState GetRelatedLayerState(TileLayerState state)
        {
            var result = TileLayerState.None;
            if ((state & TileLayerState.Egg) != 0)
            {
                result |= TileLayerState.Bird;
            }
            
            if ((state & TileLayerState.FlowerPot) != 0)
            {
                result |= TileLayerState.FlowerPot;
            }

            if ((state & TileLayerState.Sheep) != 0)
            {
                result |= TileLayerState.Banana;
            }

            if ((state & TileLayerState.Monkey) != 0)
            {
                result |= TileLayerState.Banana;
            }

            if ((state & TileLayerState.Hen) != 0)
            {
                result |= TileLayerState.Chicken;
            }

            if ((state & TileLayerState.Hive) != 0)
            {
                result |= TileLayerState.Bee;
            }
            
            if ((state & TileLayerState.GiantPinata) != 0)
            {
                result |= TileLayerState.Pinata;
            }

            return result;
        }

        private static IEnumerable<TileLayerState> GetUniversalLayerStates()
        {
            yield return TileLayerState.HorizontalLb;
            yield return TileLayerState.VerticalLb;
            yield return TileLayerState.Bomb;
            yield return TileLayerState.ColorBomb;
            yield return TileLayerState.Propeller;
            yield return TileLayerState.DropItem;
            yield return TileLayerState.Blinking;
            yield return TileLayerState.Undefined;
            yield return TileLayerState.Normal;
        }
    }
}
