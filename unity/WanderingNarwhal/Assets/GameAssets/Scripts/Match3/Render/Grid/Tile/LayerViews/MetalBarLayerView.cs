using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class MetalBarLayerView : SheetTileLayerViewBase
    {
        private MetalBarLayerRenderer _renderer;
        private int _prevLevel = -1;
        private int _sizeX;
        private int _sizeY;
        private int _metalBarOrientation;
        private Vector2 _cellSize;

        public MetalBarLayerView(ITileLayer layer) : base(layer)
        {
            
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<MetalBarLayerRenderer>();
            _cellSize = cellSize;
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            var count = tile.GetParam(TileParamEnum.AdjacentHp);
            return count;
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            if (newLevel < _prevLevel)
            {
                var pos = coords ?? Coords.Zero;
                SetupRendererSize(tile, pos, true);
                if (newLevel > 0)
                {
                    AudioProxy.PlaySound(Match3SoundIds.MetalBarMetalRemove);
                }
            }
            _prevLevel = newLevel;
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);
            SetupRendererSize(tile, coords, false);
            _renderer.OnRefresh();
        }

        private void SetupRendererSize(Tile tile, Coords coords, bool animate)
        {
            if (_renderer == null || tile == null)
                return;
            _renderer.CellSize = _cellSize;
            _sizeX = tile.GetParam(TileParamEnum.SizeX);
            _sizeY = tile.GetParam(TileParamEnum.SizeY);
            _metalBarOrientation = tile.GetParam(TileParamEnum.MetalBarOrientation);
            _renderer.UpdateSize(_sizeX, _sizeY, _metalBarOrientation, animate);
            if (animate)
            {
                FxRenderer.SpawnSingleAnimatorEffectWithHapticFeedback(coords, FxType.MetalBarLayerRemove, _renderer.DestroyDuration);
            }
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim, TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None)
        {
            base.Animate(coords, anim, animParams);
            switch (anim)
            {
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayAppear();
                    break;

                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;

                case TileLayerViewAnims.Destroy:
                    IsPlayingLayerViewAnimation = true;
                    AudioProxy.PlaySound(Match3SoundIds.MetalBarMachineRemove);
                    _renderer.PlayDestroy(OnDone);
                    void OnDone()
                    {
                        FxRenderer.SpawnSingleAnimatorEffectWithHapticFeedback(coords, FxType.MetalBarTileDestroy, _renderer.DestroyDuration);
                        IsPlayingLayerViewAnimation = false;
                    }
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
            }
        }
    }
}
