using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class PinataLayerView : TileLayerViewBase
    {
        private PinataLayerRenderer _renderer;

        public PinataLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<PinataLayerRenderer>();
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);
            _renderer.PlayIdle();
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);
            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                    AudioProxy.PlaySound(Match3SoundIds.PinataDestroy);
                    FxRenderer.SpawnSingleAnimatorEffect(coords, FxType.PinataRemoval, 3);
                    IsPlayingLayerViewAnimation = true;
                    _renderer.PlayDestroy(onDone: OnEndBlockingAnimation);
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }

        private void OnEndBlockingAnimation()
        {
            IsPlayingLayerViewAnimation = false;
        }
    }
}