using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class ChainTileLayerView : SheetTileLayerViewBase
    {
        private ChainLayerRenderer _renderer;
        private int _prevLevel = -1;
        private bool _destroyPlayed = false;

        public ChainTileLayerView(ITileLayer layer) : base(layer) { }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.ChainLayerCount);
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<ChainLayerRenderer>();
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            if (_renderer != null)
                _renderer.Refresh(newLevel);

            if (_prevLevel != newLevel)
            {
                if (newLevel < _prevLevel)
                {
                    //play sound here if needed

                    if (coords.HasValue)
                    {
                        AudioProxy.PlaySound(_prevLevel == 3
                            ? Match3SoundIds.LockChainRemove
                            : Match3SoundIds.ChainDestroy);

                        switch (newLevel)
                        {
                            case 2:
                            {
                                FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.ChainThirdLayerRemove);
                                break;
                            }
                            case 1:
                            {
                                FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.ChainSecondLayerRemove);
                                break;
                            }
                            case 0:
                            {
                                FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.ChainRemove);
                                AudioProxy.PlaySound(Match3SoundIds.ChainDestroy);
                                break;
                            }
                        }

                        ShakeWithSiblings();
                    }
                }

                _prevLevel = newLevel;
            }
        }

        public override void UnApply()
        {
            _prevLevel = -1;
            _destroyPlayed = false;

            base.UnApply();

        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim, animParams);

            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                case TileLayerViewAnims.Unapply:
                    if (_prevLevel > 0 && !_destroyPlayed)
                    {
                        FxRenderer.SpawnSingleAnimatorEffect(coords, FxType.ChainRemove);
                        AudioProxy.PlaySound(Match3SoundIds.ChainDestroy);
                        _destroyPlayed = true;
                    }
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }
    }
}
