using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class IceCubeLayerView : SheetTileLayerViewBase
    {
        private IceCubeLayerRenderer _renderer;
        private SpriteRenderer[] _images;

        private int _prevLevel = -1;
        private bool _destroyPlayed = false;
        
        public IceCubeLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.IceLayerCount);
        }
        
        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<IceCubeLayerRenderer>();
            _images = instance.GetComponentsInChildren<SpriteRenderer>(true);
            foreach (var image in _images)
                image.gameObject.SetActive(false);
            
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            foreach (var image in _images)
                image.gameObject.SetActive(false);
            
            if(newLevel >= 1 && newLevel <= 3)
                _images[3 - newLevel].gameObject.SetActive(true);

            if (_prevLevel != newLevel)
            {
                if (newLevel < _prevLevel)
                {
                    if (coords.HasValue)
                    {
                        switch (newLevel)
                        {
                            case 2:
                                AudioProxy.PlaySound(Match3SoundIds.IceCubeHit);
                                FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.IceCubeThirdLevelRemove);
                                break;
                            case 1:
                                AudioProxy.PlaySound(Match3SoundIds.IceCubeHit);
                                FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.IceCubeSecondLayerRemove);
                                break;
                            case 0:
                                AudioProxy.PlaySound(Match3SoundIds.IceCubeDestroy);
                                FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.IceCubeRemoval);
                                break;
                        }
                    }

                    if(newLevel >= 0)
                        ShakeWithSiblings();
                }
                _prevLevel = newLevel;
            }
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);
            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                case TileLayerViewAnims.Unapply:
                    if (_prevLevel > 0 && !_destroyPlayed)
                    {
                        FxRenderer.SpawnSingleAnimatorEffect(coords, FxType.IceCubeRemoval);
                        AudioProxy.PlaySound(Match3SoundIds.IceCubeDestroy);
                        _destroyPlayed = true;
                    }
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }

        public override void UnApply()
        {
            _prevLevel = -1;
            _destroyPlayed = false;
            
            foreach (var image in _images)
                image.gameObject.SetActive(false);
            
            base.UnApply();
              
        }
    }
}