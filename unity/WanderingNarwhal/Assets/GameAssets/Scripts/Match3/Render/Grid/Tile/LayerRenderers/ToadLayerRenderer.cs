using System;
using System.Collections;
using BBB.Audio;
using BBB.Core;
using BebopBee.Core.Audio;
using Bebopbee.Core.Extensions.Unity;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts.Utils;
using Spine;
using Spine.Unity;
using UnityEngine;
using Random = UnityEngine.Random;

namespace BBB.Match3.Renderer
{
    public class ToadLayerRenderer : DelayedAppearLayerRenderer
    {
        private const float DefaultRectTransformMultiplier = 1.1f;
        private const float ToadToHelpPanelRatio = 0.82f;
        
        [SerializeField] private SkeletonGraphic _sk;
        [SerializeField] private Canvas _baseCanvas;
        [SerializeField] private Canvas _toadTileBgCanvas;
        [SerializeField] private RectTransform _contentRectTransform;
        
        [Header("Animation Settings")]
        [SerializeField] private Animator _toadTileBGAnimator;
        [SerializeField] private string _appearAnim = "appear";
        [Range(0f, 1f)]
        [SerializeField] private float _appear1Probability = 0.5f;
        [SerializeField] private string _appearAnim1 = "appear";
        [SerializeField] private string _idle0Anim = "idle_lazy";
        [Range(0f, 1f)]
        [SerializeField] private float _idle1Probability = 0.3f;
        [SerializeField] private string _idle1Anim = "idle_lazy_var";
        [Range(0f, 1f)]
        [SerializeField] private float _sleepProbability = 0.05f;
        [SerializeField] private int _sleepCountMin = 3;
        [SerializeField] private int _sleepCountMax = 10;
        [SerializeField] private string _sleepEnterAnim = "idletosleep";
        [SerializeField] private string _sleepLoopAnim = "sleeploop";
        [SerializeField] private string _sleepEndAnim = "sleeptoidle";
        [SerializeField] private int _afterMatchLoopCountMin = 0;
        [SerializeField] private int _afterMatchLoopCountMax = 3;
        [SerializeField] private string _afterMatchLoopAnim = "idle_angry_loop";
        [SerializeField] private string _afterMatchEndAnim = "idle_angry";
        [SerializeField] private string _matchAnim = "fly_release";
        [SerializeField] private string _tapFeedbackAnim = "idle_angry_2";
        [SerializeField] private string _previewAnim = "idle_lazy_var";
        [SerializeField] private string _destroyAnim = "end";
        [SerializeField] private string _sleepSoundUid = "toad_sleep";
        [SerializeField] private string _appearSoundUid = "toad_appear";

        
        [Tooltip("Max duration of destroy clips sequence.")]
        [SerializeField]private float _destroyAnimationFailsafeTimeout = 5f;
        
        private Action _currentOnDestroyCallback;
        private int _currentSleepCount;

        private bool _isCurrentlyDestroying;
        private int _destroyAnimsCountBeforeEnd;
        private static readonly int DestroyTileBg = Animator.StringToHash("Destroy");

        public void Setup(int size)
        {
            if (size < 1) size = 1;
            
            _toadTileBGAnimator.ResetAllParameters();
            _toadTileBgCanvas.sortingOrder = _baseCanvas.sortingOrder - 1;
            
            _contentRectTransform.anchorMin = Vector2.zero;
            _contentRectTransform.anchorMax = Vector2.one * (size * DefaultRectTransformMultiplier);
            _contentRectTransform.offsetMin = Vector2.zero;
            _contentRectTransform.offsetMax = Vector2.zero;
            _contentRectTransform.localScale = Vector3.one;
        }

        private void HelpPanelSetup()
        {
            _contentRectTransform.UpdateRectForTile(TileHelpMinOffset, TileHelpMaxOffset);
            _contentRectTransform.offsetMin = Vector2.zero;
            _contentRectTransform.offsetMax = Vector2.zero;
            _contentRectTransform.localScale = Vector3.one * ToadToHelpPanelRatio;
        }
        
        public override void OnRefresh()
        {
            base.OnRefresh();
            if (!_hideUntilAppear || IsPlayedAppear)
            {
                PlayIdle();
            }

            _sk.AnimationState.Start -= OnAnimStartCallback;
            _sk.AnimationState.Start += OnAnimStartCallback;
            _sk.AnimationState.Complete -= OnAnimEndCallback;
            _sk.AnimationState.Complete += OnAnimEndCallback;
        }

        public void PlayHit()
        {
            _sk.AnimationState.SetAnimation(0, _matchAnim, loop: false);
           
            var loopCount = Random.Range(_afterMatchLoopCountMin, _afterMatchLoopCountMax + 1);
            _sk.AnimationState.AddAnimation(0, _afterMatchEndAnim, loop: false, 0);
            for (int i = 0; i < loopCount; i++)
            {
                _sk.AnimationState.AddAnimation(0, _afterMatchLoopAnim, loop: false, 0);
            }
            PlayIdle(append: true);
            AudioProxy.PlaySound(Match3SoundIds.ToadHit);
        }
        
        private void PlayIdle(bool append = false)
        {
            if (append)
            {
                _sk.AnimationState.AddAnimation(0, _idle0Anim, loop: false, delay: 0);
            }
            else
            {
                _sk.AnimationState.SetAnimation(0, _idle0Anim, loop: false);
            }
        }

        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            if (this._doTweenTapFeedbackSettings.IsEnabled)
            {
                base.PlayTapFeedback(layerView);
                return;
            }

            if (_sk == null) return;
            Show();
            if (_sk.AnimationState == null)
            {
                _sk.Initialize(false);
            }

            _sk.AnimationState.SetAnimation(0, _tapFeedbackAnim, loop: false);
            PlayIdle(append: true);
        }

        public override void PlayPreview()
        {
            if (_sk == null) return;

            HelpPanelSetup();
            Show();
            if (_sk.AnimationState == null)
            {
                _sk.Initialize(false);
            }

            _sk.AnimationState.SetAnimation(0, _previewAnim, loop: true);
        }

        public override void PlayVisitorPreview()
        {
            PlayPreview();
        }

        protected override void OnAppearAfterDelayAfterBaseCall()
        {
            if (_sk == null)
                return;

            string animName;
            if (_appearAnim1.IsNullOrEmpty())
            {
                animName = _appearAnim;
            }
            else
            {
                animName = Random.value <= _appear1Probability ? _appearAnim1 : _appearAnim;
            }

            _sk.AnimationState.SetAnimation(0, animName, loop: false);
            PlayIdle(append: true);
            AudioProxy.PlaySound(_appearSoundUid);
        }

        protected override void OnDisable()
        {
            if (_sk != null && _sk.AnimationState != null)
            {
                _sk.AnimationState.Start -= OnAnimStartCallback;
                _sk.AnimationState.Complete -= OnAnimEndCallback;
            }

            if (_currentOnDestroyCallback != null)
            {
                _currentOnDestroyCallback.Invoke();
                _currentOnDestroyCallback = null;
            }

            _isCurrentlyDestroying = false;
            _destroyAnimsCountBeforeEnd = 0;

            Hide();
            ResetToDefault();
        }

        private void OnAnimStartCallback(TrackEntry entry)
        {
            var animName = entry.Animation.Name;

            if (animName == _sleepLoopAnim)
            {
                AudioProxy.PlaySound(_sleepSoundUid);
            }

            if (animName == _destroyAnim)
            {
                AudioProxy.PlaySound(Match3SoundIds.ToadDestroy);
            }
        }

        /// <summary>
        /// Current animation end callback.
        /// </summary>
        /// <remarks>
        /// Handles completion of animation and starting of next. Next clip selection logic is specific for this tile.
        /// For example, sleep animation may be 1 or more consequent clips that start randomly after idle clip,
        /// and also idle clip has 2 variations, and the second variant may be triggered also randomly.
        /// </remarks>
        private void OnAnimEndCallback(TrackEntry entry)
        {
            var animName = entry.Animation.Name;

            if (_isCurrentlyDestroying)
            {
                _destroyAnimsCountBeforeEnd--;
                if (_destroyAnimsCountBeforeEnd <= 0)
                {
                    _isCurrentlyDestroying = false;
                    _currentOnDestroyCallback?.Invoke();
                    _currentOnDestroyCallback = null;
                    return;
                }

                // If destroy animation in progress then next clip is already added to the spine sequence
                // and this method can just end withiout invoking next clip.
                return;
            }

            if (animName == _idle0Anim || animName == _idle1Anim || animName == _sleepLoopAnim || animName == _sleepEndAnim)
            {
                if (_currentSleepCount > 0)
                {
                    _currentSleepCount--;
                    if (_currentSleepCount == 0)
                    {
                        _sk.AnimationState.SetAnimation(0, _sleepEndAnim, loop: false);
                    }
                    else
                    {
                        _sk.AnimationState.SetAnimation(0, _sleepLoopAnim, loop: false);
                    }
                }
                else
                {
                    if (Random.value <= _sleepProbability)
                    {
                        _currentSleepCount = Random.Range(_sleepCountMin, _sleepCountMax);
                        _sk.AnimationState.SetAnimation(0, _sleepEnterAnim, loop: false);
                        _sk.AnimationState.AddAnimation(0, _sleepLoopAnim, loop: false, delay: 0);
                        AudioProxy.PlaySound(_sleepSoundUid);
                    }
                    else
                    {
                        var idleName = Random.value <= _idle1Probability ? _idle1Anim : _idle0Anim;
                        _sk.AnimationState.SetAnimation(0, idleName, loop: false);
                    }
                }
            }
            else if (animName == _previewAnim)
            {
            }
            else if (entry.Next == null)
            {
                BDebug.Log(LogCat.Match3, "Unhandled animation state after clip: '" + animName + "'");
                // If this happens and current spine doesn't already have next clip to play, then it will freeze on screen.
                // This should never happen, but if it's occurred (due to some refactor, for example)
                // then it's not critical and it can be unfreezed by match or destroy events.
            }
        }

        /// <summary>
        /// Play destroy animation after which callback will be invoked.
        /// </summary>
        public void PlayDestroy(Action onDone)
        {
            _isCurrentlyDestroying = true;
            _toadTileBGAnimator.SetTrigger(DestroyTileBg);
            if (_destroyAnim.IsNullOrEmpty())
            {
                _sk.AnimationState.SetAnimation(0, _matchAnim, loop: false);
                _destroyAnimsCountBeforeEnd = 1;
            }
            else
            {
                _sk.AnimationState.SetAnimation(0, _matchAnim, loop: false);
                _sk.AnimationState.AddAnimation(0, _destroyAnim, loop: false, delay: 0);
                _destroyAnimsCountBeforeEnd = 2;
            }
            StartCoroutine(AutoReleaseOnDestroy(onDone));
        }

        private IEnumerator AutoReleaseOnDestroy(Action callback)
        {
            yield return WaitCache.Seconds(_destroyAnimationFailsafeTimeout);
            _isCurrentlyDestroying = false;
            callback();
        }

        protected override void Hide()
        {
            base.Hide();
            if (_sk == null) return;
            _sk.gameObject.SetActive(false);
        }

        public override void Show()
        {
            base.Show();
            if (_sk == null) return;
            _sk.gameObject.SetActive(true);
        }
    }
}