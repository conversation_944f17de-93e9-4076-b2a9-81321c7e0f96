using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class WatermelonLayerView : SheetTileLayerViewBase
    {
        private WatermelonLayerRenderer _renderer;
        private int _prevLevel = -1;

        public WatermelonLayerView(ITileLayer layer) : base(layer)
        {}

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.AdjacentHp);
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<WatermelonLayerRenderer>();
            if (_renderer == null)
            {
                UnityEngine.Debug.LogError($"Missing component {typeof(WatermelonLayerRenderer).Name} on tile prefab: {instance.name}", instance);
            }
            else
            {
                _renderer.ResetView();
            }
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);

            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                    AudioProxy.PlaySound(Match3SoundIds.WaterMelonDestroy);
                    FxRenderer.SpawnSingleAnimatorEffect(coords, FxType.WatermelonDestroy, 6f);
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            _renderer.UpdateView(newLevel);
            if (newLevel < _prevLevel)
            {
                ShakeWithSiblings();
                if (coords.HasValue)
                    switch (newLevel)
                    {
                        case 2:
                        {
                            AudioProxy.PlaySound(Match3SoundIds.WaterMelonHitOne);
                            FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.WatermelonThirdLayerRemove, 6f);
                            break;
                        }
                        case 1:
                        {
                            AudioProxy.PlaySound(Match3SoundIds.WaterMelonHitTwo);
                            FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.WatermelonSecondLayerRemove, 6f);
                            break;
                        }
                    }
            }
            _prevLevel = newLevel;
        }
    }
}
