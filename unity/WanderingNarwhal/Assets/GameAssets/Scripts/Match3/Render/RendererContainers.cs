using System;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Core;
using BBB.DI;
using BBB.RaceEvents;
using BBB.UI;
using BBB.UI.Level;
using BebopBee.UnityEngineExtensions;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;
using Object = UnityEngine.Object;

namespace BBB.Match3.Renderer
{
    public class RendererContainers : IContextInitializable, IContextReleasable
    {
        private const int RectTransformHierarchyCapacity = 1500;

        private TilesResources _tilesResources;
        private IMatch3SharedResourceProvider _match3ResourceProvider;
        private IConfig _config;
        private Match3ResourceHelper _match3ResourceHelper;
        private SpawnerSettingsManager _spawnerSettingsManager;
        private OverlaysRenderer _overlaysRenderer;
        private Transform _mainContainer;
        private RectTransform _tilesContainer;
        private RectTransform _formingBoostsContainer;

        private Dictionary<FxType, int> _initialFxTypesCountMap;
        private Dictionary<TileLayerState, int> _initialTileStateCountMap;
        private Dictionary<TileLayerState, GoPool> _layersPool;
        private Dictionary<FxType, GoPool> _fxPool;
        private readonly Dictionary<ContainerType, Transform> _containersDict = new();

        public RectTransform FloorsOverlay { get; private set; }
        public RectTransform TilesOverlay { get; private set; }
        public GoPool TilesViewPool { get; private set; }
        public RectTransform FloorsContainer { get; private set; }

        public void InitializeByContext(IContext context)
        {
            if (_containersDict.Count == 0)
            {
                _overlaysRenderer = context.Resolve<OverlaysRenderer>();
                foreach (var enumValue in new[] { ContainerType.OverlayFx0, ContainerType.OverlayFx1, ContainerType.OverlayFx2 })
                {
                    _containersDict.Add(enumValue, _overlaysRenderer.GetOverlayLayer(enumValue));
                }
            }

            _config = context.Resolve<IConfig>();
            _spawnerSettingsManager = context.Resolve<SpawnerSettingsManager>();
            _tilesResources = context.Resolve<TilesResources>();
            _match3ResourceProvider = context.Resolve<IMatch3SharedResourceProvider>();
            _match3ResourceHelper = context.Resolve<Match3ResourceHelper>();
            _mainContainer = context.Resolve<IGridController>().Transform;
            FloorsContainer = InitAndGetContainer(ContainerType.Floors);
            FloorsOverlay = InitAndGetContainer(ContainerType.FloorsOverlay);
            TilesOverlay = InitAndGetContainer(ContainerType.TilesOverlay);
            _tilesContainer = InitAndGetContainer(ContainerType.Tiles);
            _formingBoostsContainer = InitAndGetContainer(ContainerType.FormingBoosts);
        }

        private static void LeftBottomAlignment(RectTransform rectTransform, Vector2 size, Vector2 offset)
        {
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.zero;
            rectTransform.pivot = Vector2.zero;
            rectTransform.offsetMin = offset;
            rectTransform.sizeDelta = size;
        }

        private RectTransform InitAndGetContainer(ContainerType containerType)
        {
            if (_containersDict.TryGetValue(containerType, out var value))
                return value as RectTransform;

            var rectTransform = _mainContainer.Find(containerType.ToString()) as RectTransform;
            _containersDict[containerType] = rectTransform;

            if (rectTransform == null) return null;
            rectTransform.hierarchyCapacity = RectTransformHierarchyCapacity;
            return rectTransform;
        }

        private bool ShouldBeCleaned(TileLayerState layerState)
        {
            if (_initialTileStateCountMap.TryGetValue(layerState, out int count))
                return count == 0;

            return true;
        }

        private bool ShouldBeCleaned(FxType fxType)
        {
            return false;
            // if (_initialFxTypesCountMap.TryGetValue(fxType, out int count))
            //     return count == 0;
            //
            // return true;
        }

        public void ForceReleaseAllSpawned(bool cleanupPools = false)
        {
            if (_layersPool != null)
            {
                foreach (var kvp in _layersPool)
                {
                    var goPool = kvp.Value;
                    goPool.ForceReleaseAll();
                    if (cleanupPools)
                    {
                        goPool.Cleanup();
                    }
                }
            }
            
            _layersPool?.Clear();
            _layersPool = null;

            if (_fxPool != null)
            {
                foreach (var kvp in _fxPool)
                {
                    var goPool = kvp.Value;
                    goPool.ForceReleaseAll();
                    if (cleanupPools)
                    {
                        goPool.Cleanup();
                    }
                }
            }
            
            _fxPool?.Clear();
            _fxPool = null;
            
            if (TilesViewPool != null) 
            { 
                TilesViewPool.ForceReleaseAll(); 
                TilesViewPool.Cleanup(); 
                TilesViewPool = null; 
            }
        }

        public void ReparantTo(TileView view, ContainerType containerType)
        {
            if (_containersDict.TryGetValue(containerType, out var container))
            {
                view.transform.SetParent(container);
            }
            else
            {
                BDebug.LogError(LogCat.General, $"Container {containerType} not found");
            }
        }

        public void PrewarmEventFx(FxType fxType, int count)
        {
            _fxPool[fxType].Prewarm(count);
        }

        public void SetupContainers(ILevel level, LevelController levelController)
        {
            var grid = level.Grid;
            var gridSizeInTiles = grid.Size;
            var size = gridSizeInTiles.Multiply(_tilesResources.CellSize);

            LeftBottomAlignment(FloorsContainer, size, Vector2.zero);
            LeftBottomAlignment(FloorsOverlay, size, Vector2.zero);
            LeftBottomAlignment(TilesOverlay, size, Vector2.zero);
            LeftBottomAlignment(_tilesContainer, size, Vector2.zero);
            LeftBottomAlignment(_formingBoostsContainer, size, Vector2.zero);

            TilesViewPool ??= new GoPool(_tilesResources.TileContainerPrefab,
                _tilesContainer, grid.Cells.Count);

            _initialTileStateCountMap ??= CreateInitialLayerStateCountMap(grid.Cells.Count);
            _initialFxTypesCountMap ??= CreateInitialFxTypeCountMap();
            _overlaysRenderer.LoadOverlay(Match3ResKeys.BonusTimeOverlayName);

            var tileStateCountMap = new Dictionary<TileLayerState, int>(_initialTileStateCountMap);
            var fxTypeCountMap = new Dictionary<FxType, int>(_initialFxTypesCountMap);

            foreach (var cell in grid.Cells)
            {
                if (cell.IsAnyOf(CellState.BackOne))
                    fxTypeCountMap[FxType.BackgroundRemove]++;

                if (cell.IsAnyOf(CellState.BackDouble))
                    fxTypeCountMap[FxType.BackgroundDoubleRemove]++;

                if (cell.IsAnyOf(CellState.Petal))
                {
                    fxTypeCountMap[FxType.PetalRemove]++;
                    fxTypeCountMap.AddSafe(FxType.PetalAnticipation, 1);
                }

                if (cell.IsAnyOf(CellState.Ivy))
                    fxTypeCountMap.AddSafe(FxType.IvyDestroy, 1);

                if (ReferenceEquals(cell.Tile, null))
                    continue;

                var assetLayerState = cell.Tile.Speciality.ToLayerState();
                var stateLayerState = cell.Tile.State.ToLayerState();

                var count = tileStateCountMap.GetSafe(assetLayerState);
                if (count < grid.Cells.Count)
                    tileStateCountMap.AddSafe(assetLayerState, 1);

                count = tileStateCountMap.GetSafe(stateLayerState);
                if (count < grid.Cells.Count)
                    tileStateCountMap.AddSafe(stateLayerState, 1);

                ApplyTileToFxTypeCountMap(cell.Tile, fxTypeCountMap);

                fxTypeCountMap.AddSafe(FxType.TileRemove, 1);
                fxTypeCountMap.AddSafe(FxType.TileStar, 1);
                fxTypeCountMap.AddSafe(FxType.ScoreText, 1);
            }

            const int amountOfFxInGrid = 5;
            foreach (var fxType in GetSingleInstanceFxTypes())
            {
                fxTypeCountMap.AddSafe(fxType, amountOfFxInGrid);
            }

            var tileResourceInfos = _match3ResourceHelper.GetTileResourceInfos(level, _spawnerSettingsManager.SpawnerSettings);

            _layersPool ??= new Dictionary<TileLayerState, GoPool>(tileResourceInfos.Count);

            foreach (var tileResourceInfo in tileResourceInfos)
            {
                var state = tileResourceInfo.State;

                if (_layersPool.ContainsKey(state))
                    continue;

                var order = tileResourceInfo.DefaultOrder;
                var prefabName = tileResourceInfo.PrefabName;
                var prefab = _match3ResourceProvider.GetPrefab(prefabName);

                if (prefab == null)
                {
                    BDebug.LogError(LogCat.Match3, $"Prefab for tile state '{prefabName}' is null");
                    continue;
                }

                _layersPool[state] = new GoPool(prefab, _tilesContainer, tileStateCountMap.GetSafe(state), OnCreate);
                continue;

                void OnCreate(GameObject createdInstance)
                {
                    ApplySorting(order, createdInstance);
                }
            }

            _fxPool ??= new Dictionary<FxType, GoPool>();

            foreach (var fxType in _match3ResourceHelper.GetUsedFxTypes(level, _config, _spawnerSettingsManager.SpawnerSettings))
            {
                if (_fxPool.TryGetValue(fxType, out var fxPool))
                {
                    if (ShouldBeCleaned(fxType))
                        fxPool.Cleanup();
                    if (fxTypeCountMap.TryGetValue(fxType, out var fxCount))
                        fxPool.Prewarm(fxCount);
                    continue;
                }

                var prefabName = fxType.ToPrefabName();
                var prefab = _match3ResourceProvider.GetPrefab(prefabName);
                if (prefab == null)
                {
                    BDebug.LogError(LogCat.Match3, $"Fx prefab is null: '{prefabName}'");
                    continue;
                }

                var containerType = fxType.GetContainerType();
                var rectTransform = InitAndGetContainer(containerType);
                var canvas = rectTransform.GetComponent<Canvas>();
                var containerSortingOrder = canvas.sortingOrder;
                if (!_containersDict.ContainsKey(containerType))
                {
                    LeftBottomAlignment(rectTransform, size, Vector2.zero);
                }

                var fxFound = fxTypeCountMap.TryGetValue(fxType, out var count);

                if (!fxFound)
                {
                    UnityEngine.Debug.LogWarning($"Fx {fxType} was not found in the count map");
                }

                _fxPool[fxType] = new GoPool(prefab, rectTransform, Math.Max(1, count), OnCreate);
                continue;

                void OnCreate(GameObject createdInstance)
                {
                    ApplySorting(containerSortingOrder, createdInstance);
                }
            }
        }

        private static IEnumerable<FxType> GetSingleInstanceFxTypes()
        {
            yield return FxType.LineBreaker;
            yield return FxType.DoubleBomb;
            yield return FxType.CollectEventTile;
            yield return FxType.DoFlyResource;
        }

        private void ApplyTileToFxTypeCountMap(Tile tile, Dictionary<FxType, int> map)
        {
            foreach (var fxType in tile.ToLayerState().ToFxTypes())
                map.AddSafe(fxType, 1);
        }

        private Dictionary<TileLayerState, int> CreateInitialLayerStateCountMap(int totalCount)
        {
            var tileStateCountMap = new Dictionary<TileLayerState, int>();

            tileStateCountMap[TileLayerState.Normal] = totalCount;
            tileStateCountMap[TileLayerState.HorizontalLb] = 5;
            tileStateCountMap[TileLayerState.VerticalLb] = 5;
            tileStateCountMap[TileLayerState.Bomb] = 5;
            tileStateCountMap[TileLayerState.Propeller] = 5;
            tileStateCountMap[TileLayerState.ColorBomb] = 3;
            tileStateCountMap[TileLayerState.Litter] = 1;
            tileStateCountMap[TileLayerState.Blinking] = 1;

            return tileStateCountMap;
        }

        private static Dictionary<FxType, int> CreateInitialFxTypeCountMap()
        {
            var result = new Dictionary<FxType, int>();

            result[FxType.TripleLineBreaker] = 1;
            result[FxType.Whirlpool] = 1;
            result[FxType.WhirlpoolSecondWave] = 1;
            result[FxType.DoubleLineBreaker] = 1;
            result[FxType.Match] = 5;
            result[FxType.WindTrail] = 1;
            result[FxType.Shovel] = 1;
            result[FxType.Balloon] = 1;
            result[FxType.LightningBolt] = 2;
            result[FxType.BombBombCombine] = 1;
            result[FxType.BombLinebreakerCombine] = 1;
            result[FxType.DiscoBallDiscoBallCombine] = 1;
            result[FxType.SuperDiscoBallSuperDiscoBallCombine] = 1;
            result[FxType.BoostRevealExplosion] = 3;
            result[FxType.CircleWave] = 1;
            result[FxType.Comet] = 5;
            result[FxType.CometExplosion] = 5;
            result[FxType.CometStart] = 5;
            result[FxType.DoFlyStar] = 5;
            result[FxType.Dim] = 1;
            result[FxType.StarExplosion] = 5;
            result[FxType.LightningHit] = 10;
            result[FxType.LightningRainPiece] = 10;
            result[FxType.LightningBoltPiece] = 10;
            result[FxType.BackgroundRemove] = 0;
            result[FxType.BackgroundDoubleRemove] = 0;
            result[FxType.PetalRemove] = 0;
            result[FxType.ShakeOverlayEffect] = 20;
            result[FxType.TileRemove] = 20;
            result[FxType.TileStar] = 20;
            result[FxType.ScoreText] = 20;
            result[FxType.LitterDestroy] = 10;
            result[FxType.SmallCrossExplosion] = 5;
            result[FxType.PropellerFlight] = 5;
            result[FxType.PropellerFlightShadow] = 5;
            result[FxType.PropellerDestroy] = 5;
            result[FxType.Swap] = 1;
            result[FxType.DiscoRushCollect] = 0;
            result[FxType.VerticalBooster] = 1;
            result[FxType.HorizontalBooster] = 1;
            result[FxType.BoosterTrail] = 1;
            return result;
        }

        private void ApplySorting(int sortingValue, GameObject instance)
        {
            var canvas = instance.GetComponent<Canvas>();
            if (canvas != null)
            {
                canvas.overrideSorting = true;
                canvas.sortingOrder += sortingValue;
            }

            foreach (var child in instance.transform.GetChildrenRecursevely())
            {
                var spriteRenderer = child.GetComponent<UnityEngine.Renderer>();
                if (spriteRenderer != null)
                    spriteRenderer.sortingOrder += sortingValue;
            }
        }


        public void ReleaseByContext(IContext context)
        {
            ForceReleaseAllSpawned(true);
            foreach (var child in _mainContainer.GetComponentsInChildren<Transform>())
            {
                Object.Destroy(child.gameObject);
            }
        }

        public Transform GetContainer(ContainerType containerType)
        {
            Transform result;
            if (_containersDict.TryGetValue(containerType, out result))
                return result;

            throw new Exception(string.Format("Container {0} not found", containerType));
        }

        public GoPool GetLayersPool(TileLayerState state)
        {
            try
            {
                return _layersPool[state];
            }
            catch
            {
                UnityEngine.Debug.LogError($"{state} not found in _layersPool");
                throw;
            }
        }

        public GoPool GetFxPool(FxType type)
        {
            return _fxPool[type];
        }

        public TObject SpawnFx<TObject>(FxType type) where TObject : Component
        {
            try
            {
                var obj = _fxPool[type].Spawn<TObject>();
                return obj;
            }
            catch (KeyNotFoundException e)
            {
                UnityEngine.Debug.LogError($"FxType key {type} not found: " + e.Message);
            }

            return null;
        }

        public GameObject SpawnFx(FxType type)
        {
            try
            {
                var obj = _fxPool[type].Spawn();
                //YA: Make obj transform scale 0 so that there is no glitch before the scale curve gets applied 
                obj.transform.localScale = Vector3.zero;
                return obj;
            }
            catch (KeyNotFoundException e)
            {
                UnityEngine.Debug.LogError($"FxType key {type} not found: " + e.Message);
            }

            return null;
        }
    }
}