using System;
using System.Collections.Generic;
using BBB.Audio;
using BBB.Core;
using BBB.DI;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.MMVibrations;
using BBB.MMVibrations.Plugins;
using BBB.UI;
using BBB.UI.Level;
using BebopBee.Core;
using BebopBee.Core.Audio;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;
using Random = UnityEngine.Random;

namespace BBB.Match3.Renderer
{
    public sealed partial class FxRenderer : RendererBase, ICoroutineExecutor
    {
        private static readonly Vector2 CenterOffset = new(0.5f, 0.5f);
        private IGridController _gridController;
        private TileController _tileController;
        private GameController _gameController;
        private RendererContainers _rendererContainers;
        private TileResourceSelector _tileResourceSelector;
        private TurnsPanelController _turnsPanel;
        private IVibrationsWrapper _vibrations;
        private M3Settings _settings;
        private TilesResources _tilesResources;
        private ICellController _cellController;

        private readonly HashSet<Tweener> _tweenersSet = new();
        private readonly HashSet<GameObject> _releaseableGoSet = new();

        public override void InitializeByContext(IContext context)
        {
            base.InitializeByContext(context);

            _gameController = context.Resolve<GameController>();
            _turnsPanel = context.Resolve<TurnsPanelController>();
            _tilesResources = context.Resolve<TilesResources>();
            _tileResourceSelector = context.Resolve<TileResourceSelector>();
            _vibrations = context.Resolve<IVibrationsWrapper>();
            _gridController = context.Resolve<IGridController>();
            _cellController = context.Resolve<ICellController>();

            _tileController = context.Resolve<TileController>();
            _rendererContainers = context.Resolve<RendererContainers>();
            _settings = context.Resolve<M3Settings>();
        }

        public void SpawnRemoveTile(Coords coords, TileKinds kind)
        {
            var ctrl = _rendererContainers.SpawnFx<TileDestroyEffect>(FxType.TileRemove);
            LocateEffectInLocalSpace(ctrl, 3f, coords.ToUnityVector2());
            var tileKindData = TilesResourcesRef.GetKindData(kind);
            ctrl.SetExplosionPieceMaterial(tileKindData.PieceMaterial);
        }

        public void SpawnRemoveTileByBombBombEffect(Coords coords)
        {
            var ctrl = _rendererContainers.SpawnFx<SingleAnimatorEffect>(FxType.DestroyByBombBombCombo);
            LocateEffectInLocalSpace(ctrl, 1f, coords.ToUnityVector2());
        }

        public GameObject SpawnTripleLineBreaker(Coords coords, M3Settings m3Settings, TileSpeciality lineBreakerSpeciality)
        {
            var ctrl = _rendererContainers.SpawnFx<TripleLineBreakerFX>(FxType.TripleLineBreaker);
            LocateEffectInLocalSpace(ctrl, 2.2f, coords.ToUnityVector2());
            ctrl.Init(m3Settings, lineBreakerSpeciality);
            return ctrl.gameObject;
        }

        public void SpawnWindLeaves(TileView tileView)
        {
            var ctrl = _rendererContainers.SpawnFx<WindEffect>(FxType.WindTrail);
            var go = ctrl.gameObject;
            SetAutoreleaseComponentTimer(go, 4f);
            ctrl.Init(tileView.LocalAnchor);
        }

        public void SpawnShovel(Coords coords)
        {
            var ctrl = _rendererContainers.SpawnFx<SingleAnimatorEffect>(FxType.Shovel);
            LocateEffectInLocalSpace(ctrl, 1.5f, coords.ToUnityVector2());
        }

        public void SpawnBalloon(List<Coords> coordsList, float totalTime)
        {
            var ctrl = _rendererContainers.SpawnFx<BalloonEffect>(FxType.Balloon);
            var go = ctrl.gameObject;
            SetAutoreleaseComponentTimer(go, totalTime + 0.5f);
            ctrl.Play(coordsList, totalTime, CellSize);
        }

        public void SpawnLightningFromSky(Coords coords, TileKinds kind, float time, Action callback)
        {
            var ctrl = _rendererContainers.SpawnFx<LightningBoltEffect>(FxType.LightningBolt);
            var go = ctrl.gameObject;
            SetAutoreleaseComponentTimer(go, time + 0.16f);
            var from = new Coords(Random.Range(-1, _gameController.Grid.Width), 15);
            var piecePool = _rendererContainers.GetFxPool(FxType.LightningRainPiece);

            Vector3 CoordsTransform(Coords inputCoords)
            {
                var gridLocalPos = _gridController.ToDisplacedLocalPosition(inputCoords);
                return gridLocalPos;
            }

            ctrl.PlayAsBrokenLine(time * 0.5f, piecePool, from, coords, CoordsTransform, Color.white, Color.white);

            Tweener tween1 = null;
            Tweener tween2 = null;

            void FullTimeAction(long _)
            {
                _tweenersSet.Remove(tween1);
                callback();
            }

            void HalfTimeAction(long _)
            {
                var lightningHit = _rendererContainers.SpawnFx<SingleAnimatorEffect>(FxType.LightningHit);
                AudioProxy.PlaySound(Match3SoundIds.LightningHit);
                _vibrations.PlayHaptic(ImpactPreset.MediumImpact);
                LocateEffectInWorldSpace(lightningHit, 1f, _gridController.ToWorldPosition(coords.ToUnityVector2()));

                _tweenersSet.Remove(tween2);
            }

            tween1 = Rx.Invoke(time * 0.5f, FullTimeAction);
            tween2 = Rx.Invoke(time * 0.5f, HalfTimeAction);

            _tweenersSet.Add(tween1);
            _tweenersSet.Add(tween2);
        }

        /// <summary>
        /// Spawn lightning segments effects in chain. Each segment starts where previous segment ends.
        /// Spawning is happening with delay for each segment. Segment lifetime is calculated from total duration divided by segments count.
        /// </summary>
        public void SpawnLightningBoltsChain(Cell colorBombCell, List<KeyValuePair<TileKinds, List<CellWithTileKinds>>> coordsList,
            float duration, float lightningsWidth, Action<Coords> lightningCallback)
        {
            AudioProxy.PlaySound(Match3SoundIds.LightningBoltStart);

            var stageDelay = 0f;

            var useSdbOverlayColorDefault = false;
            var regularOverlayColor = _settings.DiscoBallRegularTilesOverlayColor;
            var sdbOverlayColor = _settings.SuperDiscoBallRegularTilesOverlayColor;

            foreach (var kvp in coordsList)
            {
                var useSdbOverlayColor = useSdbOverlayColorDefault;
                var overlayColor = useSdbOverlayColor ? sdbOverlayColor : regularOverlayColor;
                
                Rx.Invoke(stageDelay, _ =>
                {
                    foreach (var item in kvp.Value)
                    {
                        var tileKind = item.TileKinds;
                        var coords = item.Cell.Coords;

                        if (useSdbOverlayColor)
                        {
                            SpawnWithBaseColor(sdbOverlayColor);
                        }
                        else if (!tileKind.IsColored())
                        {
                            SpawnWithBaseColor(Color.white);
                        }
                        else
                        { 
                            TilesResourcesRef
                                .GetAsync(tileKind)
                                .ContinueWith(tileData =>
                                {
                                    var baseColor = tileData.Color;
                                    SpawnWithBaseColor(baseColor);
                                }).Forget();
                        }

                        continue;

                        void SpawnWithBaseColor(Color baseColor)
                        {
                            AudioProxy.PlaySound(Match3SoundIds.LightningBoltJump);
                            _vibrations.PlayHaptic(ImpactPreset.MediumImpact);
                            var ctrl = _rendererContainers.SpawnFx<LightningBoltEffect>(FxType.LightningBolt);
                            var go   = ctrl.gameObject;
                            SetAutoreleaseComponentTimer(go, duration);
                            var piecePool = _rendererContainers.GetFxPool(FxType.LightningBoltPiece);
                            ctrl.PlayAsStraightLine(piecePool, colorBombCell.Coords, coords, CellSize, baseColor, overlayColor, lightningsWidth);
                            lightningCallback(coords);
                        }
                    }
                });
                
                stageDelay += _settings.RegularTilesStageDelayWhileUsingSuperDiscoBall;
                useSdbOverlayColorDefault = true;
            }
        }

        public void SpawnLightningBoltsFromFirstPoint(Coords sourceCoord, List<KeyValuePair<TileKinds, List<CellWithTileKinds>>> coordsList,
            float spawnDuration, float lightningsWidth, Action<Coords> lightningCallback)
        {
            AudioProxy.PlaySound(Match3SoundIds.LightningBoltStart);
            AudioProxy.PlaySound(Match3SoundIds.LightningBoltJump);
            _vibrations.PlayHaptic(ImpactPreset.MediumImpact);

            var stageDelay = 0f;

            var baseColorDefault = Color.white;
            var overlayColorDefault = _settings.DiscoBallBoostersTilesOverlayColor;

            foreach (var kvp in coordsList)
            {
                var baseColor = baseColorDefault;
                var overlayColor = overlayColorDefault;
                Rx.Invoke(stageDelay, _ =>
                {
                    foreach (var item in kvp.Value)
                    {
                        var toCoords = item.Cell.Coords;

                        var ctrl = _rendererContainers.SpawnFx<LightningBoltEffect>(FxType.LightningBolt);
                        var go = ctrl.gameObject;
                        SetAutoreleaseComponentTimer(go, spawnDuration);
                        var piecePool = _rendererContainers.GetFxPool(FxType.LightningBoltPiece);
                        ctrl.PlayAsStraightLine(piecePool, sourceCoord, toCoords, CellSize, baseColor, overlayColor, lightningsWidth);
                        lightningCallback(toCoords);
                    }
                });
                stageDelay += _settings.BoostersStageDelayWhileUsingSuperDiscoBall;
                baseColorDefault = overlayColorDefault = _settings.SuperDiscoBallBoostersTilesOverlayColor;
            }
        }

        public void SpawnPropellerLinebreakerImpact(Grid grid, Coords coords)
        {
            _vibrations.PlayHaptic(ImpactPreset.HeavyImpact);
            var ctrl = _rendererContainers.SpawnFx<BombEffect>(FxType.DoubleBomb);
            LocateEffectInLocalSpace(ctrl, 1f, coords.ToUnityVector2() + CenterOffset);
           
            TilesResourcesRef
                .GetAsync(TileKinds.Red)
                .ContinueWith(tileData =>
                {
                    ctrl.Init(grid, _tileController, coords, M3Constants.DefaultBombRadius, tileData.Color);
                    _gridController.DoTweenShakeBoard(coords.GetHashCode(), shakeSettings: ShakeSettingsType.Bomb);
                }).Forget();
        }

        public void SpawnBomb(Grid grid, Coords coords, int radius, TileKinds tileKind, bool withShake, float time = 1)
        {
            _vibrations.PlayHaptic(ImpactPreset.HeavyImpact);
            var ctrl = _rendererContainers.SpawnFx<BombEffect>(FxType.DoubleBomb);
            LocateEffectInLocalSpace(ctrl, time, coords.ToUnityVector2() + CenterOffset);
            
            TilesResourcesRef
                .GetAsync(tileKind)
                .ContinueWith(tileData =>
                {
                    ctrl.Init(grid, _tileController, coords, radius, tileData.Color);

                    if (withShake)
                    {
                        _gridController.DoTweenShakeBoard(coords.GetHashCode(), shakeSettings: ShakeSettingsType.Bomb);
                    }
                });
        }

        public void SpawnCircleWave(Vector2 startCoord)
        {
            var ctrl = _rendererContainers.SpawnFx<SingleAnimatorEffect>(FxType.CircleWave);
            LocateEffectInLocalSpace(ctrl, 5f, startCoord);
        }

        public void SpawnStars(Grid grid, Vector2 startCoord, float time)
        {
            var anchorCoord = grid.Cells[^1].Coords;
            var maxDistance = anchorCoord.DistanceFrom(startCoord);

            foreach (var cell in grid.Cells)
            {
                var coord = cell.Coords;
                var distance = coord.DistanceFrom(startCoord);

                var startTime = time * distance / maxDistance;
                var vecCoord = coord.ToUnityVector2();

                void SpawnStarsAction(long _)
                {
                    var ctrl = _rendererContainers.SpawnFx<SingleAnimatorEffect>(FxType.TileStar);
                    LocateEffectInLocalSpace(ctrl, 0.5f, vecCoord);
                }

                Rx.Invoke(startTime, SpawnStarsAction);
            }
        }

        public void SpawnWhirlpool(Coords secondCoords, FxType fxType)
        {
            var centerUnityCoords = secondCoords.ToUnityVector2();
            var ctrl = _rendererContainers.SpawnFx<WhirlpoolEffect>(fxType);
            LocateEffectInLocalSpace(ctrl, -1f, centerUnityCoords);
        }

        public void SpawnMatch(Grid grid, List<Match> matches)
        {
            var ctrl = _rendererContainers.SpawnFx<MatchEffect>(FxType.Match);
            var go = ctrl.gameObject;
            SetAutoreleaseComponentTimer(go, 0.5f);
            ctrl.Init(grid, _tileController, matches);
        }

        public void SpawnSingleAnimatorEffectWithHapticFeedback(Coords coords, FxType fxType, float releaseTime = 1f, ImpactPreset impactPreset = ImpactPreset.HeavyImpact)
        {
            _vibrations.PlayHaptic(impactPreset);
            var ctrl = _rendererContainers.SpawnFx<SingleAnimatorEffect>(fxType);
            LocateEffectInLocalSpace(ctrl, releaseTime, coords.ToUnityVector2());
        }

        public void SpawnSingleAnimatorEffect(Coords coords, FxType fxType, float releaseTime = 1f)
        {
            var ctrl = _rendererContainers.SpawnFx<SingleAnimatorEffect>(fxType);
            LocateEffectInLocalSpace(ctrl, releaseTime, coords.ToUnityVector2());
        }

        public void SpawnSingleAnimatorEffect(Vector2 coordsPos, FxType fxType, float releaseTime = 1f)
        {
            var ctrl = _rendererContainers.SpawnFx<SingleAnimatorEffect>(fxType);
            LocateEffectInLocalSpace(ctrl, releaseTime, coordsPos);
        }

        public void SpawnSingleAnimatorEffect(Coords coords, FxType fxType, TileKinds kind, float releaseTime = 1f)
        {
            var ctrl = _rendererContainers.SpawnFx<SingleAnimatorEffect>(fxType);
            LocateEffectInLocalSpace(ctrl, releaseTime, coords.ToUnityVector2());
            SetupEffectColorByKind(ctrl.gameObject, kind);
        }

        public void SpawnSingleAnimatorEffectWithCustomParameters(Coords coords, FxType fxType, FxOptionalParameters prm, float releaseTime = 1f)
        {
            var ctrl = _rendererContainers.SpawnFx<SingleAnimatorEffect>(fxType);
            ctrl.ApplyParameters(prm);
            LocateEffectInLocalSpace(ctrl, releaseTime, coords.ToUnityVector2());
        }

        public void SpawnConfigurableEffectWithCustomParameters(Coords coords, FxType fxType, FxOptionalParameters prm, float releaseTime = 1f)
        {
            var ctrl = _rendererContainers.SpawnFx<ConfigurableEffectBase>(fxType);
            ctrl.ApplyParameters(prm);
            LocateEffectInLocalSpace(ctrl, releaseTime, coords.ToUnityVector2());
        }

        public void SpawnConfigurableEffectWithCustomParameters(Vector2 coordsPos, FxType fxType, FxOptionalParameters prm, float releaseTime = 1f)
        {
            var ctrl = _rendererContainers.SpawnFx<ConfigurableEffectBase>(fxType);
            ctrl.ApplyParameters(prm);
            LocateEffectInLocalSpace(ctrl, releaseTime, coordsPos);
        }

        private void LocateEffectInWorldSpace(MonoBehaviour ctrl, float releaseTime, Vector3 worldSpacePos)
        {
            var go = ctrl.gameObject;
            SetAutoreleaseComponentTimer(go, releaseTime);
            var rectTransform = go.transform as RectTransform;
            rectTransform.pivot = Vector2.zero;
            rectTransform.position = worldSpacePos;
            rectTransform.sizeDelta = CellSize;
        }

        private void LocateEffectInLocalSpace(MonoBehaviour ctrl, float releaseTime, Vector2 vec2)
        {
            var go = ctrl.gameObject;
            LocateEffectInLocalSpace(go, releaseTime, vec2);
        }

        private void LocateEffectInLocalSpace(GameObject go, float releaseTime, Vector2 vec2)
        {
            SetAutoreleaseComponentTimer(go, releaseTime);
            var rectTransform = go.transform as RectTransform;
            rectTransform.pivot = Vector2.zero;
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.zero;
            rectTransform.offsetMin = vec2.Multiply(CellSize);
            rectTransform.sizeDelta = CellSize;
        }

        public void ReduceOneTurn()
        {
            _turnsPanel.ReduceTextNumber();
        }

        public void SpawnComet(Coords targetCoords, float timeForComet)
        {
            ReduceOneTurn();
            var ctrl = _rendererContainers.SpawnFx<CometEffect>(FxType.Comet);
            var go = ctrl.gameObject;
            SetAutoreleaseComponentTimer(go, timeForComet + 0.5f);
            var localTargetPosition = _gridController.ToLocalPosition(targetCoords);
            var container = _rendererContainers.GetContainer(ContainerType.Tiles);
            var worldTargetPosition = container.TransformPoint(localTargetPosition);
            var turnsTextPosition = _turnsPanel.TextTargetTransform.position;
            ctrl.Play(turnsTextPosition, worldTargetPosition, timeForComet);
            AudioProxy.PlaySound(Match3SoundIds.CometStart);
            var cometStart = _rendererContainers.SpawnFx<SingleAnimatorEffect>(FxType.CometStart);
            SetAutoreleaseComponentTimer(cometStart.gameObject, 1f);
            var cometStartTransform = cometStart.gameObject.transform;
            cometStartTransform.position = turnsTextPosition;
            var locPos = cometStartTransform.localPosition;
            locPos.z = 0f;
            cometStartTransform.localPosition = locPos;

            void CometExplosionAction(long _)
            {
                AudioProxy.PlaySound(Match3SoundIds.CometEnd);
                _vibrations.PlayHaptic(ImpactPreset.MediumImpact);
                var cometExplosion = _rendererContainers.SpawnFx<SingleAnimatorEffect>(FxType.CometExplosion);
                LocateEffectInLocalSpace(cometExplosion, 1f, targetCoords.ToUnityVector2());
            }

            Rx.Invoke(timeForComet, CometExplosionAction);
        }

        public void SpawnStarExplosion(Vector3 position)
        {
            var starExplosion = _rendererContainers.SpawnFx<SingleAnimatorEffect>(FxType.StarExplosion);
            SetAutoreleaseComponentTimer(starExplosion.gameObject, 1f);
            starExplosion.transform.position = position;
        }

        public void SpawnRainCloud()
        {
            var rainController = _rendererContainers.SpawnFx<RainEffect>(FxType.Rain);
            rainController.Play();
            SetAutoreleaseComponentTimer(rainController.gameObject, 15f);

            var dimController = _rendererContainers.SpawnFx<DimEverythingEffect>(FxType.Dim);
            dimController.Play();
            SetAutoreleaseComponentTimer(dimController.gameObject, 15f);
        }

        public void SpawnLitterDestroy(Coords coords)
        {
            var ctrl = _rendererContainers.SpawnFx<SingleAnimatorEffect>(FxType.LitterDestroy);
            SetAutoreleaseComponentTimer(ctrl.gameObject, 2f);
            ctrl.transform.localPosition = _gridController.ToLocalPosition(coords);
            var litterRenderer = ctrl.GetComponent<LitterRenderer>();
            litterRenderer.Setup(_tileResourceSelector.SelectedLitterVariant);
        }

        public void SpawnBoostRevealExplosion(Coords coords)
        {
            var ctrl = _rendererContainers.SpawnFx<ActivePoolItem>(FxType.BoostRevealExplosion);
            LocateEffectInLocalSpace(ctrl, 3f, coords.ToUnityVector2());
        }

        public void SpawnBombBombCombine(Coords fromCoord, Coords toCoord, float time)
        {
            AudioProxy.PlaySound(Match3SoundIds.BombBombCombine);

            var ctrl = _rendererContainers.SpawnFx<BombBombCombineEffect>(FxType.BombBombCombine);
            Rx.Invoke(time, _ => { _gridController.DoTweenShakeBoard(fromCoord.GetHashCode(), shakeSettings: ShakeSettingsType.BombBomb); });

            LocateEffectInLocalSpace(ctrl, time, toCoord.ToUnityVector2());
        }

        public void SpawnBoltBoosterCombine(M3Settings m3Settings, Coords start, TileKinds kind, float animDuration)
        {
            var ctrl = _rendererContainers.SpawnFx<BoltRegularTileCombine>(FxType.BoltBoosterCombine);
            ctrl.transform.localPosition = _gridController.ToLocalPosition(start);
            ctrl.PlayClipWithDuration(m3Settings, "Main", animDuration, () =>
            {
                AudioProxy.PlaySound(Match3SoundIds.Award5);
                Rx.Invoke(1f, _ => { ctrl.gameObject.Release(); });
            });
            SetupEffectColorByKind(ctrl.gameObject, kind);
        }

        private void SetupEffectColorByKind(GameObject effectInstance, TileKinds kind)
        {
            TilesResourcesRef
                .GetAsync(kind)
                .ContinueWith(tileData =>
                {
                    if (tileData == null)
                        return;

                    var configurator = effectInstance.GetComponent<EffectDisplayStateConfiguratorComponent>();
                    
                    if (configurator == null)
                        return;

                    configurator.SetParticlesColor(tileData.Color);
                    configurator.SetSpritesColor(tileData.Color);
                });
        }

        private void LocateCombineEffect(TileView firstTileView, TileView secondTileView, Transform effectTransform)
        {
            var firstTransform = firstTileView.transform;
            var secondTransform = secondTileView.transform;

            var worldPos = (firstTransform.position + secondTransform.position) * 0.5f;
            effectTransform.position = worldPos;
        }

        public void SpawnSodaBottleColorFx(Vector3 offset, FxType fxType, FxOptionalParameters prm,
            float releaseTime = 1f)
        {
            var ctrl = _rendererContainers.SpawnFx<SingleAnimatorEffect>(fxType);
            ctrl.ApplyParameters(prm);
            var go = ctrl.gameObject;
            SetAutoreleaseComponentTimer(go, releaseTime);
            var rectTransform = go.transform as RectTransform;
            rectTransform.position = offset;
        }

        public void SpawnSkunkFx(Coords start, Coords target, Action callback)
        {
            var startLocalPos = (Vector3)_gridController.ToLocalPosition(start);
            var targetLocalPos = (Vector3)_gridController.ToLocalPosition(target);
            var ctrl = _rendererContainers.SpawnFx<SkunkAttackEffect>(FxType.SkunkAttack);
            ctrl.transform.localPosition = targetLocalPos;
            ctrl.SetupSkunkAttackEffect(ctrl.transform.parent.localToWorldMatrix.MultiplyPoint(startLocalPos), callback);
            SetAutoreleaseComponentTimer(ctrl.gameObject, 3f);
        }

        public void SpawnBoltRegularTileCombine(M3Settings m3Settings, TileView firstTileView, float boltAnimDuration)
        {
            PlayHapticFeedback(ImpactPreset.LightImpact);
            
            var ctrl = _rendererContainers.SpawnFx<BoltRegularTileCombine>(FxType.BoltRegularTileCombine);
            var go = ctrl.gameObject;
            go.transform.position = firstTileView.transform.position;
            ctrl.PlayClipWithDuration(m3Settings, "Main", boltAnimDuration, () =>
            {
                AudioProxy.PlaySound(Match3SoundIds.Award5);
                Rx.Invoke(1f, _ => { ctrl.gameObject.Release(); });
            });
        }

        public void SpawnBoltBoltCombine(M3Settings m3Settings, Coords coord, TileView firstTileView, TileView secondTileView)
        {
            AudioProxy.PlaySound(Match3SoundIds.LightningBoltStart);

            var ctrl = _rendererContainers.SpawnFx<BoltBoltCombineEffect>(m3Settings.IsSuperDiscoBallActive
                ? FxType.SuperDiscoBallSuperDiscoBallCombine
                : FxType.DiscoBallDiscoBallCombine);

            LocateCombineEffect(firstTileView, secondTileView, ctrl.transform);

            var hashCode = int.MaxValue;
            if (firstTileView != null)
            {
                hashCode = firstTileView.TileMotionController.Coords.GetHashCode();
            }

            _vibrations.EnqueueHaptic(ImpactPreset.HeavyImpact, hashCode);
            ctrl.Setup(firstTileView, secondTileView, m3Settings, ExplosionCallback);
            return;

            void ExplosionCallback()
            {
                _gridController.DoTweenShakeBoard(coord.GetHashCode(), shakeSettings: ShakeSettingsType.BoltBolt);
            }
        }

        public void SpawnBombLinebreakerCombine(TileView firstTileView, TileView secondTileView, float time)
        {
            var ctrl = _rendererContainers.SpawnFx<BombLinebreakerCombineEffect>(FxType.BombLinebreakerCombine);
            LocateCombineEffect(firstTileView, secondTileView, ctrl.transform);
            ctrl.Setup(firstTileView, secondTileView, time);
        }

        private void SetAutoreleaseComponentTimer(GameObject target, float time)
        {
            var autorelease = target.GetComponent<AutoReleaseEffect>();
            if (autorelease != null)
            {
                _releaseableGoSet.Add(autorelease.gameObject);
                autorelease.SetTime(time);
                autorelease.SetCallback(ReleaseCallback);

                void ReleaseCallback()
                {
                    _releaseableGoSet.Remove(autorelease.gameObject);
                }
            }
        }

        public void SpawnAnticipationEffect(Coords target)
        {
            var pos = (Vector3)_gridController.ToLocalPosition(target);
            var ctrl = _rendererContainers.SpawnFx<AutoReleaseEffect>(FxType.Anticipation);
            ctrl.transform.localPosition = pos;
            SetAutoreleaseComponentTimer(ctrl.gameObject, 3f);
        }


        public void SpawnSpreadAnticipationEffect(Coords target, FxType type)
        {
            var pos = (Vector3)_gridController.ToLocalPosition(target);
            var ctrl = _rendererContainers.SpawnFx<AutoReleaseEffect>(type);
            ctrl.transform.localPosition = pos;
            SetAutoreleaseComponentTimer(ctrl.gameObject, 1f);
        }

        public void SpawnSwapEffect(TileView firstView, TileView secondView, float duration)
        {
            var target = firstView != null ? firstView.transform : secondView.transform;

            var effect = _rendererContainers.SpawnFx<TransformFollower>(FxType.Swap);
            if (firstView != null && secondView != null)
            {
                var difference = (secondView.transform.position - firstView.transform.position).normalized * CellSize.y;
                // we start half behind and move to half further
                effect.transform.position = target.position - difference * 0.5f;
                effect.SetOffset(difference * 0.5f);
                effect.FollowThis(target, false);
            }
            else
            {
                effect.transform.position = target.position;
                effect.FollowThis(target, false);
            }

            SetAutoreleaseComponentTimer(effect.gameObject, duration);
        }

        public void SpawnDiscoRushCollect(TileView firstTileView, TileView secondTileView, float totalDuration, int countOfTilesToDestroy)
        {
            if (firstTileView == null || secondTileView == null) return;
            var scoreText = _rendererContainers.SpawnFx<DiscoRushCollect>(FxType.DiscoRushCollect);
            LocateCombineEffect(firstTileView, secondTileView, scoreText.transform);
            scoreText.transform.position += new Vector3(0, CellSize.y, 0);
            scoreText.Setup(totalDuration, countOfTilesToDestroy);
        }

        public void SpawnDiscoRushCollect(Coords firstCoords, float totalDuration, int countOfTilesToDestroy)
        {
            var scoreText = _rendererContainers.SpawnFx<DiscoRushCollect>(FxType.DiscoRushCollect);
            scoreText.transform.localPosition = _gridController.ToLocalPosition(firstCoords) + new Vector2(0, CellSize.y);
            scoreText.Setup(totalDuration, countOfTilesToDestroy);
        }

        public GameObject SpawnFx(FxType fx)
        {
            var result = fx == FxType.SlotMachineColorBombAppear ? SpawnSlotMachineColorCombAppear() : _rendererContainers.SpawnFx(fx);
            SetAutoreleaseComponentTimer(result, _settings.SpawnFxReleaseTime);
            return result;
        }

        private GameObject SpawnSlotMachineColorCombAppear()
        {
            var effect = _rendererContainers.SpawnFx<SlotMachineDiscoBallEffect>(FxType.SlotMachineColorBombAppear);
            effect.SetDiscoBallType(_settings.IsSuperDiscoBallActive);
            return effect.gameObject;
        }

        public void SpawnBoosterTrail(Transform target, float duration)
        {
            var effect = _rendererContainers.SpawnFx<TransformFollower>(FxType.BoosterTrail);
            effect.transform.localPosition = target.position;
            effect.FollowThis(target);
            SetAutoreleaseComponentTimer(effect.gameObject, duration);
        }

        public void SpawnSmallCrossExplosion(Coords target, CardinalDirections dir)
        {
            var ctrl = _rendererContainers.SpawnFx<SmallCrossExplosionEffect>(FxType.SmallCrossExplosion);
            ctrl.transform.localPosition = _gridController.ToLocalPosition(target);
            ctrl.Setup(dir);
            SetAutoreleaseComponentTimer(ctrl.gameObject, 3f);
        }

        public void SpawnPropellerDestroy(Vector2 coords)
        {
            var ctrl = _rendererContainers.SpawnFx<SingleAnimatorEffect>(FxType.PropellerDestroy);
            SetAutoreleaseComponentTimer(ctrl.gameObject, 2f);
            ctrl.transform.localPosition = _gridController.ToLocalPosition(coords);
            _vibrations.PlayHaptic(ImpactPreset.LightImpact);
        }

        public void SpawnFireWorksDestroy(Vector2 target)
        {
            var ctrl = _rendererContainers.SpawnFx<SingleAnimatorEffect>(FxType.FireWorksDestroy);
            SetAutoreleaseComponentTimer(ctrl.gameObject, 2f);
            ctrl.transform.localPosition = _gridController.ToLocalPosition(target);
            _vibrations.PlayHaptic(ImpactPreset.LightImpact);
        }

        public void PlayHapticFeedback(ImpactPreset impactPreset)
        {
            _vibrations.PlayHaptic(impactPreset);
        }

        public void ShakeBoard(Coords coords, ShakeSettingsType shakeSettingsType)
        {
            _gridController.DoTweenShakeBoard(coords.GetHashCode(), shakeSettings: shakeSettingsType);
        }

        public void SpawnPropellerFlight(int propellerComboIndex, TrajectoryMultiplierDeterminer trajDeterminer,
            Coords sourceCoords, Coords targetCoords, Vector2 targetPosition, Vector2 cellSize,
            float duration, Action launched, Action reached)
        {
            var ctrl = _rendererContainers.SpawnFx<PropellerFlightEffect>(FxType.PropellerFlight);
            var startPosition = _gridController.ToLocalPosition(sourceCoords);
            targetPosition = _gridController.ToLocalPosition(targetPosition);
            var settings = _settings.GetSettingsForEffect<PropellerEffectSettings>(FxType.PropellerFlight);
            _vibrations.PlayHaptic(ImpactPreset.LightImpact);
            SpawnFlyShadow(ctrl.transform as RectTransform, duration, settings.ShadowDistance,
                settings.ScaleEase);
            ctrl.Launch(sourceCoords, targetCoords, propellerComboIndex, startPosition, targetPosition, cellSize, trajDeterminer,
                duration, settings, _tilesResources.TileLayerSpriteRescale, reached);
            launched.SafeInvoke();
        }

        public void StartFlightForPropellerCombo(Coords propellerCoord, Coords otherCoord, Coords targetCoord,
            TrajectoryMultiplierDeterminer multiplierDeterminer, Action launched, Action reached, int targetTileId)
        {
            //coords are swapped here before they are coming after swap, but key is created and added to dict before swap
            if (_preSwapInitialtedEffects.TryGetValue(new PreSwapInitialtedEffectId(otherCoord, propellerCoord),
                    out var effect) && effect is PropellerComboEffect propellerComboEffect)
            {
                var ctrl = propellerComboEffect.Ctrl;

                var settings = _settings.GetSettingsForEffect<PropellerComboFlightEffectSettings>(FxType.PropellerComboFlight);
                ctrl.LaunchFlight(propellerCoord, otherCoord, multiplierDeterminer, targetCoord, settings, reached, _gridController, targetTileId).Forget(ex => BDebug.LogError(LogCat.Match3, ex));
                AudioProxy.PlaySound(Match3SoundIds.PropellerComboFly);
                launched.SafeInvoke();
            }
        }

        public void SpawnFireWorksFlight(Vector2 sourceCoords, Vector2 targetCoords, float duration,
            FireWorksEffectSettings settings, Action launched, Action reached)
        {
            var ctrl = _rendererContainers.SpawnFx<FireWorksFlightEffect>(FxType.FireWorksFlight);
            _vibrations.PlayHaptic(ImpactPreset.LightImpact);
            ctrl.Launch(sourceCoords, targetCoords, duration, _gridController, settings, reached);
            launched.SafeInvoke();
        }

        public GameObject StartShake(Coords coords, float shakeEffectTime)
        {
            var tileView = _tileController.GetTileViewByCoord(coords, false);
            if (tileView != null)
            {
                if (tileView.Animator != null)
                {
                    tileView.Animator.Shake();
                }

                var ctrl = _rendererContainers.SpawnFx<ActivePoolItem>(FxType.ShakeOverlayEffect);
                LocateEffectInLocalSpace(ctrl, shakeEffectTime, coords.ToUnityVector2());
                return ctrl.gameObject;
            }

            return null;
        }

        public void PlayGondolaFlagAnimation(Coords coords)
        {
            var cellView = _cellController.GetCellView(coords);
            cellView?.Animate(CellAnimation.FlagEndCollect);
        }

        public void PlaySquidHit(Coords mainCellCoords, Vector2 offsetCoords, float releaseTime = 1f)
        {
            var localPos = _gridController.ToDisplacedLocalPosition(mainCellCoords);
            var from = (Vector2)_gridController.Transform.localToWorldMatrix.MultiplyPoint(localPos);
            var offset = _gridController.ToLocalPosition(offsetCoords - CenterOffset);
            from += offset;
            var ctrl = _rendererContainers.SpawnFx<SingleAnimatorEffect>(FxType.SquidHit);
            if (ctrl == null)
            {
                BDebug.LogError(LogCat.Match3, "SquidHit Fx is null");
                return;
            }
            var go = ctrl.gameObject;
            go.transform.position = from;
            SetAutoreleaseComponentTimer(go, releaseTime);
        }

        public void Complete()
        {
            foreach (var tween in _tweenersSet)
            {
                tween?.Kill();
            }

            _tweenersSet.Clear();

            foreach (var go in _releaseableGoSet)
            {
                go.Release();
            }

            _releaseableGoSet.Clear();
        }

        public void SetTileHighlight(Coords coords, bool value)
        {
            _tileController?.GetTileViewByCoord(coords, false)?.SetGlow(value);
        }

        public void SpawnFlyShadow(RectTransform tf, float duration, Vector3 shadowDistance, AnimationCurve scaleEase)
        {
            var ctrlShadow = _rendererContainers.SpawnFx<PropellerFlightShadowEffect>(FxType.PropellerFlightShadow);
            ctrlShadow.LaunchFollowTransform(tf, duration, _tilesResources.TileLayerSpriteRescale, shadowDistance, scaleEase);
        }        
    }
}