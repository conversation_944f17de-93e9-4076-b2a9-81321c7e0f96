using System.Collections.Generic;
using BBB.DI;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.GoalsService;
using BBB.MMVibrations;
using BBB.UI;
using BBB.UI.Level;
using BBB.UI.Level.Input;
using BBB.UI.Level.Scripts.Boosts;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems
{
    public class PlaySimulationActionProxy
    {
        public Match3SpecialVoiceoversPlayer VoiceoverPlayer { get; }
        public IM3CharacterAnimator CharacterAnimator { get; }
        public GameController GameController { get; }
        public GoalsSystem GoalsSystem { get; }
        public GoalResourcesPanel GoalPanel { get; }
        public IGridController GridController { get; }
        public ICellController CellController { get; }
        public TileController TileController { get; }
        public ScoreRenderer ScoreRenderer { get; }
        public FxRenderer FXRenderer { get; }
        public AwardRenderer AwardRenderer { get; }
        public OverlaysRenderer OverlayRenderer { get; }
        public M3Settings Settings { get; }
        public SuperBoostSystem SuperBoostSystem { get; }
        public AssistValuesPanelController AssistPanel { get; }
        public IVibrationsWrapper Vibrations { get; }
        public TileFeedbackNotificationHandler TilesFeedbackNotificationsHandler { get; } = new();
        public Dictionary<int, Coords> TilesUnderLongSpawningAnimation { get; } = new();
        public Match3SimulationPlayer Match3SimulationPlayer { get; }
        public TileResourceSelector TileResourceSelector { get; }
        public TilesResources TilesResources { get; }
        public ILevelSkipper LevelSkipper { get; }
        public TileTickPlayer TileTickPlayer { get; }
        public ILevelAnalyticsReporter LevelAnalyticsReporter { get; }

        public SuperBoostPanelController SuperBoostController { get; }
        public ButlerGiftGlobeController ButlerGiftGlobeController { get; }
        public ILevelRevealer LevelRevealer { get; }
        public IButlerGiftManager ButlerGiftManager { get; }
        public IEventDispatcher EventDispatcher { get; }
        public IInputController InputController { get; }

        public PlaySimulationActionProxy(IContext context)
        {
            Match3SimulationPlayer = context.Resolve<Match3SimulationPlayer>();
            VoiceoverPlayer = context.Resolve<Match3SpecialVoiceoversPlayer>();
            CharacterAnimator = context.Resolve<IM3CharacterAnimator>();
            GameController = context.Resolve<GameController>();
            GridController = context.Resolve<IGridController>();
            CellController = context.Resolve<ICellController>();
            TileController = context.Resolve<TileController>();
            ScoreRenderer = context.Resolve<ScoreRenderer>();
            FXRenderer = context.Resolve<FxRenderer>();
            AwardRenderer = context.Resolve<AwardRenderer>();
            OverlayRenderer = context.Resolve<OverlaysRenderer>();
            GoalsSystem = context.Resolve<GoalsSystem>();
            GoalPanel = context.Resolve<GoalResourcesPanel>();
            Settings = context.Resolve<M3Settings>();
            SuperBoostSystem = context.Resolve<SuperBoostSystem>();
            AssistPanel = context.Resolve<AssistValuesPanelController>();
            Vibrations = context.Resolve<IVibrationsWrapper>();
            TileResourceSelector = context.Resolve<TileResourceSelector>();
            TilesResources = context.Resolve<TilesResources>();
            LevelSkipper = context.Resolve<ILevelSkipper>();
            TileTickPlayer = context.Resolve<TileTickPlayer>();
            LevelAnalyticsReporter = context.Resolve<ILevelAnalyticsReporter>();
            SuperBoostController = context.Resolve<SuperBoostPanelController>();
            ButlerGiftGlobeController = context.Resolve<ButlerGiftGlobeController>();
            LevelRevealer = context.Resolve<ILevelRevealer>();
            ButlerGiftManager = context.Resolve<IButlerGiftManager>();
            EventDispatcher = context.Resolve<IEventDispatcher>();
            InputController = context.Resolve<IInputController>();
        }
    }
}