using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.Match3.Debug;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.UI.Level.Match3BoostProgressProvider;
using BebopBee.Core;
using Core.Configs;
using FBConfig;
using GameAssets.Scripts.Tutorial.Steps;
using UnityEngine;
using SpecPair = UniRx.Tuple<BBB.TileSpeciality, BBB.TileSpeciality>;
using SuperBoostConfig = FBConfig.SuperBoostConfig;

namespace BBB.Match3.Systems
{
    public class SuperBoostReadyEvent : IEvent
    {
    }

    public class SuperBoostSystem : IContextInitializable, IContextReleasable
    {
        private static readonly Type[] RequiredConfigs =
        {
            typeof(SuperBoostConfig),
            typeof(SuperBoostProgressConfig)
        };

        private static SuperBoostSystem _instance;

        public event Action OnProgressIncreasedEvent;
        public event Action OnProgressResetEvent;

        private readonly Dictionary<SpecPair, float> _specToProgressMap = new();

        public float CurrentProgress
        {
            get => _progressProvider.GetValue();
            private set => _progressProvider.SetValue(value);
        }

        public bool AllowedToUse { get; private set; }

        public Vector2 LastVec { get; private set; }

        private IMatch3BoostProgressProvider _progressProvider;
        private IGridController _gridController;
        private GameController _gameController;
        private TileRevealer _tileRevealer;
        private IEventDispatcher _eventDispatcher;
        private ILockManager _lockManager;
        private ICoroutineExecutor _coroutineExecutor;
        private readonly HashSet<int> _superBoostTiles = new();

        private bool _enabled;
        private bool _debugMode;
        
        public float FillSpeed { get; private set; }
        
        public void InitializeByContext(IContext context)
        {
            LastVec = default;
            _enabled = false;
            _debugMode = false;
            FillSpeed = 0f;

            _eventDispatcher = context.Resolve<IEventDispatcher>();
            if (_instance != null)
            {
                _eventDispatcher.Unsubscribe(_instance);
            }

            _instance = this;
            _progressProvider = context.Resolve<IMatch3BoostProgressProvider>();
            _gridController = context.Resolve<IGridController>();
            _gameController = context.Resolve<GameController>();
            _tileRevealer = context.Resolve<TileRevealer>();
            _lockManager = context.Resolve<ILockManager>();
            _coroutineExecutor = context.Resolve<ICoroutineExecutor>();

            _eventDispatcher.RemoveListener<SuperBoostProgressSetterEvent>(UpdateProgress);
            _eventDispatcher.AddListener<SuperBoostProgressSetterEvent>(UpdateProgress);

            var config = context.Resolve<IConfig>();
            SetupBoosterConfig(config);
            Config.OnConfigUpdated -= SetupBoosterConfig;
            Config.OnConfigUpdated += SetupBoosterConfig;

            _enabled = !_lockManager.IsLocked(SuperBoostConstants.SuperBoostLockId, LockItemType.Booster);
            CurrentProgress = 0.0f;
            SetAllowedToUse(false);
            _superBoostTiles.Clear();
        }

        private void SetupBoosterConfig(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            var superBoostProgressConfigDict = config.Get<SuperBoostProgressConfig>();
            var superBoostConfigDict = config.Get<SuperBoostConfig>();

            if (superBoostConfigDict != null && superBoostConfigDict.TryGetValue("default", out _))
            {
                FillSpeed = SuperBoostConstants.FillSpeed; //superBoostConfig.FillSpeed;
            }
            else
            {
                BDebug.LogError(LogCat.Match3, "SuperBoostConfig config not found");
            }

            if (superBoostProgressConfigDict != null)
            {
                ParseConfig(superBoostProgressConfigDict);
            }
            else
            {
                BDebug.LogError(LogCat.Match3, "SuperBoostProgressConfig config not found");
            }
        }


        public void ResetOnRetry(ILevel level)
        {
            CurrentProgress = 0f;
            SetAllowedToUse(false);
            LastVec = Vector2.zero;
        }

        public void SetAllowedToUse(bool allowedToUse)
        {
            AllowedToUse = allowedToUse;
        }

        public void TransitToDebugMode()
        {
            _debugMode = true;
        }

        private void UpdateProgress(SuperBoostProgressSetterEvent obj)
        {
            if (!AllowedToUse)
            {
                AddProgress(obj.Arg0 - CurrentProgress, Vector2.zero);
            }
        }

        public static bool IsAllowedToUse()
        {
            return _instance?.AllowedToUse ?? false;
        }

        private void ParseConfig(IDictionary<string, SuperBoostProgressConfig> configDict)
        {
            _specToProgressMap.Clear();

            if (configDict.Count == 0)
            {
                M3Debug.LogError("SuperBoostProgressConfig dict is empty");
            }

            foreach (var config in configDict.Values)
            {
                var firstSpec = (TileSpeciality) Enum.Parse(typeof(TileSpeciality), config.FirstSpeciality);
                var secondSpec = (TileSpeciality) Enum.Parse(typeof(TileSpeciality), config.SecondSpeciality);
                var tupleKey = new SpecPair(firstSpec, secondSpec);
                _specToProgressMap.Add(tupleKey, config.ProgressAmount);
            }
        }

        public void AddProgress(TileSpeciality firstSpec, TileSpeciality secondSpec,
            Coords firstCoords, Coords secondCoords, bool bonusTime = false)
        {
            if (!_debugMode && (bonusTime || !_enabled))
                return;

            var tupleKeyA = new SpecPair(firstSpec, secondSpec);
            var tupleKeyB = new SpecPair(secondSpec, firstSpec);

            var valueFound = _specToProgressMap.TryGetValue(tupleKeyA, out var amount)
                             || _specToProgressMap.TryGetValue(tupleKeyB, out amount);

            if (valueFound)
            {
                Vector2 lastVec;
                if (firstCoords == secondCoords || firstCoords.Equals(Coords.OutOfGrid) || secondCoords.Equals(Coords.OutOfGrid))
                {
                    var localPos = _gridController.ToDisplacedLocalPosition(firstCoords.Equals(Coords.OutOfGrid) ? secondCoords : firstCoords);
                    lastVec = _gridController.Transform.localToWorldMatrix.MultiplyPoint(localPos);
                }
                else
                {
                    var localPos0 = _gridController.ToDisplacedLocalPosition(firstCoords);
                    var localPos1 = _gridController.ToDisplacedLocalPosition(secondCoords);
                    var mid = Vector2.Lerp(localPos0, localPos1, 0.5f);
                    lastVec = _gridController.Transform.localToWorldMatrix.MultiplyPoint(mid);
                }

                AddProgress(amount, lastVec);
            }
        }

        private void AddProgress(float amount, Vector2 lastVec)
        {
            if (CurrentProgress < 1f)
            {
                var prevProgress = CurrentProgress;
                CurrentProgress += amount;

                BDebug.Log(LogCat.Match3, $"Adding progress to Super Boost amount={amount} SB_progress={CurrentProgress}");

                LastVec = lastVec;

                if (CurrentProgress >= SuperBoostConstants.ProgressReachedThreshold)
                {
                    CurrentProgress = 1f;
                    BDebug.Log(LogCat.Match3, $"Super Boost Reached Max, InputLock = true");
                    _gameController.LockInput(true);
                    HandleProgressReachedMax(CurrentProgress - prevProgress);
                }

                if (CurrentProgress > prevProgress)
                    OnProgressIncreasedEvent.SafeInvoke();
            }
        }

        private void HandleProgressReachedMax(float lastDelta)
        {
            var time = lastDelta / FillSpeed;

            BDebug.LogFormat(LogCat.Match3, "Launching super boost in {0}", time);

            SetAllowedToUse(true);
            Rx.Invoke(SuperBoostConstants.SecondsToWaitBeforeMakeSuperBoostReady + time,
                _ => { _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<SuperBoostReadyEvent>()); });
        }

        public void UseSuperBoost(AutoBoostInstance booster = null, bool isEndGame = false)
        {
            ResetProgress();
            if (!isEndGame)
            {
                _coroutineExecutor.StartCoroutine(SettleBooster(booster));
            }
        }

        private IEnumerator SettleBooster(AutoBoostInstance booster)
        {
            yield return _tileRevealer.SettleExtraBoosters(_gameController.Grid,
                new[] {booster}, false, true);
            if (!_gameController.GameEnded)
            {
                _gameController.LockInput(false);
            }
        }

        public void RegisterSuperBoostTile(int tileId)
        {
            _superBoostTiles.Add(tileId);
        }

        public void UnRegisterSuperBoostTile(int tileId)
        {
            _superBoostTiles.Remove(tileId);
        }

        public bool IsTileCreatedBySuperBoost(int tileId)
        {
            return _superBoostTiles.Contains(tileId);
        }

        public void ResetProgress()
        {
            SetAllowedToUse(false);
            CurrentProgress = 0f;
            OnProgressResetEvent.SafeInvoke();
        }

        public void ReleaseByContext(IContext context)
        {
            Config.OnConfigUpdated -= SetupBoosterConfig;
        }
    }
}