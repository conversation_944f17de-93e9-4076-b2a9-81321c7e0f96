using System.Collections.Generic;
using BBB.Match3.Logic;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.GoalsService
{
    public sealed class GoalActionRemovedTile : GoalActionBase, IGoalGroupOwner
    {
        IEnumerable<Coords> IGoalGroupOwner.AffectedCoords { get { yield return DisplayCoords; } }

        private readonly DamageSource _damageSource;
        private readonly TileState _tileState;
        private readonly TileSpeciality _tileSpeciality;


        public override bool ShouldRenderScoresOnBoard => (_damageSource & DamageSource.ScoreRendering) != 0;
        public override bool ShouldAnimateDidi => (_damageSource & DamageSource.PowerUp) != 0;

        public GoalActionRemovedTile(Coords displayCoords, Tile tile, DamageSource damageSource) : base(displayCoords)
        {
            if (!ReferenceEquals(tile, null))
            {
                TileKind = tile.Kind;
                _tileState = tile.State;
                _tileSpeciality = tile.Speciality;
                DisplayCoords = displayCoords;
                _damageSource = damageSource;
            }
            else
            {
                UnityEngine.Debug.LogError("Invalid GoalActionRemovedTile, tile is null");
                DisplayCoords = Coords.OutOfGrid;
            }
        }

        public override int GetComboDelta()
        {
            return 0;
        }

        public override void Apply(GoalState goalState, GoalScoreRules rules, float comboMult)
        {
            if (DisplayCoords == Coords.OutOfGrid)
                return;

            Score = rules.GetMaxDamageSourceBonusScore(_damageSource);
            Score += rules.GetTileSpecDestroyScore(_tileSpeciality);
            Score += rules.GetTileStateDestroyScore(_tileState);
            goalState.Reduce(GoalType.Score, Score);
        }
    }
}