using System.Collections.Generic;
using BBB.DI;
using BebopBee.Core.UI;
using Cysharp.Threading.Tasks;

namespace BBB.Actions
{
    public class OpenFennecPerksInfoModal : ISimpleAction
    {
        public UniTask ExecuteAsync(IContext context, Dictionary<string, string> actionParams)
        {
            var butlerGiftManager = context.Resolve<IButlerGiftManager>();
            butlerGiftManager.ShowInfoModal(ShowMode.Delayed);
            return UniTask.CompletedTask;
        }
    }
}