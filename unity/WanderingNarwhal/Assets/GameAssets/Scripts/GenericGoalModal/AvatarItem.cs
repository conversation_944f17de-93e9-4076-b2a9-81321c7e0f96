using System;
using BBB.Audio;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.UI.Core;
using BebopBee.Core.Audio;
using BebopBee.Social;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.Quests
{
    public class AvatarItem : ContextedUiBehaviour
    {
        [SerializeField] private AsyncImage _avatarImage;
        [SerializeField] private GameObject _selectedHolder;
        [SerializeField] private Button _button;

        public string AvatarName { get; private set; }
        private IAssetsManager _assetsManager;
        private Action<string> _selectedCallback;

        protected override void InitWithContextInternal(IContext context)
        {
            _assetsManager = context.Resolve<IAssetsManager>();

            _button.ReplaceOnClick(() => _selectedCallback?.Invoke(AvatarName));
        }

        public void Setup(string avatarName, Action<string> selectedCallback)
        {
            LazyInit();

            AvatarName = avatarName;
            _selectedCallback = selectedCallback;

            SetAsSelected(false);
            _avatarImage.Load(avatarName, SocialConstants.AvatarsTag, error =>
            {
                if (_avatarImage)
                {
                    _assetsManager
                        .LoadSpriteAsync(GenericResourceProvider.FallbackOfflineAvatar)
                        .ContinueWith(sprite => _avatarImage.ReplaceSprite(sprite));
                }
            });
        }

        public void SetAsSelected(bool selected)
        {
            if (_selectedHolder != null)
            {
                AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
                _selectedHolder.SetActive(selected);
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            if (_avatarImage != null)
            {
                _avatarImage.Stop();
            }
        }
    }
}