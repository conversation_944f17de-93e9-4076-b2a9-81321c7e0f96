using BBB.Core;
using BBB.DI;
using BBB.UI.Core;
using BBB.Wallet;
using Cysharp.Threading.Tasks;
using TMPro;
using UnityEngine;

namespace BBB.UI
{
    public class UICurrencyFXedComponent : ContextedUiBehaviour
    {
        [SerializeField] private TextMeshProUGUI _number;
        [SerializeField] private GenericRewardItem _genericRewardItem;
        private CurrencyIconsLoader _currencyIconsLoader;

        protected override void InitWithContextInternal(IContext context)
        {
            _currencyIconsLoader = context.Resolve<CurrencyIconsLoader>();
        }

        public void Setup(string currencyUid, int number)
        {
            LazyInit();

            _currencyIconsLoader.LoadAndGetCurrencySpriteSheetSpritesAsync(currencyUid).ContinueWith(sprites =>
            {
                if (sprites == null || (sprites.Length == 1 && sprites[0] == null))
                {
                    BDebug.LogError(LogCat.Resources,$"Couldn't find icons for currency: {currencyUid}");
                    return;
                }
            
                _genericRewardItem.Setup(sprites);
                _number.text = number.ToString();
            });
        }
    }
}