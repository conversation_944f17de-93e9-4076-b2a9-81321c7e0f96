using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.Match3.Debug;
using BBB.Match3.Logic;
using BBB.Match3.Systems;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using BBB.UI;
using BBB.UI.Level;
using UnityEngine;
using BBB.Core.Crash;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Wallet;
using Core.Configs;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;

namespace BBB
{
    /// <summary>
    /// Always use event dispatcher for this class communication to MonoBehaviours and other engine classes
    /// Public methods of this class may be called from m3 simulation on a secondary thread
    /// </summary>
    public sealed class GameController : BbbMonoBehaviour, IContextInitializable, IContextReleasable
    {
        private Match3SimulationPlayer _match3SimulationPlayer;

        public event Action<bool> InputLocked;
        
        public ILevel Level => _levelHolder.level;
        public IAutoBoostInputFactory AutoBoostInputFactory { get; private set; }

        public Grid Grid => Level.Grid;

        public int RemainingMoves { get; private set; }
        public int ExtraMovesAdded { get; private set; }
        public bool GameEnded { get; set; }
        public bool HasWon { get; set; }
        public bool IsBonusTime { get; set; }
        public bool IsLevelEnded { get; set; }
        public bool SimulateLoopException { get; set; }
        public M3Settings Settings { get; private set; }
        public GoalsSystem GoalsSystem { get; private set; }
        public IGridController GridController => _gridController;
        public bool IsInputLocked { get; private set; }
        public List<Coords> AllowedToMoveTiles { get; } = new();
        public bool BoostersUsed { get; private set; }

        public bool AreTilesAllowedToSwap(Coords firstCoords, Coords secondCoords)
        {
            if (AllowedToMoveTiles.IsNullOrEmpty())
                return true;

            if (AllowedToMoveTiles.Contains(firstCoords) && AllowedToMoveTiles.Contains(secondCoords))
                return true;

            return false;
        }

        public bool IsTileAllowedToTap(Coords tapCoord)
        {
            if (AllowedToMoveTiles.IsNullOrEmpty())
                return true;

            if (AllowedToMoveTiles.Count > 1)
                return false;

            if (AllowedToMoveTiles.Contains(tapCoord))
                return true;

            return false;
        }

        public bool IsAllowedToUseBooster(Coords tapCoord)
        {
            if (AllowedToMoveTiles.IsNullOrEmpty())
                return true;

            if (AllowedToMoveTiles.Contains(tapCoord))
                return true;

            return false;
        }

#if UNITY_EDITOR
        public Match3SimulationPlayer M3SimPlayerDEBUG => _match3SimulationPlayer;
#endif

#if UNITY_EDITOR
        public SpawnerSettingsManager DebugSpanwerSettingsManager => _spawnerSettingsManager;
#endif
        public Grid OriginalGrid { get; private set; }

        private LevelHolder _levelHolder;
        private IM3CharacterAnimator _characterAnimator;
        private IGridController _gridController;
        private IEventDispatcher _eventDispatcher;
        private IPlayerManager _playerManager;
        private IScreensManager _screensManager;
        private ILevelAnalyticsReporter _levelAnalyticsReporter;
        private bool _isShuffleFailed;
        private SpawnerSettingsManager _spawnerSettingsManager;
        private ExtraBoostersHelper.PendingBoosters _pendingBoosters;
        private IConfig _config;

        public void InitializeByContext(IContext context)
        {
            RemainingMoves = 0;
            ExtraMovesAdded = 0;
            GameEnded = false;
            IsBonusTime = false;
            IsLevelEnded = false;
            HasWon = false;
            SimulateLoopException = false;
            IsInputLocked = false;

            _characterAnimator = context.Resolve<IM3CharacterAnimator>();
            _gridController = context.Resolve<IGridController>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _playerManager = context.Resolve<IPlayerManager>();
            _screensManager = context.Resolve<IScreensManager>();
            Settings = context.Resolve<M3Settings>();
            _levelHolder = context.Resolve<LevelHolder>();
            GoalsSystem = context.Resolve<GoalsSystem>();
            AutoBoostInputFactory = context.Resolve<IAutoBoostInputFactory>();
            _spawnerSettingsManager = context.Resolve<SpawnerSettingsManager>();
            _match3SimulationPlayer = context.Resolve<Match3SimulationPlayer>();
            _levelAnalyticsReporter = context.Resolve<ILevelAnalyticsReporter>();
            _config = context.Resolve<IConfig>();

            RefreshForLevel(_levelHolder.level);

            Subscribe();
        }

        public void ReleaseByContext(IContext context)
        {
            Unsubscribe();
            M3DebugInfoSystem.CleanupLevel();
        }

        private void Subscribe()
        {
            Unsubscribe();
            _eventDispatcher.AddListener<PendingAutoBoosterAddedEvent>(PendingAutoBoosterAdded);
            _eventDispatcher.AddListener<SuperBoostInvokedEvent>(SuperBoostInvoked);
        }

        private void Unsubscribe()
        {
            if (_eventDispatcher != null)
            {
                _eventDispatcher.RemoveListener<PendingAutoBoosterAddedEvent>(PendingAutoBoosterAdded);
                _eventDispatcher.RemoveListener<SuperBoostInvokedEvent>(SuperBoostInvoked);   
            }
        }

        public void ResetOnRetry(ILevel level)
        {
            RefreshForLevel(level);
        }

        private void RefreshForLevel(ILevel level)
        {
            if (Level == null)
            {
                BDebug.Log(LogCat.Match3, "Level equals null in InitializeByContext");
            }

            HasWon = false;
            _isShuffleFailed = false;

            GameEnded = true;
            IsBonusTime = false;
            IsLevelEnded = false;
            IsInputLocked = true;
            AllowedToMoveTiles.Clear();
            BoostersUsed = false;

            if (Level != null)
            {
                RemainingMoves = Level.TurnsLimit;
                var remainingMovesChanged = _eventDispatcher.GetMessage<RemainingMovesChanged>();
                remainingMovesChanged.Set(RemainingMoves);
                _eventDispatcher.TriggerEventNextFrame(remainingMovesChanged);
            }
        }

        public void SetLevel(ILevel level)
        {
            _levelHolder.level = level;
        }

        protected override void OnDisable()
        {
            M3DebugInfoSystem.CleanupLevel();
        }

#if M3_QUEUE_DEBUG
        private void Update()
        {
            PopQueueMonitor.CurrentFrame = Time.frameCount;
        }
#endif

        public void Run(bool instantReveal = false, Action onEventsManagersSetup = null)
        {
            OriginalGrid = Level.Grid.Clone();
            
            Level.Grid.DebugCurrentLevelUid = Level.Config.Equals(FlatBufferHelper.DefaultProgressionLevelConfig) ? "NULL" : Level.Config.Uid;
            M3DebugInfoSystem.SetupLevel(Level);
            RandomSystem.Reset();
            GameEnded = false;
            IsBonusTime = false;
            IsLevelEnded = false;
            HasWon = false;
            RemainingMoves = Level.TurnsLimit;

            if (instantReveal)
            {
                var remainingMovesChanged = _eventDispatcher.GetMessage<RemainingMovesChanged>();
                remainingMovesChanged.Set(RemainingMoves);
                _eventDispatcher.TriggerEventNextFrame(remainingMovesChanged);
            }
            
            int movesIncreasedByBooster = _playerManager.PlayerInventory.GetBoosterAmount(InventoryItems.ExtraMoveBooster);

            if (movesIncreasedByBooster > 0)
            {
                RemainingMoves += movesIncreasedByBooster;
                ExtraMovesAdded += movesIncreasedByBooster;
                _playerManager.PlayerInventory.AddBooster(InventoryItems.ExtraMoveBooster, -movesIncreasedByBooster);
            }

            _match3SimulationPlayer.RunInitializeBoardProcess(Grid, Level, RemainingMoves, onEventsManagersSetup);
            _match3SimulationPlayer.PostGameStartGridProcess();
            _characterAnimator.Lock(false);
            _characterAnimator.SetCurrentIdleAnim(Match3CharacterAnim.Idle);
            _characterAnimator.PlayAnimation(Match3CharacterAnim.Intro);
            _characterAnimator.Freeze(false);

            _gridController.RevealGrid(instantReveal, movesIncreasedByBooster);
#if UNITY_EDITOR
            if (BBB.M3Editor.M3Editor.IsCurrentSceneLevelEditorScene())
            {
                CrashLoggerService.Log("Match3 Level Name: " + Level.GetLevelFullName());
                CrashLoggerService.Log("Match3 Level Seed: " + RandomSystem.Seed);
                BDebug.Log(LogCat.Match3, "Match3 Level Name: " + Level.GetLevelFullName());
                BDebug.Log(LogCat.Match3, "Match3 Level Seed: " + RandomSystem.Seed);
            }
#endif
        }

        public void Refresh(bool instantReveal = true)
        {
            OriginalGrid = Level.Grid.Clone();
            RemainingMoves = Level.TurnsLimit;
            _gridController.Clear();
            _gridController.SetupGrid(Level.Grid);
            _gridController.RevealGrid(instantReveal, revealMovesChange: 0);
            
            var remainingMovesChanged = _eventDispatcher.GetMessage<RemainingMovesChanged>();
            remainingMovesChanged.Set(RemainingMoves);
            _eventDispatcher.TriggerEventNextFrame(remainingMovesChanged);
            
            _levelAnalyticsReporter.LevelStarted(Level, _config, string.Empty);
        }

        public void DebugCompleteGoals()
        {
            if (!GameEnded && Level != null)
            {
                GoalsSystem.RemoveRemainingGoalsExceptScore();
            }
        }

        public void DebugSetOneMoveLeft()
        {
            if (!GameEnded && Level != null)
            {
                RemainingMoves = 1;
                var remainingMovesChanged = _eventDispatcher.GetMessage<RemainingMovesChanged>();
                remainingMovesChanged.Set(RemainingMoves);
                _eventDispatcher.TriggerEventNextFrame(remainingMovesChanged);
            }
        }

        public void DebugSetMovesLeft(int moves)
        {
            if (!GameEnded && Level != null)
            {
                RemainingMoves = Mathf.Max(1, moves);
                var remainingMovesChanged = _eventDispatcher.GetMessage<RemainingMovesChanged>();
                remainingMovesChanged.Set(RemainingMoves);
                _eventDispatcher.TriggerEventNextFrame(remainingMovesChanged);
            }
        }

        private void PendingAutoBoosterAdded(PendingAutoBoosterAddedEvent ev)
        {
            _pendingBoosters = new ExtraBoostersHelper.PendingBoosters(ev.Arg0, ev.Arg1);
        }

        private void SuperBoostInvoked(SuperBoostInvokedEvent superBoostInvokedEvent)
        {
            if (superBoostInvokedEvent.Arg1 != Coords.OutOfGrid)
            {
                BoostersUsed = true;

                var remainingMovesChanged = _eventDispatcher.GetMessage<RemainingMovesChanged>();
                remainingMovesChanged.Set(RemainingMoves);
                _eventDispatcher.TriggerEventNextFrame(remainingMovesChanged);
            }
        }

        public void PlayerInputSync(IPlayerInput playerInput)
        {
            var randomState = RandomSystem.GetRandomGenerationValues();
            _levelAnalyticsReporter.RegisterPlayerMove(randomState, playerInput);
            _match3SimulationPlayer.SimulateSync(OriginalGrid, Grid, Level, playerInput, RemainingMoves, _pendingBoosters, false);
        }

        public async UniTask<SimulationResult> PlayerInputAsync(IPlayerInput playerInput)
        {
            if (IsInputLocked)
            {
                CrashLoggerService.Log("Multiple inputs detected. InputLock = true " + playerInput);
                return SimulationResult.Fail;
            }
            
            CrashLoggerService.Log($"Match3 input on move {Level.TurnsLimit - RemainingMoves + 1} : " + playerInput);

            LockInput(true);
            var goalsProgressBeforeMove = GoalsSystem.NumberOfOverAllGoalsLeft();
            var randomState = RandomSystem.GetRandomGenerationValues();

            var result = await _match3SimulationPlayer.SimulateAsync(OriginalGrid, Grid, Level, playerInput, RemainingMoves, _pendingBoosters, result =>
            {
                if (result != SimulationResult.Fail)
                {
                    if (playerInput.Type is PlayerInputType.Swap or PlayerInputType.DTap or PlayerInputType.Tap)
                    {
                        RemainingMoves--;
                        if (RemainingMoves < 0)
                        {
                            RemainingMoves = 0;
                        }
                    }

                    _levelAnalyticsReporter.RegisterPlayerMove(randomState, playerInput);

                    var usedBoostersBefore = BoostersUsed;
                    if (playerInput is PlayerInputItemBase)
                    {
                        BoostersUsed = true;
                    }

                    _isShuffleFailed = result == SimulationResult.SuccessShuffleLose;

                    HasWon = result == SimulationResult.SuccessWin;

                    if (RemainingMoves <= 0 || HasWon || _isShuffleFailed)
                    {
                        GameEnded = true;
                        BDebug.Log(LogCat.Match3,
                            $"GameEnded with remaining moves {RemainingMoves}, result {result}, InputLock = true");
                        LockInput(true);
                    }

                    // We consider it's the first move if:
                    // 1. the user uses a booster as their first move
                    // 2. the user actually made a move
                    var isFirstMove = !usedBoostersBefore && BoostersUsed &&
                                      RemainingMoves == _levelHolder.level.TurnsLimit ||
                                      !BoostersUsed && RemainingMoves == _levelHolder.level.TurnsLimit - 1;

                    if (isFirstMove)
                    {
                        _levelAnalyticsReporter.RegisterFirstMove();
                        _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<FirstLevelMoveEvent>());
                    }

                    var goalsProgressAfterMove = GoalsSystem.NumberOfOverAllGoalsLeft();
                    if (goalsProgressAfterMove == goalsProgressBeforeMove)
                    {
                        _levelAnalyticsReporter.RegisterWastedMove();
                    }

                    var remainingMovesChanged = _eventDispatcher.GetMessage<RemainingMovesChanged>();
                    remainingMovesChanged.Set(RemainingMoves);
                    _eventDispatcher.TriggerEventNextFrame(remainingMovesChanged);
                    if (HasWon)
                    {
                        var levelResultPredicted = _eventDispatcher.GetMessage<LevelResultPredicted>();
                        levelResultPredicted.Set(LevelOutcome.Win);
                        levelResultPredicted.AllowRecording = true;
                        levelResultPredicted.ScreenType = _screensManager.GetCurrentScreenType();
                        levelResultPredicted.level = Level;
                        _eventDispatcher.TriggerEventNextFrame(levelResultPredicted);
                    }
                }
                else
                {
                    if (playerInput.Type != PlayerInputType.Swap)
                    {
                        LockInput(false);
                    }
                }
            });

            return result;
        }

        public void SimulationEnded()
        {
            if (GameEnded)
            {
                var outcome = HasWon
                    ? LevelOutcome.Win
                    : GetCurrentLoseOutcome();

                var levelEndedEvent = _eventDispatcher.GetMessage<LevelEndedEvent>();
                levelEndedEvent.LevelOutcome = outcome;
                levelEndedEvent.AllowShowOutOfMovesModal = true;
                levelEndedEvent.ShuffleCount = _match3SimulationPlayer.ShuffleCount;
                _eventDispatcher.TriggerEventNextFrame(levelEndedEvent);
#if BBB_LOG
                M3Debug.Log("[GameController]".Paint() + " Game ended " + (HasWon ? "You Win!" : ("You Lose [" + outcome + "]")));
#endif
            }

            BDebug.Log(LogCat.Match3, "Simulation Ended, now InputLock = " + GameEnded);
            LockInput(GameEnded);
        }

        private LevelOutcome GetCurrentLoseOutcome()
        {
            if (_isShuffleFailed)
                return LevelOutcome.ShuffleFailed;
            if (RemainingMoves <= 0)
                return LevelOutcome.OutOfMoves;

            return LevelOutcome.Exit;
        }

        public Coroutine StartCoroutineMethod(IEnumerator coroutineMethod)
        {
            return StartCoroutine(coroutineMethod);
        }

        public void LockInput(bool state)
        {
            IsInputLocked = state;
            InputLocked?.Invoke(state);
        }

        public void Clear()
        {
            _levelHolder.level = null;
            _pendingBoosters = null;
        }

        public bool AddExtraMoves(int quantity)
        {
            var addedToPanel = false;
            if (quantity > 0)
            {
                GameEnded = false;
                BDebug.Log(LogCat.Match3, $"Adding {quantity} extra moves, InputLock = false");
                LockInput(false);

                if (RemainingMoves <= 0)
                {
                    RemainingMoves += quantity;
                    ExtraMovesAdded += quantity;
                    addedToPanel = true;
                }

                var remainingMovesChanged = _eventDispatcher.GetMessage<RemainingMovesChanged>();
                remainingMovesChanged.Set(RemainingMoves);
                _eventDispatcher.TriggerEventNextFrame(remainingMovesChanged);
            }

            return addedToPanel;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Unsubscribe();
        }
    }
}