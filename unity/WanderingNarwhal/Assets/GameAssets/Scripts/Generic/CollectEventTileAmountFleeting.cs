using TMPro;
using UnityEngine;
using BBB.Core;
using UnityEngine.UI;

namespace BBB
{
    public class CollectEventTileAmountFleeting : ActivePoolItem
    {
        [SerializeField] private TextMeshProUGUI _amountText;
        [SerializeField] private Image _sprite;
        private Animator _animator;

        private Material _defaultMaterial;

        public override void OnInstantiate()
        {
            if (!Initialized)
                _animator = gameObject.GetComponent<Animator>();
            _defaultMaterial = _amountText.fontSharedMaterial;
            base.OnInstantiate();
        }

        public override void OnRelease()
        {
            base.OnRelease();
            _amountText.fontMaterial = _defaultMaterial;
            if (!Initialized)
                _animator.Rebind();
        }

        public void Setup(Sprite sprite, int amount)
        {
            _sprite.sprite = sprite;
            _sprite.preserveAspect = true;
            _amountText.text = $"+ {amount}";
        }

        public void SetMaterial(Material material)
        {
            _amountText.fontMaterial = material;
        }

        public void OnAnimationEnded()
        {
            gameObject.Release();
        }
    }
}