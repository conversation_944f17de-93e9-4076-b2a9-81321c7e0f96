using System;
using BBB.DI;
using BBB.MMVibrations;
using BBB.MMVibrations.Plugins;
using BBB.UI.Core;
using DG.Tweening;
using TMPro;
using UnityEngine;

namespace BBB.UI.Level
{
    public class AnimatableNumberText : ContextedUiBehaviour
    {
        [SerializeField] private bool _commaFormatting = true;
        [SerializeField] private bool _slashMaxFormat = false;

        private int _max;
        private int _current;
        private int _previous;
        private Tween _scoreTween;

        private Action _completeCallback;

        private TextMeshProUGUI _text;
        private IVibrationsWrapper _vibrationsWrapper;

        protected override void InitWithContextInternal(IContext context)
        {
            _text = GetComponent<TextMeshProUGUI>();
            _vibrationsWrapper = context.Resolve<IVibrationsWrapper>();
        }

        public void SetMaxScore(int max)
        {
            _max = max;
            _text.text = GetCurrentText(_current);
        }

        private string GetCurrentText(int value)
        {
            if (_commaFormatting)
            {
                if (_slashMaxFormat)
                {
                    return value.ToCurrency() + "/" + _max.ToCurrency();
                }
                else
                {
                    return value.ToCurrency();
                }
            }
            else
            {
                if (_slashMaxFormat)
                {
                    return value + "/" + _max;
                }
                else
                {
                    return value.ToString();
                }
            }
        }

        public void SetScoreInstant(int score)
        {
            LazyInit();

            _previous = _current;
            _current = score;
            
            _text.text = GetCurrentText(_current);
        }

        public void SetScoreAnimated(int score, float progressDuration)
        {
            LazyInit();

            _scoreTween.Kill();
            _previous = _current;
            _current = score;
            _scoreTween = DOTween.To(() => _previous, value =>
                {
                    _text.text = GetCurrentText(value);
                }, _current, progressDuration)
                .OnKill(() =>
                {
                    _text.text = GetCurrentText(_current);
                })
                .OnComplete(() =>
                {
                    _completeCallback.SafeInvoke();
                    _completeCallback = null;
                });
        }

        public void SetScoreAnimated(int previous, int current, float progressDuration, bool shouldPlayHaptics)
        {
            LazyInit();

            _scoreTween.Kill();
            _previous = previous;
            _current = current;
            var lastValue = previous;
            _scoreTween = DOTween.To(() => _previous, value =>
                {
                    _text.text = GetCurrentText(value); 
                    if (shouldPlayHaptics && value != lastValue)
                    {
                        _vibrationsWrapper.PlayHaptic(ImpactPreset.LightImpact);
                        lastValue = value;
                    }
                }, _current, progressDuration)
                .OnKill(() =>
                {
                    _text.text = GetCurrentText(_current);  
                    if (shouldPlayHaptics && _current != lastValue)
                    {
                        _vibrationsWrapper.PlayHaptic(ImpactPreset.LightImpact);
                    }
                    
                })
                .OnComplete(() =>
                {
                    _completeCallback.SafeInvoke();
                    _completeCallback = null;
                });
        }

        public void KillIfNeeded()
        {
            _scoreTween?.Kill();
        }
    }
}