using System;
using System.Collections.Generic;
using BBB.Match3.Renderer;
using BBB.UI.Level;
using UnityEngine;

namespace BBB
{
    /// <summary>
    /// Speechbubble text settings.
    /// Contains mapping between TileLayerState and localization key.
    /// </summary>
    [CreateAssetMenu(menuName = "BBB/VisitorSpeechSettings", fileName = "VisitorSpeechSettings", order = 0)]
    public class VisitorSpeechSettingsAsset : ScriptableObject
    {
        [Serializable]
        public class VisitorSpeechItem
        {
            [TileLayerStateName]
            public int TileValue;
            public string LocKey;
        }

        [SerializeField]
        private List<VisitorSpeechItem> _list = new List<VisitorSpeechItem>();

        public string FindSpeechLocKeyForTile(TileLayerState tile)
        {
            foreach (var item in _list)
            {
                var itemLayerState = item.TileValue.GetTileLayerFromInt();
                if (itemLayerState == tile)
                {
                    return item.LocKey;
                }
            }

            return "";
        }
    }
}