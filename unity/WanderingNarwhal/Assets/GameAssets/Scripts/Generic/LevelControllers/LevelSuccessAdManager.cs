using System;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.RaceEvents;
using BBB.Wallet;

namespace BBB.UI
{
    public class LevelSuccessAdManager
    {
        private readonly LevelSuccessAdData _levelSuccessAdData = new();
        private readonly IRaceEventManager _raceEventManager;
        private readonly IScreensManager _screensManager;
        private readonly IGameEventManager _gameEventManager;
        private readonly IWalletManager _walletManager;
        private readonly LevelHolder _levelHolder;
        private readonly VideoAdManager _videoAdManager;
        private readonly GameEventMatch3ManagersCollection _gameEventM3ManagerCollection;
        private readonly IRaceEventMatch3Manager _raceEventMatch3Manager;

        public LevelSuccessAdManager(IContext context)
        {
            _screensManager = context.Resolve<IScreensManager>();
            _gameEventManager = context.Resolve<IGameEventManager>();
            _raceEventManager = context.Resolve<IRaceEventManager>();
            _walletManager = context.Resolve<IWalletManager>();
            _levelHolder = context.Resolve<LevelHolder>();
            _videoAdManager = context.Resolve<VideoAdManager>();

            _gameEventM3ManagerCollection = context.Resolve<GameEventMatch3ManagersCollection>();
            _raceEventMatch3Manager = context.Resolve<IRaceEventMatch3Manager>();
        }

        public LevelSuccessAdData GetLevelSuccessAdData()
        {
            _levelSuccessAdData.Clear();
            _levelSuccessAdData.AdWatchedCallback = AdWatchedHandler;

            var setupMethods = new Func<bool>[] { TrySetupSideMapEvent, TrySetupRaceEvent, TrySetupGameEvents };

            foreach (var setupMethod in setupMethods)
            {
                if (setupMethod())
                {
                    return _levelSuccessAdData;
                }
            }

            return _levelSuccessAdData;

            bool TrySetupSideMapEvent()
            {
                var screenType = _screensManager.GetCurrentScreenType();

                if ((screenType & ScreenType.SideMap) <= 0)
                {
                    return false;
                }

                var sideMapEvent = _gameEventManager.GetCurrentSideMapEvent();
                if (sideMapEvent == null)
                {
                    return false;
                }

                var gameEventMatch3Manager = _gameEventM3ManagerCollection.GetFirstManager(manager =>
                    manager.ActiveGameEvent != null && manager.ActiveGameEvent.Uid == sideMapEvent.Uid);

                return gameEventMatch3Manager != null && TryToSetGameEventData(gameEventMatch3Manager);
            }

            bool TrySetupRaceEvent()
            {
                if (!_raceEventMatch3Manager.IsAnyRaceEventOfType(RaceEventTypes.RaceEventType.Collect,
                        out var eventUid))
                {
                    return false;
                }

                var raceEvent = _raceEventManager.GetRaceEvent(eventUid);
                var score = _raceEventMatch3Manager.GetCollectEventScore();

                if (raceEvent == null || score <= 0)
                {
                    return false;
                }

                _levelSuccessAdData.AdPlacementUid = VideoAdManager.GetRaceEventCurrencyUid();
                _levelSuccessAdData.RaceEvent = raceEvent;
                _levelSuccessAdData.RaceEventMatch3Manager = _raceEventMatch3Manager;
                _levelSuccessAdData.Score = score;
                
                if (!_videoAdManager.IsAdAvailable(_levelSuccessAdData.AdEngagementSource, _levelSuccessAdData.AdPlacementUid))
                {
                    return false;
                }

                return true;
            }

            bool TrySetupGameEvents()
            {
                foreach (var gameEventMatch3Manager in _gameEventM3ManagerCollection)
                {
                    if (gameEventMatch3Manager == null || gameEventMatch3Manager.IsAlternateMapScreen)
                    {
                        continue;
                    }

                    if (TryToSetGameEventData(gameEventMatch3Manager))
                    {
                        return true;
                    }
                }

                return false;
            }

            bool TryToSetGameEventData(IGameEventMatch3Manager gameEventMatch3Manager)
            {
                var gameEvent = gameEventMatch3Manager.ActiveGameEvent;
                var score = gameEventMatch3Manager.LastCollectedScores;

                if (gameEvent == null || score <= 0)
                {
                    return false;
                }

                _levelSuccessAdData.AdPlacementUid = VideoAdManager.GetDoubleEventCurrencyUid(gameEventMatch3Manager.GetGameplayType());
                _levelSuccessAdData.GameEventBase = gameEvent;
                _levelSuccessAdData.GameEventMatch3Manager = gameEventMatch3Manager;
                _levelSuccessAdData.Score = score;
                
                if (!_videoAdManager.IsAdAvailable(_levelSuccessAdData.AdEngagementSource, _levelSuccessAdData.AdPlacementUid))
                {
                    return false;
                }

                return true;
            }
        }

        private void AdWatchedHandler()
        {
            var score = _levelSuccessAdData.Score;
            if (_levelSuccessAdData.RaceEventMatch3Manager != null)
            {
                _raceEventMatch3Manager.AddScore(score, true);
                _levelSuccessAdData.RaceEvent.SubmitScore(null);
            }
            else if (_levelSuccessAdData.GameEventMatch3Manager != null)
            {
                var gameEventMatch3Manager = _levelSuccessAdData.GameEventMatch3Manager;
                gameEventMatch3Manager.SubmitScore(score, true);
                var gameEventUid = gameEventMatch3Manager.ActiveGameEventUid;

                if (score <= 0 || gameEventUid.IsNullOrEmpty() ||
                    gameEventMatch3Manager.ActiveGameEvent.Status != GameEventStatus.Active) return;

                var level = _levelHolder.level;
                var gamePlayType = gameEventMatch3Manager.GetGameplayType();
                var eventScoreCurrencyUid = InventoryItems.GetGameEventScoreUid(gamePlayType);
                var gameEventScoreTransaction = new Transaction()
                    .AddTag(TransactionTag.LevelReward)
                    .SetAnalyticsData(CurrencyFlow.Level.Name, level.LevelUid, level.Stage.ToString())
                    .SetExtraData(gameEventUid)
                    .Earn(eventScoreCurrencyUid, score);
                _walletManager.TransactionController.MakeOnlyVisualTransaction(gameEventScoreTransaction);
            }
            else
            {
                BDebug.LogError(LogCat.Match3, $"Received AdWatchedHandler callback, but not event setup");
            }
        }
    }
}