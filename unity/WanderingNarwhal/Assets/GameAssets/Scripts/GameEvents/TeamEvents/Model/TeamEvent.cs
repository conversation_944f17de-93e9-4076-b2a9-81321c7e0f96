using System;
using System.Collections.Generic;
using BBB.BrainCloud;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.TeamCoopEventAnalytic;
using BBB.UI.Level;
using BebopBee;
using BebopBee.Core.UI;
using Beebopbee.Core.Extensions;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.GameEvents;
using GameAssets.Scripts.Player;
using PBConfig;
using PBGame;
using TeamEventConfig = FBConfig.TeamEventConfig;

namespace BBB.TeamEvents
{
    public class TeamEvent : IGameEventWithResources, IComparableEvent
    {
        private const string InTeamDescLocTextId = "TEAM_COOP_IN_TEAM_DESC";
        private const string NotInTeamDescLocTextId = "TEAM_COOP_NOT_IN_TEAM_DESC";
        private const string InTeamInfoDescriptionId = "TEAM_COOP_IN_TEAM_INFO_DESC";
        private const string EventTimerCompletedLocTextId = "EVENT_TIMER_COMPLETED_LOC";
        private const string EventTimerFinishedLocTextId = "FINISHED_LOC";
        private const string OpenTimerButtonLocTextId = "OPEN_BUTTON_LOC";
        private const string CompletedFinalizedLocTextId = "TEAM_COOP_COMPLETED_FINALIZED_LOC";
        private const string FinishedFinalizedLocTextId = "TEAM_COOP_FINISHED_FINALIZED_LOC";
        private const string TeamCoopLastFinalizedEpisodeTimestamp = "{0}_teamcoop_last_finalized_timestamp";

        protected static readonly Dictionary<string, int> EmptyRewardDict = new();

        public TeamEventConfig EventConfig;
        protected readonly GameNotificationManager NotificationManager;
        protected readonly IPlayerManager PlayerManager;
        protected readonly BrainCloudManager BrainCloudManager;
        protected readonly ILocalizationManager LocalizationManager;
        protected readonly ISchedulableDataProvider SchedulableDataProvider;
        protected readonly IModalsBuilder ModalsBuilder;
        protected readonly IAccountManager AccountManager;
        private readonly ITeamEventManager _teamEventManager;

        protected ITeamEventLeaderboard Leaderboard;
        protected PBTeamCoopEventState State;

        private readonly IConfig _config;
        private readonly IDictionary<string, ProgressionLevelConfig> _levelConfigDict;

        protected GenericEventStateProxy<PBTeamCoopEventState, TeamEventConfig> StateProxy => PlayerManager.Player.TeamEventStateProxy;
        public int PrevShownTeamTotalScore => State?.PrevShownTeamScore ?? 0;

        public IList<MilestoneState> CurrentMilestoneStates => State?.MilestoneStates;
        public IList<MilestoneState> PrevShownMilestoneStates => State?.PrevShownMilestoneStates;

        public int CurrentTeamTotalScore => IsLaunched_Internal() ? State.CurrentTeamScore : 0;

        public int CurrentOwnScore => IsLaunched_Internal() ? State.CurrentOwnScore : 0;

        public int CurrentSubmittedOwnScore => IsLaunched_Internal() ? State.CurrentSubmittedOwnScore : 0;

        private int MaxScore
        {
            get
            {
                if (EventConfig.MilestoneConfigsLength <= 0)
                    return int.MaxValue;
                
                return EventConfig.MilestoneConfigs(EventConfig.MilestoneConfigsLength - 1)?.Scores ?? int.MaxValue;
            }
        }

        public int LastAddedScoreDeltaToShow => State?.LastAddedScoreDeltaToShow ?? 0;

        public Action<(string eventUid, bool released)> OnAfterViewingStateUpdate;
        public string Uid => EventConfig.Uid;
        public string EventResourceId => EventConfig.Uid.RemoveDigits();
        public bool Joined => State is { Joined: true };
        public bool Finalized => State is { Finalized: true };

        public string MainTitle => LocalizationManager.getLocalizedText(EventConfig.MainTitleLocTextId);

        public int CurrentMilestoneMaxScore => GetMilestoneScore(GetMilestoneIndexForScore(CurrentTeamTotalScore));

        public TeamCoopLeaderboardData LeaderboardData => Leaderboard.LeaderboardData;

        private int LastFinalizedEpisodeTimestamp
        {
            get
            {
                var key = string.Format(TeamCoopLastFinalizedEpisodeTimestamp, Uid);
                return PlayerManager?.Player?.GameEventStateProxy.GetPersistentInteger(key) ?? -1;
            }
            set
            {
                var key = string.Format(TeamCoopLastFinalizedEpisodeTimestamp, Uid);
                PlayerManager?.Player?.GameEventStateProxy.SetPersistentInteger(key, value);
            }
        }

        public bool Finished
        {
            get
            {
                if (IsLaunched_Internal())
                {
                    return GetTimeLeft().TotalSeconds <= 0;
                }

                //we treat not launched as finished but in general case if event is not launched it should not show the icon at all
                return true;
            }
        }

        public bool Completed
        {
            get
            {
                if (IsLaunched_Internal())
                {
                    if (LeaderboardData == null)
                    {
                        return PrevShownTeamTotalScore >= MaxScore;
                    }

                    return CurrentTeamTotalScore >= MaxScore;
                }

                return false;
            }
        }

        public int GetCurrentScore(TeamEventLeaderboardType teamEventLeaderboardType)
        {
            if (IsLaunched_Internal())
            {
                switch (teamEventLeaderboardType)
                {
                    case TeamEventLeaderboardType.MyTeam:
                        return State?.CurrentOwnScore ?? 0;
                    case TeamEventLeaderboardType.Battle:
                        return State?.CurrentTeamScore ?? 0;
                }
            }

            return 0;
        }

        public void SetSubmittedScore(int score)
        {
            State.CurrentSubmittedOwnScore = score;
        }

        public virtual void TryFinalizeEvent()
        {
        }

        public virtual string GetContributionRewardText()
        {
            return null;
        }

        public int GetPrevShownOwnScore(TeamEventLeaderboardType teamEventLeaderboardType)
        {
            if (IsLaunched_Internal())
            {
                switch (teamEventLeaderboardType)
                {
                    case TeamEventLeaderboardType.MyTeam:
                        return State?.PrevShownOwnScore ?? 0;
                    case TeamEventLeaderboardType.Battle:
                        return State?.PrevShownTeamScore ?? 0;
                }
            }

            return 0;
        }

        public virtual void OpenMainModal(ShowMode showMode, bool isFinalState = false)
        {
        }

        public int PrevShownOwnIndex(TeamEventLeaderboardType teamEventLeaderboardType)
        {
            if (IsLaunched_Internal())
            {
                switch (teamEventLeaderboardType)
                {
                    case TeamEventLeaderboardType.MyTeam:
                        return State.PrevShownOwnLeaderboardIndex;
                    case TeamEventLeaderboardType.Battle:
                        return State.PrevShownOwnTeamLeaderboardIndex;
                }
            }

            return 0;
        }

        protected TeamEvent(IContext context, TeamEventConfig eventConfig, ISchedulableDataProvider schedulableDataProvider)
        {
            EventConfig = eventConfig;
            SchedulableDataProvider = schedulableDataProvider;

            NotificationManager = context.Resolve<GameNotificationManager>();
            PlayerManager = context.Resolve<IPlayerManager>();
            BrainCloudManager = context.Resolve<BrainCloudManager>();

            State = StateProxy.GetEventState(EventConfig.Uid);

            _teamEventManager = context.Resolve<ITeamEventManager>();
            _config = context.Resolve<IConfig>();
            _levelConfigDict = _config.Get<ProgressionLevelConfig>();

            LocalizationManager = context.Resolve<ILocalizationManager>();
            ModalsBuilder = context.Resolve<IModalsBuilder>();

            AccountManager = context.Resolve<IAccountManager>();
        }

        public virtual void OnTeamChanged()
        {
        }

        public virtual int GetTopTeamRewardsCount()
        {
            return 0;
        }

        public virtual string GetInfoModeDescription()
        {
            return LocalizationManager.getLocalizedText(InTeamInfoDescriptionId);
        }

        public virtual string GetDescription(bool isInTeam)
        {
            return LocalizationManager.getLocalizedTextWithArgs(isInTeam ? InTeamDescLocTextId : NotInTeamDescLocTextId, MainTitle);
        }

        public bool ShouldKeepResourceLoaded()
        {
            return IsLaunched_Internal();
        }

        public bool IsLaunched()
        {
            return IsLaunched_Internal();
        }
        
        public bool WasNotIntroduced()
        {
            return State is { WasIntroduced: false };
        }

        public void MarkAsIntroduced()
        {
            if (State != null)
            {
                State.WasIntroduced = true;
            }
        }
        
        public int GetNextToBeReachedMilestone()
        {
            if (IsLaunched_Internal())
            {
                for (var i = 0; i < State.MilestoneStates.Count; i++)
                {
                    if (State.MilestoneStates[i] == MilestoneState.ToBeReached)
                    {
                        return i;
                    }
                }
            }
            return -1;
        }

        public TimeSpan GetTimeLeft(string arg = null)
        {
            if (IsLaunched_Internal())
            {
                var lastStartTime = SchedulableConfigUtils.GetLastStartTime(EventConfig, SchedulableDataProvider);

                //if start time is a lot different then we release the event
                return Math.Abs(lastStartTime.ToUnixTimeSeconds() - State.CurrentStartTimestamp) > 0.01f ? TimeSpan.FromSeconds(0) : SchedulableConfigUtils.GetTimeLeft(EventConfig, SchedulableDataProvider, arg);
            }

            return TimeSpan.FromSeconds(0);
        }

        public int CompareTo(IComparableEvent otherEvent)
        {
            if (otherEvent is not TeamEvent)
            {
                return -1;
            }

            return string.Compare(Uid, otherEvent.Uid, StringComparison.Ordinal);
        }

        protected bool IsLaunched_Internal()
        {
            return State != null && State.CurrentStartTimestamp != 0;
        }

        public void ProcessState(float highestPassedSortOrder)
        {
            if (IsReadyToLaunch())
            {
                var requiredLevelSortOrder = LevelHelper.GetLevelSortOrder(_config, EventConfig.RequiredLevelUid);

                if (highestPassedSortOrder >= requiredLevelSortOrder)
                {
                    Launch();
                }
            }
            else if (IsReadyToRelease())
            {
                Release();
            }
            else if (IsLaunched_Internal())
            {
                if (!State.Joined && AccountManager.IsInTeam)
                    JoinIfLaunched();

                if (State.Joined && !AccountManager.IsInTeam)
                    OnTeamChanged();
            }
        }

        protected void CreateNewTeamEventState()
        {
            var lastStartTime = SchedulableConfigUtils.GetLastStartTime(EventConfig, SchedulableDataProvider);
            State = StateProxy.GetEventState(EventConfig.Uid);
            if (State != null) return;

            State = StateProxy.CreateEventState(EventConfig.Uid);
            State.CurrentStartTimestamp = (int)lastStartTime.ToUnixTimeSeconds();
            State.LocalLeaderboardVersion = -1;
            State.PrevShownOwnLeaderboardIndex = -1;
            State.PrevShownOwnTeamLeaderboardIndex = -1;
        }

        protected virtual void Launch()
        {
            CreateNewTeamEventState();
            InitializeMilestones();
            var notifier = NotificationManager.GetTeamCoopEventNotifier(EventConfig.Uid);
            notifier?.SetNotifier(1);
            PlayerManager.MarkDirty();
            if (AccountManager.IsInTeam)
            {
                JoinIfLaunched();
                FetchLeaderboardData();
            }
        }

        protected virtual void InitializeMilestones()
        {
        }

        public void DebugRelease()
        {
            Release();
        }

        protected void Release()
        {
            var notifier = NotificationManager.GetTeamCoopEventNotifier(EventConfig.Uid);
            notifier?.ResetNotifier();
            StateProxy.ReleaseEventState(EventConfig.Uid);
            PlayerManager.MarkDirty();
            Leaderboard.Release();
            State = null;
            OnAfterViewingStateUpdate?.Invoke((EventConfig.Uid, true));
        }

        public bool IsReadyToRelease()
        {
            if (IsLaunched_Internal() && ConnectivityStatusManager.ConnectivityReachable)
            {
                var currentTime = SchedulableDataProvider.GetCurrentUtcDateTime();
                bool withinTimeSpan = DoesMyTimeSpanContain(currentTime);

                var lastStartTime = SchedulableConfigUtils.GetLastStartTime(EventConfig, SchedulableDataProvider);

                //if start time is a lot different then we release the event
                if (Math.Abs(lastStartTime.ToUnixTimeSeconds() - State.CurrentStartTimestamp) > 0.01f)
                {
                    if (State.Finalized || !State.Joined)
                    {
                        return true;
                    }
                }

                if (State.Finalized)
                {
                    return true;
                }

                return !withinTimeSpan && !State.Joined;
            }

            return false;
        }

        protected virtual bool IsReadyToLaunch()
        {
            if (IsLaunched_Internal() || !ConnectivityStatusManager.ConnectivityReachable)
                return false;

            var lastStartTime = (int)SchedulableConfigUtils.GetLastStartTime(EventConfig, SchedulableDataProvider).ToUnixTimeSeconds();

            if (LastFinalizedEpisodeTimestamp == lastStartTime)
            {
                return false;
            }

            var currentTime = SchedulableDataProvider.GetCurrentUtcDateTime();
            var withinTimeSpan = DoesMyTimeSpanContain(currentTime);

            return withinTimeSpan;
        }

        public bool CanSendScore()
        {
            var currentTime = SchedulableDataProvider.GetCurrentUtcDateTime();
            if (IsLaunched_Internal() && State.Joined && DoesMyTimeSpanContain(currentTime))
            {
                var lastStartTime = SchedulableConfigUtils.GetLastStartTime(EventConfig, SchedulableDataProvider);
                return Math.Abs(lastStartTime.ToUnixTimeSeconds() - State.CurrentStartTimestamp) <= 0.01f;
            }

            return false;
        }

        protected bool DoesMyTimeSpanContain(DateTime dateTime)
        {
            return SchedulableConfigUtils.DoesMyTimeSpanContain(EventConfig, SchedulableDataProvider, dateTime);
        }

        public bool ShouldShowIcon()
        {
            if (IsLaunched_Internal())
            {
                if (State.Joined)
                {
                    return true;
                }

                //check for cooldown since last round
                var currentTime = SchedulableDataProvider.GetCurrentUtcDateTime();

                if (DoesMyTimeSpanContain(currentTime) && !State.TeamChangedDuringCurrentPeriod)
                {
                    return true;
                }
            }

            return false;
        }

        public void JoinIfLaunched()
        {
            if (IsLaunched_Internal())
            {
                Analytics.LogEvent(new TeamCoopEventAnalyticsRoundStarted(Uid, AccountManager.Profile?.CurrentTeam));
                State.Joined = true;
                var notifier = NotificationManager.GetTeamCoopEventNotifier(EventConfig.Uid);
                notifier?.ResetNotifier();
                PlayerManager.MarkDirty();
            }
        }

        public virtual void FetchTeamEvent(string eventUid, string localLeaderboardId, int localLeaderboardVersion,
            Action<BCTeamEventResponse> success, Action failure)
        {
        }

        public virtual void SubmitTeamEventScore(string eventUid, int score, Action<BCTeamEventResponse> success, Action failure)
        {
        }

        public bool ShouldAnimateTeamProgress()
        {
            if (!IsLaunched_Internal())
            {
                BDebug.LogError(LogCat.Events, "Event is not launched while checking for ShouldAnimateProgress");
                return false;
            }

            if (State.Finalized)
            {
                return false;
            }

            return State.CurrentTeamScore > State.PrevShownTeamScore;
        }

        public int GetScoreValue(string levelUid)
        {
            if (_levelConfigDict.TryGetValue(levelUid, out var levelConfig))
            {
                var difficulty = levelConfig.Difficulty;

                if (EventConfig.ScoreForLevelDifficultyLength > 0)
                {
                    if (difficulty < 0)
                    {
                        difficulty = 0;
                    }

                    if (difficulty >= EventConfig.ScoreForLevelDifficultyLength)
                    {
                        difficulty = EventConfig.ScoreForLevelDifficultyLength - 1;
                    }

                    return EventConfig.ScoreForLevelDifficulty(difficulty);
                }

                BDebug.LogError(LogCat.Events, $"TeamCoopEvent: difficulty score map not found for {Uid}");
            }
            else
            {
                BDebug.LogError(LogCat.Events, $"TeamCoopEvent: {levelUid} not found");
            }

            return 0;
        }

        public string GetFinalizedText()
        {
            if (Finalized)
            {
                if (Completed)
                {
                    return LocalizationManager.getLocalizedTextWithArgs(CompletedFinalizedLocTextId, MainTitle);
                }

                if (Finished)
                {
                    return LocalizationManager.getLocalizedTextWithArgs(FinishedFinalizedLocTextId, MainTitle);
                }
            }

            return null;
        }

        public void DebugSetScore(int score)
        {
            if (!IsLaunched_Internal())
            {
                BDebug.LogError(LogCat.Events, "Event is not launched");
                return;
            }

            State.CurrentOwnScore = score;
        }

        public void DebugAddScore(int scoreDelta)
        {
            if (!IsLaunched_Internal())
            {
                BDebug.LogError(LogCat.Events, "Event is not launched");
                return;
            }

            State.CurrentOwnScore += scoreDelta;
        }

        public bool AddScore(string levelUid)
        {
            var scoreDelta = GetScoreValue(levelUid);
            if (scoreDelta > 0 && CanSendScore())
            {
                State.CurrentOwnScore += scoreDelta;
                State.LastAddedScoreDeltaToShow = scoreDelta;
                return true;
            }

            return false;
        }

        public async UniTask SubmitScore(Action<bool> dataSubmitCallback)
        {
            if (!IsLaunched_Internal())
            {
                BDebug.LogError(LogCat.Events, "Event is not launched");
                return;
            }

            if (State is { LocalLeaderboardVersion: 0 })
            {
                State.LocalLeaderboardVersion = -1;
            }

            var leaderboardId = Uid;
            var leaderboardData = await Leaderboard.SubmitScore(State.CurrentOwnScore, leaderboardId);
            ProcessNewData(leaderboardData);
            dataSubmitCallback?.Invoke(true);
        }

        public async UniTask FetchData(Action<TeamCoopLeaderboardData, bool> dataFetchedCallback)
        {
            if (!IsLaunched_Internal()) return;

            var eventUid = Uid;
            if (State is { LocalLeaderboardVersion: 0 })
            {
                State.LocalLeaderboardVersion = -1;
            }

            var localLeaderboardVersion = State?.LocalLeaderboardVersion ?? -1;
            var localLeaderboardId = State?.LocalLeaderboardId ?? Uid;
            var leaderboardData = await Leaderboard.FetchData(eventUid, localLeaderboardId, localLeaderboardVersion);
            if (leaderboardData != null)
            {
                ProcessNewData(leaderboardData);
                dataFetchedCallback?.Invoke(leaderboardData, true);
            }
            else
            {
                dataFetchedCallback?.Invoke(null, false);
            }
        }

        protected virtual void ProcessNewData(TeamCoopLeaderboardData leaderboardData)
        {
            if (!IsLaunched_Internal()) return;

            if (Finished || Completed)
            {
                State.CurrentOwnScore = State.CurrentSubmittedOwnScore;
            }

            State.CurrentTeamScore = leaderboardData.TeamTotalScore;

            if (State.CurrentTeamScore > MaxScore)
            {
                State.CurrentTeamScore = MaxScore;
            }

            State.LocalLeaderboardVersion = leaderboardData.LocalLeaderboardVersion;
            State.LocalLeaderboardId = leaderboardData.LocalLeaderboardId;

            PlayerManager.MarkDirty();
        }


        public virtual int GetScoreLeftToMilestone(int index)
        {
            return -1;
        }

        public virtual Dictionary<string, int> GetMilestoneRewardForIndex(int index)
        {
            return null;
        }

        /// <summary>
        /// initial index is 0 and the last one is 3
        /// </summary>
        /// <param name="score"></param>
        /// <returns></returns>
        public int GetMilestoneIndexForScore(int score)
        {
            var result = 0;
            var found = false;
            for (var i = 0; i < EventConfig.MilestoneConfigsLength; i++)
            {
                var mileStoneConfig = EventConfig.MilestoneConfigs(i);
                
                if(!mileStoneConfig.HasValue)
                    continue;

                found = true;
                if (mileStoneConfig.Value.Scores <= score)
                {
                    result++;
                }
            }
            return !found ? -1 : result;
        }

        public bool HasMilestoneProgress()
        {
            if (IsLaunched_Internal())
            {
                return GetMilestoneIndexForScore(State.CurrentTeamScore) > GetMilestoneIndexForScore(State.PrevShownTeamScore);
            }

            return false;
        }

        public bool HasAnyRewardsToCollect()
        {
            return HasMilestoneRewardsToCollect(-1) || HasContributionRewardToCollect() || HasTeamVsTeamRewardToCollect();
        }

        //-1 meaning any milestone reward
        public virtual bool HasMilestoneRewardsToCollect(int milestoneArrayIndex)
        {
            return false;
        }

        public void GetReadyToCollectMilestones(List<int> mileStonesToCollect)
        {
            for (var i = 0; i < State.MilestoneStates.Count; i++)
            {
                if (State.MilestoneStates[i] == MilestoneState.ReachedAndReadyToCollect)
                {
                    mileStonesToCollect.Add(i);
                }
            }
        }

        public int GetHighestReadyToCollectMilestoneIndex()
        {
            var highestIndex = int.MinValue;
            for (var i = 1; i < State.MilestoneStates.Count + 1; i++)
            {
                var mileStoneArrayIndex = i - 1;
                
                if (State.MilestoneStates[mileStoneArrayIndex] != MilestoneState.ReachedAndReadyToCollect)
                    continue;
                
                if (i > highestIndex)
                {
                    highestIndex = i;
                }
            }

            return highestIndex;
        }

        public bool HasContributionRewardToCollect()
        {
            if ((!Finished && !Completed) || State is { ContributionRewardCollected: true } || LeaderboardData == null)
                return false;
            
            var ownRank = -1;
            var ownScore = 0;
            foreach (var entry in LeaderboardData.TeamLeaderboardEntries)
            {
                if (entry.PlayerId != BrainCloudManager.ProfileId)
                    continue;
                
                ownRank = entry.Rank;
                ownScore = entry.Score;
                break;
            }

            if (ownRank <= 0 || ownScore <= 0)
                return false;
            
            var ownRankIndex = ownRank - 1;
            return ownRankIndex < EventConfig.TopPlayersRewardsLength;
        }

        public virtual bool HasTeamVsTeamRewardToCollect()
        {
            return false;
        }

        public virtual TeamVsTeamLeaderboardEntryData GetOwnTeamDetails()
        {
            return null;
        }

        //-1 means claim all
        public virtual Dictionary<string, int> GetMilestoneRewards(int milestoneArrayIndexToClaim)
        {
            return EmptyRewardDict;
        }

        public virtual void MarkMilestonesCollected(int milestoneArrayIndex)
        {
        }

        public void MarkContributionRewardCollected()
        {
            if (IsLaunched_Internal())
            {
                State.ContributionRewardCollected = true;
            }
        }

        public void MarkTeamVsTeamRewardCollected()
        {
            if (IsLaunched_Internal())
            {
                State.TeamVsTeamRewardCollected = true;
            }
        }

        public int GetOwnRankIndex()
        {
            if ((!Finished && !Completed) || LeaderboardData == null)
                return -1;
            
            foreach (var entry in LeaderboardData.TeamLeaderboardEntries)
            {
                if (entry.PlayerId == BrainCloudManager.ProfileId)
                {
                    return entry.Rank - 1;
                }
            }

            return -1;
        }

        public int GetOwnTeamRankIndex()
        {
            if ((!Finished && !Completed) || LeaderboardData is not { TeamVsTeamLeaderboardEntries: not null })
                return -1;
            
            foreach (var entry in LeaderboardData.TeamVsTeamLeaderboardEntries)
            {
                if (entry.TeamUid == AccountManager.Profile.CurrentTeam.TeamUid)
                {
                    return entry.TeamRank - 1;
                }
            }

            return -1;
        }

        public Dictionary<string, int> GetContributionReward()
        {
            var alreadyCollected = State is { ContributionRewardCollected: true };
            if (alreadyCollected)
            {
                return null;
            }

            if ((!Finished && !Completed) || LeaderboardData == null)
                return EmptyRewardDict;
            
            var ownRank = -1;
            var ownScore = 0;
            foreach (var entry in LeaderboardData.TeamLeaderboardEntries)
            {
                if (entry.PlayerId != BrainCloudManager.ProfileId)
                    continue;
                
                ownRank = entry.Rank;
                ownScore = entry.Score;
                break;
            }

            if (ownRank <= 0 || ownScore <= 0)
                return EmptyRewardDict;
            
            var ownRankIndex = ownRank - 1;
            return ownRankIndex < EventConfig.TopPlayersRewardsLength ? RewardsUtility.RewardStringToDict(EventConfig.TopPlayersRewards(ownRankIndex)).FilterRewards() : EmptyRewardDict;
        }

        public virtual Dictionary<string, int> GetTeamVsTeamReward()
        {
            return EmptyRewardDict;
        }

        protected bool ReadyToFinalize()
        {
            return State is { Joined: true } && (Finished || Completed) && !HasAnyRewardsToCollect();
        }

        public void TryFinalize()
        {
            if (ReadyToFinalize() && !State.Finalized)
            {
                Analytics.LogEvent(new TeamCoopEventAnalyticsRoundEnded(Uid, State.CurrentTeamScore, State.CurrentOwnScore, AccountManager.Profile?.CurrentTeam));
                State.Finalized = true;
                LastFinalizedEpisodeTimestamp = State.CurrentStartTimestamp;
            }
        }

        public void ProcessAfterViewing()
        {
            if (IsLaunched_Internal())
            {
                State.PrevShownTeamScore = CurrentTeamTotalScore;
                State.PrevShownOwnScore = CurrentOwnScore;
                UpdateMilestones();
            }

            var notifier = NotificationManager.GetTeamCoopEventNotifier(EventConfig.Uid);

            if (((Finished || Completed) && HasAnyRewardsToCollect()) || Finalized)
            {
                notifier?.SetNotifier(1, 1);
            }
            else
            {
                notifier?.ResetNotifier();
            }
        }

        public void FetchLeaderboardData(bool silent = false)
        {
            if (State is not { Joined: true } || (silent && Leaderboard.FetchingDataNow)) return;

            FetchData((_, _) =>
            {
                if (HasAnyRewardsToCollect())
                {
                    var notifier = NotificationManager.GetTeamCoopEventNotifier(EventConfig.Uid);
                    notifier?.SetNotifier(1, 1);
                }

                TryFinalize();
                TryLeaveAndRelease();
            }).Forget();
        }

        protected virtual void UpdateMilestones()
        {
        }

        public static bool CanShowInScreen(ScreenType screenType)
        {
            return true;
        }

        public void TryLeaveAndRelease()
        {
            if (IsReadyToRelease())
            {
                Release();
            }
            else
            {
                OnAfterViewingStateUpdate?.Invoke((EventConfig.Uid, false));
            }
        }

        public string GetFinishedTimerText()
        {
            return HasAnyRewardsToCollect() ? OpenTimerButtonLocTextId : EventTimerFinishedLocTextId;
        }

        public string GetCompletedTimerText()
        {
            return HasAnyRewardsToCollect() ? OpenTimerButtonLocTextId : EventTimerCompletedLocTextId;
        }

        public void ClearLastAddedScoreDeltaToShow()
        {
            if (State != null)
            {
                State.LastAddedScoreDeltaToShow = 0;
            }
        }

        public virtual bool ShouldBeAutoShown(EventAutoshowCondition condition)
        {
            return false;
        }

        public Dictionary<string, int> GetContributionRewardBasedOnRankIndex(int rankIndex)
        {
            return rankIndex < EventConfig.TopPlayersRewardsLength ? RewardsUtility.RewardStringToDict(EventConfig.TopPlayersRewards(rankIndex)).FilterRewards() : EmptyRewardDict;
        }

        public virtual Dictionary<string, int> GetTeamRewardBasedOnRankIndex(int rankIndex)
        {
            return EmptyRewardDict;
        }

        public void SetLastShownIndex(int index, TeamEventLeaderboardType teamEventLeaderboardType)
        {
            if (State != null)
            {
                switch (teamEventLeaderboardType)
                {
                    case TeamEventLeaderboardType.MyTeam:
                        State.PrevShownOwnLeaderboardIndex = index;
                        break;
                    case TeamEventLeaderboardType.Battle:
                        State.PrevShownOwnTeamLeaderboardIndex = index;
                        break;
                }
            }
        }

        public int GetMilestoneScore(int milestoneIndex)
        {
            switch (milestoneIndex)
            {
                case >= 0:
                {
                    if (EventConfig.MilestoneConfigsLength > milestoneIndex)
                    {
                        var milestoneConfig = EventConfig.MilestoneConfigs(milestoneIndex);
                    
                        if(milestoneConfig.HasValue)
                            return milestoneConfig.Value.Scores;
                    }

                    if (EventConfig.MilestoneConfigsLength == milestoneIndex)
                    {
                        return MaxScore;
                    }

                    break;
                }
                case < 0:
                    return 0;
            }

            BDebug.LogError(LogCat.Events, $"Milestone with index: {milestoneIndex} is not found in the config");
            return MaxScore;
        }

        public float GetUnlockLevelSortOrder()
        {
            return LevelHelper.GetLevelSortOrder(_config, EventConfig.RequiredLevelUid);
        }

        public GameEventCurrencyFlowData GetContributionRewardCurrencyFlowAnalyticsData(int placeIndex)
        {
            var result = new GameEventCurrencyFlowData
            {
                Category = CurrencyFlow.GameEvents.Name,
                Family = Uid,
                Item = string.Format(AnalyticNames.ChestProductId, placeIndex + 1)
            };

            return result;
        }

        public GameEventCurrencyFlowData GetMilestoneRewardCurrencyFlowAnalyticsData(int milestoneArrayIndexToClaim)
        {
            var result = new GameEventCurrencyFlowData
            {
                Category = CurrencyFlow.GameEvents.Name,
                Family = Uid,
                Item = string.Format(AnalyticNames.MilestoneProductId, milestoneArrayIndexToClaim + 1)
            };

            return result;
        }

        public GameEventCurrencyFlowData GetTeamVsTeamRewardCurrencyFlowAnalyticsData(int placeIndex)
        {
            var result = new GameEventCurrencyFlowData
            {
                Category = CurrencyFlow.GameEvents.Name,
                Family = Uid,
                Item = string.Format(AnalyticNames.TeamVsTeamReward, placeIndex + 1)
            };
            return result;
        }
    }
}