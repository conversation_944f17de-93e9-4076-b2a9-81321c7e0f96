using System.Collections.Generic;
using BebopBee.Core.UI;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.GameEvents;

namespace BBB.TeamEvents
{
    public interface ITeamEventManager
    {
        public IEnumerable<TeamEvent> Events { get; }
        IEnumerable<(string eventUid, int score)> GetLastDeltaScoreTuples();
        void ClearLastDeltaScores();
        TeamEvent GetHighestPriorityEvent();
        TeamEvent GetEvent(string eventUid);
        void ProcessEventStates();
        bool TryToShowEvent(TeamEvent ev, bool shouldProcess);
        void IncrementScores(string eventUid, int score);
        void DebugReleaseEvent(string eventUid);
        void DebugSetScore(string eventUid, int score);
        void TryCollectTeamCoopRewards(TeamEvent teamEvent, int milestoneArrayIndexToClaim);
        void TryCollectTeamVsTeamRewards(TeamEvent teamEvent);
        void ClaimContributionReward(TeamEvent teamEvent);
        bool ShouldAutoShow(EventAutoshowCondition condition);
        bool TryAutoShowEvent(EventAutoshowCondition condition);
        void EnterLevelFlow();
        void ResetTeamEvents();
        void ShowIntroModal(TeamEvent teamEvent, bool infoMode, ShowMode showMode);
    }
}