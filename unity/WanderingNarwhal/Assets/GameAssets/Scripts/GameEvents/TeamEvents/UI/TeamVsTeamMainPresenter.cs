using System;
using BBB.Core;
using BBB.DI;
using BBB.RaceEvents.UI;
using BBB.Screens;
using BBB.UI;
using BebopBee.Core;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using GameAssets.Scripts.SocialScreens.Teams.NoTeam;
using TMPro;
using UniRx;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.TeamEvents.UI
{
    public class TeamVsTeamMainPresenter : ModalsViewPresenter, ITeamCoopMainPresenter
    {
        private const string TeamCoopMainModalBgSprite = "teamcoop_mainmodal_bg";
        private const string TeamVsTeamFinishedStateBgSprite = "teamcoop_mainmodal_finished_bg";
        private const string RewardClaimButtonText = "CLAIMREWARD_CLAIM_BUTTON";
        private const string ContinueButtonText = "KEEP_PLAYING_QUEST_FLOW";
        private const string EventPictureStringFormat = "main_event_picture";

        public event Action InfoButtonHandler;
        public event Action<int> ChestButtonHandler;
        public event Action ContinueButtonHandler;
        public event Action LogoButtonHandler;

        [SerializeField] private Button _infoButton;
        [SerializeField] private Button _continueButton;
        [SerializeField] private Button _multiUseButton;
        [SerializeField] private TextMeshProUGUI[] _headerText;
        [SerializeField] private ClockCountdownText _clockCountdownText;
        [SerializeField] private TeamCoopEventLeaderboardScrollView _scrollView;
        [SerializeField] private TeamVsTeamEventLeaderboardScrollView _teamScrollView;
        [SerializeField] private GameObject _normalState;
        [SerializeField] private GameObject _finishedState;
        [SerializeField] private Image _defaultStateImage;
        [SerializeField] private Image _finishedStateImage;
        [Header("Time Constants")]
        [SerializeField] private float _progressBarAnimDuration = 1f;

        [Header("Ease Constants")]
        [SerializeField] private Ease _progressBarAnimEase = Ease.OutCubic;

        [SerializeField] private TabButton _myTeamTab;
        [SerializeField] private TabButton _battleTab;
        [SerializeField] private GameObject _claimButtonHolder;
        [SerializeField] private GameObject _continueButtonHolder;
        [SerializeField] private int _defaultPosition = 64;
        [SerializeField] private int _claimPosition = 165;
        [SerializeField] private UIRewardComponent _rewardInfo;
        [SerializeField] private GameObject _noRewardState;
        [SerializeField] private LocalizedTextPro _finalButtonState;
        [SerializeField] private Image _gameIcon;
        [SerializeField] private TextMeshProUGUI _rank;
        [SerializeField] private EventPictureRenderer _eventPictureRenderer;

        private bool EventFinished => _teamEvent.Finalized || _teamEvent.Finished;

        private IGameEventResourceManager _gameEventResourceManager;
        private ILocalizationManager _localizationManager;
        private TeamVsTeamNudgeManager _teamVsTeamNudgeManager;

        private bool _leftProgressBarPaintedGray;
        private TeamEvent _teamEvent;
        private TeamCoopLeaderboardData _leaderboardData;
        private Tweener _updateViewTweener;
        private bool _waitingForData;

        protected override void OnContextInitialized(IContext context)
        {
            base.OnContextInitialized(context);
            _scrollView.Init(context);
            _teamScrollView.Init(context);
            _gameEventResourceManager = context.Resolve<IGameEventResourceManager>();
            _localizationManager = context.Resolve<ILocalizationManager>();
            _teamVsTeamNudgeManager = new TeamVsTeamNudgeManager();
            _teamVsTeamNudgeManager.InitializeByContext(context);

            _infoButton.ReplaceOnClick(OnInfoButton);

            _scrollView.SetupNudge(_teamVsTeamNudgeManager);

            _continueButton.ReplaceOnClick(OnContinueButton);

            _myTeamTab.ReplaceOnClick(LoadMyTeam);
            _battleTab.ReplaceOnClick(LoadTeamBattle);
        }

        private void OnInfoButton()
        {
            InfoButtonHandler?.Invoke();
        }

        private void OnContinueButton()
        {
            ContinueButtonHandler?.Invoke();
        }

        private void OnClaimButton()
        {
            ChestButtonHandler?.Invoke(0);
            OnContinueButton();
        }

        protected override void OnHide()
        {
            base.OnHide();

            if (_eventPictureRenderer != null)
                _eventPictureRenderer.HideEventPicture(_teamEvent.EventResourceId, EventPictureStringFormat);

            _scrollView.CacheLastShownIndex(TeamEventLeaderboardType.MyTeam);
            _scrollView.Clear();

            _teamScrollView.CacheLastShownIndex(TeamEventLeaderboardType.Battle);
            _teamScrollView.Clear();

            _leaderboardData = null;
            _waitingForData = false;
        }

        public void Refresh(TeamEvent teamEvent)
        {
            _teamEvent = teamEvent;
            _teamVsTeamNudgeManager.SetupTeamEvent(teamEvent);

            foreach (var headerText in _headerText)
            {
                headerText.SetText(teamEvent.MainTitle);
            }

            if (_defaultStateImage != null)
            {
                _gameEventResourceManager
                    .GetSpriteAsync(teamEvent.Uid, TeamCoopMainModalBgSprite)
                    .ContinueWith(sprite =>
                    {
                        if (sprite == null)
                        {
                            BDebug.LogError(LogCat.Events,$"Couldn't load default state bg for event {teamEvent.Uid}");
                        }
                        _defaultStateImage.sprite = sprite;
                    });
            }

            if (_finishedStateImage != null)
            {
                _gameEventResourceManager
                    .GetSpriteAsync(teamEvent.Uid, TeamVsTeamFinishedStateBgSprite)
                    .ContinueWith(sprite =>
                    {
                        if (sprite == null)
                        {
                            BDebug.LogError(LogCat.Events,$"Couldn't load finished state bg for event {teamEvent.Uid}");
                        }
                        _finishedStateImage.sprite = sprite;
                    });
            }

            if (_gameIcon != null)
            {
                _gameEventResourceManager
                    .GetSpriteAsync(teamEvent.Uid, GameEventResKeys.TeamCoopGameEventIcon)
                    .ContinueWith(sprite =>
                    {
                        if (sprite == null)
                        {
                            BDebug.LogError(LogCat.Events,$"Couldn't load game icon for event {teamEvent.Uid}");
                        }
                        _gameIcon.sprite = sprite;
                    });
            }

            if (_eventPictureRenderer != null)
            {
                _eventPictureRenderer.RefreshFromConfig<TeamCoopEventVisualConfig>(
                    teamEvent.EventResourceId,
                    EventPictureStringFormat,
                    GameEventResKeys.TeamCoopGameEventSettings);
            }

            if (teamEvent.Finished)
            {
                _clockCountdownText.SetAsFinished(_localizationManager);
            }
            else
            {
                _clockCountdownText.Init(_localizationManager, _ => teamEvent.GetTimeLeft());
                var timeLeftInSeconds = (float)teamEvent.GetTimeLeft().TotalSeconds + Mathf.Epsilon;
                _updateViewTweener?.Kill();
                _updateViewTweener = Rx.Invoke(timeLeftInSeconds, _ => LoadData());
            }

            _infoButton.gameObject.SetActive(true);

            LoadData();
        }

        private void LoadData()
        {
            _waitingForData = _teamEvent.LeaderboardData == null || EventFinished;
            if (_waitingForData)
            {
                _teamEvent.FetchData(OnDataFetched).Forget();
                return;
            }

            SetUpStates();
            OnDataFetched(_teamEvent.LeaderboardData, true);
        }

        private void SetUpStates()
        {
            var hasContributionReward = _teamEvent.HasContributionRewardToCollect();
            var hasTeamVsTeamReward = _teamEvent.HasTeamVsTeamRewardToCollect();

            var userTeamData = _teamEvent.GetOwnTeamDetails();
            if (userTeamData != null)
            {
                _rank.text = "#" + userTeamData.TeamRank;
                _rewardInfo.SetupReward(userTeamData.RewardDictionary);
            }

            if (EventFinished)
            {
                _infoButton.gameObject.SetActive(false);

                if (hasTeamVsTeamReward)
                {
                    LoadTeamBattle();

                    _normalState.SetActive(false);
                    _noRewardState.SetActive(false);

                    _finishedState.SetActive(true);
                    _rewardInfo.gameObject.SetActive(true);

                    _claimButtonHolder.SetActive(true);
                    _continueButtonHolder.SetActive(false);

                    _finalButtonState.SetTextId(RewardClaimButtonText);
                    _multiUseButton.ReplaceOnClick(OnClaimButton);

                    MoveLeaderboard(_scrollView, _claimPosition);
                    MoveLeaderboard(_teamScrollView, _claimPosition);
                    return;
                }

                if (hasContributionReward)
                {
                    LoadMyTeam();

                    _normalState.SetActive(true);
                    _noRewardState.SetActive(false);

                    _finishedState.SetActive(false);
                    _rewardInfo.gameObject.SetActive(false);

                    _claimButtonHolder.SetActive(true);
                    _continueButtonHolder.SetActive(false);

                    _finalButtonState.SetTextId(RewardClaimButtonText);
                    _multiUseButton.ReplaceOnClick(OnClaimButton);

                    MoveLeaderboard(_scrollView, _claimPosition);
                    MoveLeaderboard(_teamScrollView, _claimPosition);
                    return;
                }

                LoadTeamBattle();

                _normalState.SetActive(false);
                _noRewardState.SetActive(true);

                _finishedState.SetActive(true);
                _rewardInfo.gameObject.SetActive(false);

                _claimButtonHolder.SetActive(true);
                _continueButtonHolder.SetActive(false);

                _finalButtonState.SetTextId(ContinueButtonText);
                _multiUseButton.ReplaceOnClick(OnContinueButton);

                MoveLeaderboard(_scrollView, _claimPosition);
                MoveLeaderboard(_teamScrollView, _claimPosition);
            }
            else
            {
                LoadTeamBattle();

                _normalState.SetActive(true);
                _noRewardState.SetActive(false);

                _finishedState.SetActive(false);
                _rewardInfo.gameObject.SetActive(false);

                _claimButtonHolder.SetActive(false);
                _continueButtonHolder.SetActive(true);

                MoveLeaderboard(_scrollView, _defaultPosition);
                MoveLeaderboard(_teamScrollView, _defaultPosition);
            }
        }

        private static void MoveLeaderboard(TeamEventLeaderboardScrollView objectToMove, float y)
        {
            var rectTransform = objectToMove.gameObject.GetComponent<RectTransform>();
            rectTransform.offsetMin = new Vector2(rectTransform.offsetMin.x, y);
        }

        private void OnDataFetched(TeamCoopLeaderboardData data, bool success)
        {
            _waitingForData = false;
            if (data != null && success)
            {
                _leaderboardData = data;
                SetUpStates();
                _teamEvent.TryFinalize();
            }
            else
            {
                OnContinueButton();
            }
        }

        private void LoadTeamBattle()
        {
            if (_scrollView.LeaderboardSetupInProgress || _teamScrollView.LeaderboardSetupInProgress) return;

            _battleTab.Select();
            _myTeamTab.Deselect();
            _scrollView.Disable();

            _teamScrollView.Refresh(_teamEvent, _waitingForData);

            MainThreadDispatcher.StartUpdateMicroCoroutine(Rx.WaitUntil(
                () => _leaderboardData != null,
                () => _teamScrollView.HandleDataFetched(_leaderboardData, TeamEventLeaderboardType.Battle)));
        }

        private void LoadMyTeam()
        {
            if (_scrollView.LeaderboardSetupInProgress || _teamScrollView.LeaderboardSetupInProgress) return;

            _myTeamTab.Select();
            _battleTab.Deselect();
            _teamScrollView.Disable();

            _scrollView.Refresh(_teamEvent, _waitingForData);

            MainThreadDispatcher.StartUpdateMicroCoroutine(Rx.WaitUntil(
                () => _leaderboardData != null,
                () => _scrollView.HandleDataFetched(_leaderboardData, TeamEventLeaderboardType.MyTeam)));
        }
    }
}