using System;
using System.Collections.Generic;
using PBConfig;
using RPC.Social;
using BBB.BrainCloud;
using BBB.DI;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Player;

namespace BBB.RaceEvents
{
    public class DiscoRushEvent : RaceEvent
    {
        public DiscoRushEvent(RaceGameEventConfig raceEventConfig,
                              List<RaceStage> raceStages,
                              GameNotificationManager notificationManager,
                              IPlayerManager playerManager,
                              IConfig config,
                              ILocalizationManager localizationManager,
                              IEventDispatcher eventDispatcher,
                              ISchedulableDataProvider schedulableDataProvider,
                              IRaceDataProvider raceDataProvider,
                              IContext context
                              ) : base(raceEventConfig,
                                       raceStages,
                                       notificationManager,
                                       playerManager,
                                       config,
                                       localizationManager,
                                       eventDispatcher,
                                       schedulableDataProvider,
                                       raceDataProvider,
                                       context
                                       )
        {
        }

        protected override RaceRemoteData GetDefaultRemoteData()
        {
            return new DiscoRushRemoteData();
        }
        
        public override RaceRemoteData GetRemoteDataFromBC(BCUserRaceData dto)
        {
            var remoteData = new DiscoRushRemoteData(dto);
            if (remoteData.RaceStatus != ResultRaceStatus.Expired && GetTimeLeft() == TimeSpan.Zero)
            {
                //Adding a kill switch for multiple disco rush events per event cycle
                //Setting RoundCoolDown to zero will fallback to 1 disco rush event per event cycle
                if (GetCurrentRaceStage().RoundCoolDown > 0)
                {
                    if (DiscoRushRoundCoolDown() > 0)
                    {
                        remoteData.RaceStatus = ResultRaceStatus.Closed;
                    }
                }
                else
                {
                    remoteData.RaceStatus = ResultRaceStatus.Closed;
                }
            }

            return remoteData;
        }

        public override void TryJoinRace(Action<bool, ResultRaceStatus> callback)
        {
            base.TryJoinRace((joined, status) =>
            {
                if (joined && status == ResultRaceStatus.Active)
                {
                    State.CollectionStartTimeStamp = SchedulableDataProvider.GetCurrentUtcDateTime().ToUnixTimeSeconds();
                    PlayerManager.MarkDirty();
                }
                

                callback?.Invoke(joined, status);
            });
        }

        protected override bool DoesMyTimeSpanContain(DateTime dateTime)
        {
            if (State != null && State.CollectionStartTimeStamp != 0)
                return GetDiscoRushHourTime() != TimeSpan.Zero;
            
            return SchedulableConfigUtils.DoesMyTimeSpanContain(RaceEventConfig, SchedulableDataProvider, dateTime);
        }

        private TimeSpan GetDiscoRushHourTime()
        {
            var startedTime = DateTimeExtensions.FromUnixTimeSecondsUtc(State.CollectionStartTimeStamp);
            var stage = GetCurrentRaceStage();
            var endDateTime = startedTime.AddSeconds(stage.RoundTimeLimit);
            var result = endDateTime - SchedulableDataProvider.GetCurrentUtcDateTime();
            return result.TotalMilliseconds < 0 ? TimeSpan.Zero : result;
        }

        protected override bool IsFinished_Internal()
        {
            if (Status == ResultRaceStatus.Stopped)
                return true;

            return State.CollectionStartTimeStamp > 0 && GetDiscoRushHourTime() == TimeSpan.Zero;
        }

        public override void ProcessState(float highestPassedSortOrder)
        {
            base.ProcessState(highestPassedSortOrder);
            HandleDiscoRushUncertainState();
        }
        
        /// <summary>
        /// PZZLS-4165
        /// </summary>
        /// <remarks>
        /// Leave Race method was making CollectionStartTimeStamp to 0 (ClearField) without waiting for successful response from server.
        /// CollectionStartTimeStamp is used to calculate the time left for the event to expire Disco Rush Hour. (GetDiscoRushHourTime)
        /// Here we are handling the users who are stuck in a state where they are unable to collect rewards or let the event expire.
        /// If the user is joined and CollectionStartTimeStamp is 0, we are setting the CollectionStartTimeStamp to the current time - RoundTimeLimit
        /// Which will allow the user to collect rewards or let the event expire.
        /// </remarks>

        private void HandleDiscoRushUncertainState()
        {
            if (!IsLaunched_Internal()) return;
            var raceStage = GetCurrentRaceStage();

            if (Joined && State.CollectionStartTimeStamp == 0 && raceStage != null)
            {
                State.CollectionStartTimeStamp = SchedulableDataProvider.GetCurrentUtcDateTime().AddSeconds(-raceStage.RoundTimeLimit).ToUnixTimeSeconds();
            }
        }

        protected override DateTime GetEndTime()
        {
            if (State.CollectionStartTimeStamp > 0)
            {
                var raceStage = GetCurrentRaceStage();
                var startedTime = DateTimeExtensions.FromUnixTimeSecondsUtc(State.CollectionStartTimeStamp);
                return startedTime.AddSeconds(raceStage.RoundTimeLimit).AddSeconds(raceStage.RoundCoolDown);
            }

            return base.GetEndTime();
        }

        public override TimeSpan GetTimeLeft(string arg = null)
        {
            UnityEngine.Debug.LogError(State  + " " + State?.CollectionStartTimeStamp);
            
            if (State != null && State.CollectionStartTimeStamp != 0)
            {
                return GetDiscoRushHourTime();
            }

            var timeLeft = SchedulableConfigUtils.GetTimeLeft(RaceEventConfig, SchedulableDataProvider, arg);
            return new TimeSpan(timeLeft.Hours, timeLeft.Minutes, timeLeft.Seconds);
        }

        public override void HandleLoss()
        {
            State.RaceStatus = (int)ResultRaceStatus.Closed;
            base.HandleLoss();
        }

        public override void HandleClaimReward(Action<bool> callback)
        {
            State.RaceStatus = (int)ResultRaceStatus.Closed;
            base.HandleClaimReward(callback);
        }

        public override bool ShouldShowIcon()
        {
            //Since we are closing event to run multiple disco rush events per event cycle
            //We need this to override base condition and to display the finished state of the event
            //Particularly on the hud icon and on the bottom part of the Start Level Panel
            //AutoShow will work regardless
            if (IsLaunched_Internal() && Joined &&
                Status == ResultRaceStatus.Closed && IsFinished_Internal() && !IsPrizeClaimed())
            {
                return true;
            }

            return base.ShouldShowIcon();
        }
        
        protected override bool IsReadyToRelease(bool withinActiveTimeSpawn)
        {
            if (IsLaunched_Internal() && ConnectivityStatusManager.ConnectivityReachable)
            {
                var lastStartTime = SchedulableConfigUtils.GetLastStartTime(RaceEventConfig, SchedulableDataProvider);

                //if start time is a lot different then we release the event
                if (Math.Abs(lastStartTime.ToUnixTimeSeconds() - State.CurrentStartTimestamp) > StartTimesMaxDifference)
                    return true;

                switch (Status)
                {
                    case ResultRaceStatus.Active:
                        return !DoesMyExpirationTimeSpanContain(SchedulableDataProvider.GetCurrentUtcDateTime());
                    case ResultRaceStatus.Closed:
                        //Adding a kill switch for multiple disco rush events per event cycle
                        //Setting RoundCoolDown to zero will fallback to 1 disco rush event per event cycle
                        if (GetCurrentRaceStage().RoundCoolDown > 0)
                        {
                            return DiscoRushRoundCoolDown() < 0;
                        }
                        return !DoesMyExpirationTimeSpanContain(SchedulableDataProvider.GetCurrentUtcDateTime());
                    case ResultRaceStatus.Stopped:
                        return !Joined;
                    case ResultRaceStatus.Expired:
                    case ResultRaceStatus.Unknown:
                        return true;
                }
            }
            return false;
        }

        //After a round of Disco Rush is completed, Win/Lose the event is placed in closed Status
        //Here we are introducing RoundCoolDown, which will move the event to stopped state only after set time has passed.
        //Which will then release the event, after releasing, the event is ready to start again
        //Ultimately after the event duration has passed, Expired Status will fully stop the event
        
        private double DiscoRushRoundCoolDown()
        {
            var raceStage = GetCurrentRaceStage();
            var startedTime = DateTimeExtensions.FromUnixTimeSecondsUtc(State.CollectionStartTimeStamp);
            var endDateTime = startedTime.AddSeconds(raceStage.RoundTimeLimit).AddSeconds(raceStage.RoundCoolDown);
            var result = endDateTime - SchedulableDataProvider.GetCurrentUtcDateTime();
            return result.TotalMilliseconds;
        }
    }
}