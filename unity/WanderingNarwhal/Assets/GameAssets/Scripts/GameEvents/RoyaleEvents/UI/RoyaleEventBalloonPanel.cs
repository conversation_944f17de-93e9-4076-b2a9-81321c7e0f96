using System.Collections.Generic;
using BBB;
using BBB.BrainCloud;
using BBB.Core;
using BBB.DI;
using BBB.RaceEvents.UI;
using BebopBee;
using BebopBee.Core;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using GameAssets.Scripts.GameEvents.RoyaleEvents.Model;
using GameAssets.Scripts.Theme;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.GameEvents.RoyaleEvents.UI
{
    public class RoyaleEventBalloonPanel : BbbMonoBehaviour
    {
        [Header("References")]
        [SerializeField] private StepRectanglesPanel _stepsRectanglePanel;
        [SerializeField] private TextMeshProUGUI _debugStateText;
        [SerializeField] private RectTransform _centralObjectTransform;
        [SerializeField] private List<ParachuteCharacterView> _parachuteCharacters;
        [SerializeField] private ParachuteCharacterView _ownParachuteCharacter;
        [SerializeField] private RectTransform _centralObjectHolder;
        [SerializeField] private TransformFollower _avatarsHolderTransformFollower;
        [SerializeField] private TransformRotationFollower _avatarsHolderTransformRotationFollower;
        [SerializeField] private RectTransform _charactersDefaultParent;
        [SerializeField] private RectTransform _charactersInJumpParent;
        [Header("Configuration")]
        [SerializeField] private float _hiddenBalloonPositionY;
        [SerializeField] private float _balloonLoseMotionDuration = 3f;
        [SerializeField] private float _balloonWinMotionDuration = 2f;
        [SerializeField] private AnimationCurve _balloonFlyingOutAnimationCurve;
        [SerializeField] private JumpSettings _ownJumpSettings;
        [SerializeField] private JumpSettings _jumpSettings;
        [SerializeField] private float _delayBeforeBalloonStartsOnWin = 0.5f;
        [SerializeField] private float _delayBeforeBalloonStartsOnLoss = 0.5f;
        [SerializeField] private float _delayBeforePlayersJumpStarts = 1f;

        [Header("Temp Configuration")] 
        [SerializeField] private float _otherPlayersJumpDelay = 0.01f;
        
        private float _initialBalloonPositionX;
        private float _initialBalloonPositionY;
        
        private BrainCloudAvatarsManager _brainCloudAvatarsManager;
        private IAccountManager _accountManager;
        private IGameEventResourceManager _resourceManager;
        
        private RoyaleEventMainModalViewModel _viewModel;
        private Tweener _balloonTweener;
        private string _lastSetGameEventUid;
        private RoyaleEventCentralObjectView _royaleEventCentralObject;
        private RoyaleEventBackgroundObject _backgroundObject;
        private Tweener _balloonMotionTweener;
        private Tweener _playerJumpDelayTweener;
        
        private readonly List<RoyaleBalloonMovableObject> _balloonMovableObject = new List<RoyaleBalloonMovableObject>();
        private bool _parachuteCharactersSorted = false;

        public void Init(IContext context)
        {
            _accountManager = context.Resolve<IAccountManager>();
            _brainCloudAvatarsManager = context.Resolve<BrainCloudAvatarsManager>();
            _resourceManager = context.Resolve<IGameEventResourceManager>();
            
            var localPosition = _centralObjectTransform.localPosition;
            _initialBalloonPositionX = localPosition.x;
            _initialBalloonPositionY = localPosition.y;

            foreach (var parachuteCharacter in _parachuteCharacters)
            {
                parachuteCharacter.Init();
            }
            _ownParachuteCharacter.Init();
            _stepsRectanglePanel.Init();
        }

        private GameObject GetBackgroundPrefab(string eventId)
        {
            var eventVisualConfig = _resourceManager.GetGenericAsset<RoyaleEventVisualConfig>(eventId, GameEventResKeys.RoyaleGameEventSettings);
            if (eventVisualConfig is RoyaleEventVisualConfig visualConfig)
            {
                return visualConfig.Theme.GetThemedObject<GameObject>(ThemePlacementId.RoyaleEvent_Main_BG, "");
            }

            return null;
        }

        public void Setup(RoyaleEventMainModalViewModel viewModel)
        {
            _viewModel = viewModel;
            _debugStateText.SetText(GetDebugText(viewModel.ChangeType));
            var gameEventUid = viewModel.EventUid;
            
            SetupAvatars(_brainCloudAvatarsManager).Forget();
            Setup(_accountManager);

            if (_lastSetGameEventUid != gameEventUid)
            {
                if (_royaleEventCentralObject != null)
                {
                    Destroy(_royaleEventCentralObject.gameObject);
                }

                if (_backgroundObject != null)
                {
                    Destroy(_backgroundObject.gameObject);
                }
                
                var backgroundPrefab = GetBackgroundPrefab(viewModel.EventUid);
                var backgroundObject = Object.Instantiate(backgroundPrefab, transform);
                _backgroundObject = backgroundObject.GetComponent<RoyaleEventBackgroundObject>();
                _backgroundObject.transform.SetSiblingIndex(0);
                
                _balloonMovableObject.Clear();
                _balloonMovableObject.AddRange(_backgroundObject.GetBalloonMovableObjects());
                
                foreach (var moveableObject in _balloonMovableObject)
                {
                    moveableObject.Init();
                }
                
                var mainObjectPrefab = _resourceManager.GetGenericAsset<GameObject>(gameEventUid, GameEventResKeys.MainObject);
                var mainObject = GameObject.Instantiate(mainObjectPrefab, Vector3.zero, Quaternion.identity, _centralObjectHolder);
                mainObject.transform.localPosition = Vector3.zero;
                _royaleEventCentralObject = mainObject.GetComponent<RoyaleEventCentralObjectView>();
                _avatarsHolderTransformFollower.FollowThis(_royaleEventCentralObject.TransformToFollow);
                _avatarsHolderTransformRotationFollower.FollowThis(_royaleEventCentralObject.TransformToFollow);
                
                _lastSetGameEventUid = gameEventUid;
            }
        }

        private async UniTask SetupAvatars(BrainCloudAvatarsManager brainCloudManager)
        {
            var avatarUrls = await brainCloudManager.GetNumberOfAvatars(_parachuteCharacters.Count);
            var min = Mathf.Min(avatarUrls.Count, _parachuteCharacters.Count);
            if (avatarUrls.Count != _parachuteCharacters.Count)
            {
                BDebug.LogError(LogCat.General, $"Avatars count number: {avatarUrls.Count} while prarachute characters count: {_parachuteCharacters.Count}");
            }
            for (int i = 0; i < min; i++)
            {
                _parachuteCharacters[i].SetupAvatar(avatarUrls[i]);
            }
        }
        
        private void Setup(IAccountManager accountManager)
        {
            for (int i = 0; i < _parachuteCharacters.Count; i++)
            {
                _parachuteCharacters[i].transform.SetParent(_charactersDefaultParent, true);
                _parachuteCharacters[i].Setup(_jumpSettings);
            }

            _ownParachuteCharacter.transform.SetParent(_charactersDefaultParent, true);
            _ownParachuteCharacter.Setup(_jumpSettings, accountManager.Profile.Avatar);

            var ownLocalPos = _ownParachuteCharacter.transform.localPosition;
            if (!_parachuteCharactersSorted)
            {
                _parachuteCharacters.Sort((a, b) =>
                {
                    var aDistanceSqr = (a.transform.localPosition - ownLocalPos).sqrMagnitude;
                    var bDistanceSqr = (b.transform.localPosition - ownLocalPos).sqrMagnitude;
                    return -aDistanceSqr.CompareTo(bDistanceSqr);
                });

                _parachuteCharactersSorted = true;
            }
        }

        private string GetDebugText(RoyaleEventChangeType changeType)
        {
            switch (changeType)
            {
                case RoyaleEventChangeType.WinRound:
                    return "Round won anim will be here";
                case RoyaleEventChangeType.WinStep:
                    return "Step won anim will be here";
                case RoyaleEventChangeType.Loss:
                    return "Loss anim will be here";
                case RoyaleEventChangeType.TimeIsOut:
                    return "Instance time is out";
                case RoyaleEventChangeType.None:
                default:    
                    return "No animation here";
            }
        }

        public void SetupToCurrentStatus()
        {
            var stepToSet = _viewModel.CurrentStep;

            if (stepToSet >= 0)
            {
                _stepsRectanglePanel.SetStepSelected(stepToSet);
                foreach (var moveableObject in _balloonMovableObject)
                {
                    moveableObject.SetToStep(stepToSet);
                }
            }
            
            PutPlayersOffscreen(_viewModel.CurrentPlayersLeftInRound, _viewModel.TotalPlayers);
        }
        
        public void PrepareStatusChange()
        {
            var stepToSet = _viewModel.LastShownStep;

            if (stepToSet >= 0)
            {
                _stepsRectanglePanel.SetStepSelected(stepToSet);
                foreach (var moveableObject in _balloonMovableObject)
                {
                    moveableObject.SetToStep(stepToSet);
                }
            }

            PutPlayersOffscreen(_viewModel.LastShownPlayersLeftInRound, _viewModel.TotalPlayers);
        }
        
        public void AnimateStatusChange()
        {
            if (_balloonMotionTweener != null)
            {
                _balloonMotionTweener.Kill();
                _balloonMotionTweener = null;
            }
            
            if (_viewModel.ChangeType is RoyaleEventChangeType.Loss or RoyaleEventChangeType.TimeIsOut)
            {
                _stepsRectanglePanel.MakeStepsFade(_viewModel.LastShownStep);
                _ownParachuteCharacter.JumpOffScreen(0f, _ownJumpSettings, () =>
                {
                    _ownParachuteCharacter.transform.SetParent(_charactersInJumpParent, true);
                });
                _balloonMotionTweener = Rx.Invoke(_delayBeforeBalloonStartsOnLoss, _ =>
                {
                    MoveBalloonUp();
                });
            }
            else
            {
                var stepToAnimateDeselect = _viewModel.LastShownStep;
                if (stepToAnimateDeselect >= 0)
                {
                    _stepsRectanglePanel.TweenStepDeselected(stepToAnimateDeselect);
                }
                
                var stepToAnimateTo = _viewModel.CurrentStep;
                if (stepToAnimateTo >= 0)
                {
                    _stepsRectanglePanel.TweenStepSelected(stepToAnimateTo);
                    _balloonMotionTweener = Rx.Invoke(_delayBeforeBalloonStartsOnWin, _ =>
                    {
                        LaunchBackgroundObjectsDown(stepToAnimateTo);
                    });
                }

                if (_playerJumpDelayTweener != null)
                {
                    _playerJumpDelayTweener.Kill();
                    _playerJumpDelayTweener = null;
                }

                _playerJumpDelayTweener = Rx.Invoke(_delayBeforePlayersJumpStarts, _ =>
                {
                    JumpPlayersOffScreen(_viewModel.LastShownPlayersLeftInRound, _viewModel.CurrentPlayersLeftInRound, _viewModel.TotalPlayers);
                });
            }
        }

        private void PutPlayersOffscreen(int playersLeftInRound, int totalPlayers)
        {
            var playersLeftRatio = playersLeftInRound / (float)totalPlayers;
            var numberOfPlayersToHide = Mathf.RoundToInt((1f - playersLeftRatio) * _parachuteCharacters.Count);
            for (int i = 0; i < numberOfPlayersToHide; i++)
            {
                if (i < _parachuteCharacters.Count)
                {
                    _parachuteCharacters[i].PutOffscreen();
                    _parachuteCharacters[i].transform.SetParent(_charactersInJumpParent, true);
                }
                else
                {
                    BDebug.LogError(LogCat.General, $"numberOfPlayersToHide {numberOfPlayersToHide} is > than {_parachuteCharacters.Count}");
                }
            }
        }

        private void JumpPlayersOffScreen(int prevPlayersLeftInRound, int playersLeftInRound, int totalPlayers)
        {
            var prevPlayersLeftRatio = prevPlayersLeftInRound / (float)totalPlayers;
            var prevNumberOfPlayersToHide = Mathf.RoundToInt((1f - prevPlayersLeftRatio) * _parachuteCharacters.Count);
            
            var playersLeftRatio = playersLeftInRound / (float)totalPlayers;
            var numberOfPlayersToHide = Mathf.RoundToInt((1f - playersLeftRatio) * _parachuteCharacters.Count);
            if (prevNumberOfPlayersToHide >= numberOfPlayersToHide)
            {
                numberOfPlayersToHide = prevNumberOfPlayersToHide + 1;
            }
            for (int i = prevNumberOfPlayersToHide; i < numberOfPlayersToHide; i++)
            {
                if (i < _parachuteCharacters.Count)
                {
                    var character = _parachuteCharacters[i];
                    var counter = i - prevNumberOfPlayersToHide;
                    _parachuteCharacters[i].JumpOffScreen(_otherPlayersJumpDelay*counter, _jumpSettings, () =>
                    {
                        character.transform.SetParent(_charactersInJumpParent, true);
                    });
                }
                else
                {
                    BDebug.LogError(LogCat.General, $"numberOfPlayersToHide {numberOfPlayersToHide} is > than {_parachuteCharacters.Count}");
                }
            }
        }

        private void MoveBalloonUp()
        {
            if (_balloonTweener != null)
            {
                _balloonTweener.Kill();
                _balloonTweener = null;
            }
            
            var targetPosition = new Vector3(_initialBalloonPositionX, _hiddenBalloonPositionY, 0f);
            _balloonTweener = _centralObjectTransform.DOAnchorPos(targetPosition, _balloonLoseMotionDuration)
                .SetEase(_balloonFlyingOutAnimationCurve).OnComplete(
                () =>
                {
                    _balloonTweener = null;
                });
        }

        private void LaunchBackgroundObjectsDown(int step)
        {
            foreach (var moveableObject in _balloonMovableObject)
            {
                moveableObject.MoveToStep(step, _balloonWinMotionDuration);
            }
        }

        public void ResetState()
        {
            if (_balloonTweener != null)
            {
                _balloonTweener.Kill();
                _balloonTweener = null;
            }
            
            _centralObjectTransform.localPosition = new Vector3(_initialBalloonPositionX, _initialBalloonPositionY, 0f);
            _stepsRectanglePanel.ResetToDefault();
            
            foreach (var moveableObject in _balloonMovableObject)
            {
                moveableObject.ResetState();
            }

            foreach (var parachuteChar in _parachuteCharacters)
            {
                parachuteChar.transform.SetParent(_charactersDefaultParent, true);
            }
            _ownParachuteCharacter.transform.SetParent(_charactersDefaultParent, true);
        }
    }
}