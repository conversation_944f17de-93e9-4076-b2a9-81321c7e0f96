using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.DailyLogin;
using BBB.DI;

namespace BBB
{
    public class DailyLoginModalController: BaseModalsController<IDailyLoginViewPresenter>
    {
        private Action<bool> _closeCallback;
        private DailyLoginManager _dailyLoginManager;
        private bool _playButtonClicked;

        private SortedDictionary<int, Dictionary<string, int>> _rewards;
        private Func<string, TimeSpan> _timeLeftGetter;

        protected override void OnInitializeByContext(IContext context)
        {
            _dailyLoginManager = context.Resolve<DailyLoginManager>();
            
            base.OnInitializeByContext(context);
        }

        public void Setup(SortedDictionary<int, Dictionary<string, int>> rewards, Func<string, TimeSpan> timeLeftGetter, Action<bool> closeCallback = null)
        {
            _rewards = rewards;
            _timeLeftGetter = timeLeftGetter;
            _closeCallback = closeCallback;
        }

        protected override void OnShow()
        {
            base.OnShow();
            Subscribe();
            _playButtonClicked = false;
            View.Setup(_rewards, _timeLeftGetter, _dailyLoginManager.ClaimedDays, _dailyLoginManager.CurrentDay, _dailyLoginManager.GetRemainingTimeToClaim);
        }

        protected override void OnHide()
        {
            base.OnHide();
            Unsubscribe();
        }

        protected override void OnPostHide()
        {
            base.OnPostHide();

            _closeCallback.SafeInvoke(_playButtonClicked);
            _closeCallback = null;
            _playButtonClicked = false;
        }

        private void Subscribe()
        {
            Unsubscribe();
            _dailyLoginManager.DayChanged += OnDayChanged;
            View.InfoButtonPressedEvent += InfoButtonHandler;
            View.TermsButtonPressedEvent += TermsButtonHandler;
            View.PlayButtonPressedEvent += PlayButtonHandler;
            View.ClaimCurrentDayEvent += ClaimCurrentDayHandler;
        }

        private void Unsubscribe()
        {
            _dailyLoginManager.DayChanged -= OnDayChanged;
            View.InfoButtonPressedEvent -= InfoButtonHandler;
            View.TermsButtonPressedEvent -= TermsButtonHandler;
            View.PlayButtonPressedEvent -= PlayButtonHandler;
            View.ClaimCurrentDayEvent -= ClaimCurrentDayHandler;
        }

        private void OnDayChanged()
        {
            View.UpdateState(_dailyLoginManager.ClaimedDays, _dailyLoginManager.CurrentDay);
        }
        
        private void InfoButtonHandler()
        {
            _dailyLoginManager.ShowInfoModal();
        }
        
        private void TermsButtonHandler()
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.Sweepstakes.Name, DauInteractions.Sweepstakes.Terms, DauInteractions.Sweepstakes.SweepstakesDailyCollect));
        }

        private void PlayButtonHandler()
        {
            _playButtonClicked = true;
            base.OnCloseClicked();
        }

        private bool ClaimCurrentDayHandler()
        {
            return _dailyLoginManager.TryClaimCurrentDay();
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            Unsubscribe();
            _closeCallback = null;
        }
    }
}