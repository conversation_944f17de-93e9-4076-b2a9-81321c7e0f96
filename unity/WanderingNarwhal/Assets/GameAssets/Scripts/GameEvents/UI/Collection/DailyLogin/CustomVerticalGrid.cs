using System;
using UnityEngine;

namespace BBB
{
    public class CustomVerticalGrid : BbbMonoBehaviour
    {
        [SerializeField] private RectOffset _padding;
        [SerializeField] private Vector2 _cellSize;
        [SerializeField] private Vector2 _spacing;
        
        public int CurrentColumnCount { get; private set; }
        public RectOffset Padding => _padding;
        public Vector2 CellSize => _cellSize;
        public Vector2 Spacing => _spacing;
        
        private RectTransform _rectTransform;

        private void Awake()
        {
            _rectTransform = GetComponent<RectTransform>();
        }

        public void RefreshLayout()
        {
            var childCount = transform.childCount;

            var size = _rectTransform.rect.size;
            var sizeDelta = _rectTransform.sizeDelta;
            var sizeWithoutDelta = size - sizeDelta;
            
            var minWidth = _padding.horizontal + _cellSize.x;
            var minHeight = _padding.vertical;
            size.x = Mathf.Max(size.x, minWidth);
            
            CurrentColumnCount = Mathf.FloorToInt((size.x - _padding.horizontal + _spacing.x) / (_cellSize.x + _spacing.x));
            CurrentColumnCount = Mathf.Max(1, CurrentColumnCount);
            var rowCount = Mathf.CeilToInt((float)childCount / CurrentColumnCount);
            
            size.y = Mathf.Max(rowCount * (_cellSize.y + _spacing.y) - _spacing.y + _padding.vertical, minHeight);
            _rectTransform.sizeDelta = size - sizeWithoutDelta;
            
            UpdateChildObjects();
        }

        private void UpdateChildObjects()
        {
            var childCount = transform.childCount;
            var betweenColumnsCount = CurrentColumnCount - 1;
            var horizontalPaddingOffset = (_padding.left - _padding.right) * 0.5f;
            var horizontalCenter = _rectTransform.rect.size.x * 0.5f + horizontalPaddingOffset;

            var startX = -betweenColumnsCount * (_cellSize.x + _spacing.x) * 0.5f + horizontalCenter;
            var startY = -_padding.top - _cellSize.y * 0.5f;
            
            var childIndex = 0;
            for (var i = 0; i < childCount; i++)
            {
                var childRectTransform = transform.GetChild(i) as RectTransform;
                
                if (childRectTransform == null) continue;
                
                childRectTransform.anchorMin = Vector2.up;
                childRectTransform.anchorMax = Vector2.up;
                childRectTransform.pivot = Vector2.one * 0.5f;
                childRectTransform.sizeDelta = _cellSize;

                var columnIndex = childIndex % CurrentColumnCount;
                var rowIndex = childIndex / CurrentColumnCount;
                var x = startX + columnIndex * _cellSize.x + columnIndex * _spacing.x;
                var y = startY - (rowIndex * _cellSize.y + rowIndex * _spacing.y);
                
                childRectTransform.anchoredPosition = new Vector2(x, y);
                childIndex++;
            }
        }
    }
}
