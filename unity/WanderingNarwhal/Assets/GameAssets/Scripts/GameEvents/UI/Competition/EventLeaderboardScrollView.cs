using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Audio;
using BBB.Core;
using BBB.DI;
using BBB.UI.LoopScrollRect;
using BebopBee.Core.Audio;
using GameAssets.Scripts.UI.OverlayDialog;
using GameAssets.Scripts.Utils;
using UnityEngine;
using UnityEngine.Serialization;

namespace BBB
{
    public class EventLeaderboardIntroComplete : IEvent
    {
    }

    public class EventLeaderboardScrollView : BbbMonoBehaviour, IEventLeaderboardMembersView
    {
        private enum EffectType
        {
            None = 0,
            Submit = 1,
            LeagueUp = 2
        }

        [SerializeField] private List<GameObject> _itemPrefabsList;
        [SerializeField] private LoopScroll _loopScrollRect;
        [SerializeField] private Canvas _loopScrollCanvas;
        [SerializeField] private LoopScrollAnimaionController _loopScrollRectAnimator;
        [SerializeField] private LoopScrollSnappedItemVisibilitySwitcher _loopScrollSnapItems;

        [SerializeField] private GameObject _loadingIndicator;
        [SerializeField] private float _throbScale = 1.05f;
        [SerializeField] private float _throbDuration = 0.5f;
        [SerializeField] private int _throbLoops = 3;
        [SerializeField] private float _submittableRewardAlpha = 0.5f;

        [SerializeField] private float _minMoveDistance = 115f;
        [SerializeField] private float _maxMoveDistance = 1300f;
        [SerializeField] private float _minMoveDuration = 0.5f;
        [SerializeField] private float _maxMoveDuration = 6f;
        [Tooltip("Focus on the current player or on the top players")]
        [SerializeField] private bool _focusOnCurrentPlayer = true;


        [SerializeField] private Animator _leagueUpGoEffectAnimator;
        [SerializeField] private Animator _submitGoEffectAnimator;
        [FormerlySerializedAs("_speechBubbleConfig")] [SerializeField] private OverlayDialogConfig _overlayDialogConfig;

        private List<EventLeaderboardItemBase> _leaderboardItems;
        private League _currentLeague;

        private EventLeaderboardCommonViewData _commonViewData;

        private EventLeaderboardItemView _playerListItemViewSubmitted;
        private EventLeaderboardItemView _playerListItemViewUnsubmitted;

        private IEventDispatcher _eventDispatcher;
        private ICoroutineExecutor _coroutineExecutor;
        private IGameEventManager _gameEventManager;
        private Coroutine _coroutine;
        private int _lastScoreShown = -1;
        private string _eventUid;
        //private bool _introducingItem = false;
        private Action<League> _onLeagueRefreshedCallback;
        private bool _readyToSubmitState;
        private IOverlayDialogManager _overlayDialogManager;
        private Action<Action> _dragStartedHandler;

        private bool _animate = true;
        private bool _fetching;
        private bool _scrollEnded;

        public void Init(IContext context)
        {
            _commonViewData = new EventLeaderboardCommonViewData(context);
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _coroutineExecutor = context.Resolve<ICoroutineExecutor>();
            _gameEventManager = context.Resolve<IGameEventManager>();

            Configure();
        }

        public void Configure(bool scrollable = true, bool animate = true)
        {
            _loopScrollRect.Interactable = scrollable;
            _animate = animate;
        }

        public void Refresh(EventLeaderboardViewModel viewModel, Action<League> dataLoadedCallback, 
            IOverlayDialogManager overlayDialogManagerr)
        {
            _eventUid = viewModel.EventUid;
            _onLeagueRefreshedCallback = dataLoadedCallback;
            _overlayDialogManager = overlayDialogManagerr;
            _lastScoreShown = _gameEventManager.GetLastScoreShown(viewModel.EventUid);
            _commonViewData.Refresh(viewModel, _submittableRewardAlpha);
            
            _loopScrollCanvas.enabled = false;
            ClearScrollRect();
            if (viewModel.Items == null)
            {
                if(_loadingIndicator != null)
                {
                    _loadingIndicator.SetActive(true);
                }
            }
            else
            {
                InitItems(viewModel.Items, viewModel.League);
            }
        }

        private void ClearScrollRect()
        {
            _loopScrollRect.contentModel.Clear();
            _loopScrollRect.ClearAllVisibleListItems();

            _playerListItemViewSubmitted = null;
            _playerListItemViewUnsubmitted = null;
        }

        public void SetShownValues()
        {
            if (_gameEventManager.FindGameEventByUid(_eventUid) is CompetitionGameEvent gameEvent)
            {
                var specialType = gameEvent.SpecialType;

                switch (specialType)
                {
                    case SpecialCompetitionEventType.None:
                        if (_playerListItemViewUnsubmitted)
                        {
                            _gameEventManager.SetLastScoreShown(_eventUid, _playerListItemViewUnsubmitted.Score);
                        }
                        break;
                    case SpecialCompetitionEventType.SideMap:
                    case SpecialCompetitionEventType.Casual:
                        if (_playerListItemViewSubmitted)
                        {
                            _gameEventManager.SetLastScoreShown(_eventUid, _playerListItemViewSubmitted.Score);
                        }
                        break;
                }
            }

            if (_playerListItemViewSubmitted)
            {
                _gameEventManager.SetLastRankShown(_eventUid, _playerListItemViewSubmitted.Rank);
            }
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            _leagueUpGoEffectAnimator.gameObject.SetActive(false);
            _leagueUpGoEffectAnimator.Rebind();
        }

        public void Clear()
        {
            _onLeagueRefreshedCallback = null;
            _overlayDialogManager = null;
            _dragStartedHandler = null;

            _eventUid = null;
            _lastScoreShown = -1;

            _readyToSubmitState = false;

            _leaderboardItems = null;
            _currentLeague = default;
            UnsubscribeScroller();

            ClearScrollRect();
            _loopScrollSnapItems.Clear();

            if (_coroutine != null)
            {
                _coroutineExecutor.StopCoroutine(_coroutine);
                _coroutine = null;
            }

            _leagueUpGoEffectAnimator.gameObject.SetActive(false);
            _leagueUpGoEffectAnimator.Rebind();
            _submitGoEffectAnimator.gameObject.SetActive(false);
            _submitGoEffectAnimator.Rebind();
        }

        private void GetIndicies(List<EventLeaderboardItemBase> items,
            out int ownSubmittedItemIndex, out int ownUnsubmittedItemIndex)
        {
            ownSubmittedItemIndex = -1;
            ownUnsubmittedItemIndex = -1;

            int currentIndex = 0;

            foreach (var item in items)
            {
                if (item is PlayerEventLeaderboardItem standardItem)
                {
                    if (ownSubmittedItemIndex == -1 && standardItem.Type == PlayerItemType.OwnSubmitted)
                    {
                        ownSubmittedItemIndex = currentIndex;
                    }

                    if (ownUnsubmittedItemIndex == -1 && standardItem.Type == PlayerItemType.OwnUnsubmitted)
                    {
                        ownUnsubmittedItemIndex = currentIndex;
                    }
                }

                currentIndex++;
            }
        }

        private void UpdateScrollContentModel()
        { 
            _loopScrollRect.contentModel.Clear();

            if (_itemPrefabsList.Count == 0)
            {
                Debug.LogError("Scroll view prefabs list is empty!", gameObject);
                return;
            }

            foreach (var item in _leaderboardItems)
            {
                ILoopScrollListItem prefab = null;
                if (item.PrefabIndex < 0 || item.PrefabIndex >= _itemPrefabsList.Count)
                {
                    Debug.LogError($"Item prefab index '{item.PrefabIndex}' is out of range! [0..{_itemPrefabsList.Count}]");
                }
                else
                {
                    prefab = _itemPrefabsList[item.PrefabIndex].GetComponent<ILoopScrollListItem>();
                }

                _loopScrollRect.contentModel.Add(new ListItemData()
                {
                    model = item,
                    prefab = prefab,
                });
            }

        }

        private void InitItems(List<EventLeaderboardItemBase> items, League currentLeague)
        {
            if(items == null) return;

            _leaderboardItems = items;
            _currentLeague = currentLeague;

            GetIndicies(_leaderboardItems, out var ownSubmittedItemIndex, out var ownUnsubmittedItemIndex);
            int animationStartPositionIndex = 
                GetIntroductionStartIndex(items, currentLeague, ownSubmittedItemIndex, ownUnsubmittedItemIndex);

            SubscribeScroller();
            UpdateScrollContentModel();

            _loopScrollRect.ReinitializeVisibleListItems();
            _loopScrollRect.RefreshVisibleListItemsAndLayout();

            if (_coroutine != null)
            {
                _coroutineExecutor.StopCoroutine(_coroutine);
            }

            _onLeagueRefreshedCallback.SafeInvoke(currentLeague);

            var indexToIntroduce = ownUnsubmittedItemIndex != -1 ? ownUnsubmittedItemIndex : ownSubmittedItemIndex;

            _coroutine = _coroutineExecutor.StartCoroutine(IntroduceItem(animationStartPositionIndex, indexToIntroduce, EffectType.None, onDone: OnDone));
            return;

            void OnDone()
            {
                _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<EventLeaderboardIntroComplete>());

                TryTransferToSubmittableState(ownUnsubmittedItemIndex, ownSubmittedItemIndex);

                SetSnappedItemsTargetIndex(ownSubmittedItemIndex);
            }
        }

        private int GetIntroductionStartIndex(List<EventLeaderboardItemBase> items, League currentLeague, int ownSubmittedItemIndex, int ownUnsubmittedItemIndex)
        {
            const int noAnimatedMovement = -1;
            if (_eventUid == null || !_animate)
                return noAnimatedMovement;

            var gameEvent = _gameEventManager.FindGameEventByUid(_eventUid) as CompetitionGameEvent;
            if (gameEvent == null)
                return noAnimatedMovement;

            var curIndex = noAnimatedMovement;
            PlayerEventLeaderboardItem selectedItem = null;

            switch (gameEvent.SpecialType)
            {
                case SpecialCompetitionEventType.None when ownUnsubmittedItemIndex != noAnimatedMovement: 
                    selectedItem = (PlayerEventLeaderboardItem)_leaderboardItems[ownUnsubmittedItemIndex]; 
                    curIndex = ownUnsubmittedItemIndex;
                    break;
                case SpecialCompetitionEventType.Casual or SpecialCompetitionEventType.SideMap when ownSubmittedItemIndex != noAnimatedMovement:
                    selectedItem = (PlayerEventLeaderboardItem)_leaderboardItems[ownSubmittedItemIndex];
                    curIndex = ownSubmittedItemIndex;
                    break;
            }

            if (selectedItem == null)
            {
                BDebug.LogError(LogCat.General, $"Selected item not found for {gameEvent.Uid} {gameEvent.SpecialType}");
                return noAnimatedMovement;
            }

            if (_lastScoreShown >= selectedItem.Score)
                return noAnimatedMovement;

            var targetIndex = items.IndexOf(item => item is PlayerEventLeaderboardItem playerItem && playerItem.Score < _lastScoreShown);

            if (targetIndex == noAnimatedMovement)
            {
                targetIndex = int.MaxValue;
            }

            return targetIndex != curIndex ? items.MoveFromTo(curIndex, targetIndex) : noAnimatedMovement;
        }

        private void TryTransferToSubmittableState(int ownUnsubmittedItemIndex, int ownSubmittedItemIndex)
        {

            if (ownUnsubmittedItemIndex != -1 && _playerListItemViewUnsubmitted)
            {
                bool shouldTransferToSubmitState = false;

                if (ownSubmittedItemIndex == -1)
                {
                    var ownUnsubmittedItem = (PlayerEventLeaderboardItem) _leaderboardItems[ownUnsubmittedItemIndex];
                    if(ownUnsubmittedItem != null)
                        shouldTransferToSubmitState = ownUnsubmittedItem.Score > 0;
                }
                else
                {
                    var submittedItem = (PlayerEventLeaderboardItem)_leaderboardItems[ownSubmittedItemIndex];
                    var ownUnsubmittedItem = (PlayerEventLeaderboardItem) _leaderboardItems[ownUnsubmittedItemIndex];
                    if (submittedItem != null && ownUnsubmittedItem != null)
                    {
                        shouldTransferToSubmitState = ownUnsubmittedItem.Score > submittedItem.Score && submittedItem.Score > 0;
                    }                             
                }

                if (shouldTransferToSubmitState)
                {
                    _playerListItemViewUnsubmitted.ChangeToReadyToSubmitState(_commonViewData);
                    _readyToSubmitState = true;
                }
            }
        }

        private IEnumerator IntroduceItem(int prevPositionIndex, int currentPositionIndex, EffectType effectType, Action onDone = null)
        {
            yield return null;

            while (!_loopScrollRect.gameObject.activeInHierarchy)
            {
                yield return null;
            }

            var indexToInstantFocus = prevPositionIndex == -1 ? currentPositionIndex : prevPositionIndex;

            if (indexToInstantFocus >= 0 && _focusOnCurrentPlayer)
            {
                var normPos = _loopScrollRect.CalcNormalizedItemPosition(indexToInstantFocus);
                _loopScrollRect.SetContentNormalizedPosition(normPos);
            }

            yield return WaitCache.Seconds(0.10f);

            if (_loadingIndicator != null)
            {
                _loadingIndicator.SetActive(false);
            }

            _loopScrollCanvas.enabled = true;
            if (prevPositionIndex == -1 || prevPositionIndex == currentPositionIndex)
            {
                //if no animation required just highlight the item

                if (currentPositionIndex != -1 && _animate)
                {
                    var item = (PlayerEventLeaderboardItem) _leaderboardItems[currentPositionIndex];
                    EventLeaderboardItemView itemView = null;
                    switch (item.Type)
                    {
                        case PlayerItemType.OwnSubmitted:
                            itemView = _playerListItemViewSubmitted;
                            SmoothChangeItemScore(itemView, 0, _throbDuration);
                            break;
                        case PlayerItemType.OwnUnsubmitted:
                            itemView = _playerListItemViewUnsubmitted;
                            SmoothChangeItemScore(itemView, _lastScoreShown, _throbDuration);
                            break;
                    }


                    if (itemView != null)
                        itemView.Throb(_throbScale, _throbDuration, _throbLoops);

                    switch (effectType)
                    {
                        case EffectType.Submit:
                            _submitGoEffectAnimator.gameObject.SetActive(true);
                            break;
                        case EffectType.LeagueUp:
                            _leagueUpGoEffectAnimator.gameObject.SetActive(true);
                            break;
                    }
                }

                onDone.SafeInvoke();
            }
            else
            {
                if (prevPositionIndex == currentPositionIndex)
                {
                    Debug.LogError("Something is wrong: move delta can't be zero");
                }

                _loopScrollRect.gameObject.SetActive(true);
                _leaderboardItems.MoveFromTo(prevPositionIndex, currentPositionIndex);
                var smoothMoveDuration =  CalculateSmoothMoveDuration(prevPositionIndex, currentPositionIndex);
                _loopScrollRectAnimator.Settings.animatedMoveFarSmoothDuration = smoothMoveDuration;
                _loopScrollRectAnimator.AnimatedMoveItemHighlightedWithAutofocus(prevPositionIndex, 
                    currentPositionIndex,
                    onBeforeItemScaleUp : OnItemStartScaleUpCallback,
                    onBeforeItemScaleBack : OnItemStartScaleBackCallback,
                    onDone: onDone);
                    
                SmoothChangeItemScore(_playerListItemViewUnsubmitted, _lastScoreShown, smoothMoveDuration);
                SmoothChangeItemRank(_playerListItemViewUnsubmitted, prevPositionIndex, smoothMoveDuration);

                void OnItemStartScaleUpCallback(ILoopScrollListItem item)
                {
                    AudioProxy.PlaySound(GenericSoundIds.GameEventProgressBar);
                }

                void OnItemStartScaleBackCallback(ILoopScrollListItem item)
                {
                    if (item is { isAlive: true })
                    {
                        var uiItem = item as EventLeaderboardItemView;
                        if (uiItem)
                        {
                            uiItem.SetPlayRankProgressFx(true);
                        }

                    }

                    AudioProxy.PlaySound(GenericSoundIds.QuestHud_Settle);
                    AudioProxy.PlaySound(GenericSoundIds.GenericBurst);
                }
            }

            _coroutine = null;
        }

        private void SmoothChangeItemScore(EventLeaderboardItemView itemView, int prevScore, float duration)
        {
            if (itemView)
            {
                var scoreIncrementTarget = itemView.Score;
                var startScoreToAnimate = prevScore == -1 ? 0 : prevScore;
                itemView.Score = startScoreToAnimate;
                itemView.IncrementScoreTo(scoreIncrementTarget, duration);
            }
        }

        private void SmoothChangeItemRank(EventLeaderboardItemView itemView, int prevPositionIndex, float duration)
        {
            if (itemView)
            { 
                var rankDecrementTarget = itemView.Rank;
                var startRankToAnimate = prevPositionIndex >= int.MaxValue ? 999 : prevPositionIndex + 1;  
                itemView.Rank = startRankToAnimate;
                itemView.DecrementRankTo(rankDecrementTarget, duration);
            }
        }

        /// <summary>
        /// Get animation duration to move item from one index to another.
        /// </summary>
        /// <remarks>
        /// Configruration contains min duration at min distance and max duration at max distace.
        /// Resulting duration is interpolated from current actual distance.
        /// </remarks>
        private float CalculateSmoothMoveDuration(int listIndex, int targetIndex)
        {
            var startPos = _loopScrollRect.GetListItemContentTargetAnchoredPosition(listIndex).y;
            var endPos = _loopScrollRect.GetListItemContentTargetAnchoredPosition(targetIndex).y;
            var dist = Mathf.Abs( startPos - endPos);
            if (Mathf.Approximately( dist, 0))
            {
                return _minMoveDuration;
            }

            var ratio = Mathf.Clamp01((dist - _minMoveDistance) / (_maxMoveDistance - _minMoveDistance) );
            var result = Mathf.Lerp(_minMoveDuration, _maxMoveDuration, ratio);
            return result;
        }
                                                   
        private void OnItemSpawned(ILoopScrollListItem item, object model, int index)
        {
            var curItem = model as EventLeaderboardItemBase;
            InitItem(item, curItem, index);
        }

        private void InitItem(object itemView, EventLeaderboardItemBase curItem, int index)
        {
            switch (itemView)
            {
                case EventLeaderboardItemView playerItemView:
                {

                    playerItemView.SetRewardButtonCallback(RewardButtonClickedHandler);

                    if (curItem is not PlayerEventLeaderboardItem playerItem)
                    {
                        BDebug.LogError(LogCat.General, $"Invalid type for item of index {index}, its current type is {curItem.GetType()}, expected='PlayerEventLeaderboardItem'");
                        return;
                    }

                    playerItemView.Setup(playerItem, _currentLeague, _commonViewData);

                    if (playerItem.Type == PlayerItemType.OwnSubmitted)
                    {
                        _playerListItemViewSubmitted = playerItemView;
                    }

                    if (playerItem.Type != PlayerItemType.OwnSubmitted && playerItemView == _playerListItemViewSubmitted)
                    {
                        _playerListItemViewSubmitted = null;
                    }

                    if (playerItem.Type == PlayerItemType.OwnUnsubmitted)
                    {
                        _playerListItemViewUnsubmitted = playerItemView;
                        if (_readyToSubmitState)
                        {
                            playerItemView.ChangeToReadyToSubmitState(_commonViewData);
                        }
                    }

                    if (playerItem.Type != PlayerItemType.OwnUnsubmitted && playerItemView == _playerListItemViewUnsubmitted)
                    {
                        _playerListItemViewUnsubmitted = null;
                    }
                    break;
                }
                case EventLeaderboardFireLeagueItemView fireLeagueBorderItemBiew:
                {
                    var promBorderItem = (FireLeagueTopBorderItem) curItem;
                    fireLeagueBorderItemBiew.Setup(promBorderItem.NumberOfPlayers, _commonViewData);
                    break;
                }
                case EventLeaderboardPromotionItemView promItemView:
                {
                    var promBorderItem = (PromotionZoneBorderItem) curItem;
                    promItemView.Setup(promBorderItem.NumberOfPlayers, _commonViewData);
                    break;
                }
                case EventLeaderboardDemotionItemView demItemView:
                {
                    var promBorderItem = (DemotionZoneBorderItem) curItem;
                    demItemView.Setup(promBorderItem.NumberOfPlayers, _commonViewData);
                    break;
                }
            }
        }

        private void RewardButtonClickedHandler(Dictionary<string, int> rewardDictionary, Transform targetTransform)
        {
            if (!_scrollEnded) return;
            _overlayDialogConfig.TargetTransform = targetTransform;
            _overlayDialogConfig.DisplayType = DisplayType.RewardSpeechBubble;
            _overlayDialogConfig.RewardToDisplay = rewardDictionary;
            _overlayDialogManager.ToggleOverlayDialog(_overlayDialogConfig);
        }

        public void HandleContentChangeStarted()
        {
            if (_loadingIndicator != null)
            {
                _loadingIndicator.SetActive(true);
            }

            _readyToSubmitState = false;

            ClearScrollRect();

            _loopScrollSnapItems.Clear();
            _leaderboardItems = null;
            _currentLeague = default;
        }

        private void SubscribeScroller()
        {
            UnsubscribeScroller();
            _loopScrollRect.onItemSpawnedEvent += OnItemSpawned;
            _loopScrollRect.onDragStarted += DragStartedHandler;
            _loopScrollRect.onScrollEnded += ScrollEndedHandler;
        }
        
        private void ScrollEndedHandler()
        {
            _scrollEnded = true;
        }

        private void DragStartedHandler()
        {
            _dragStartedHandler.SafeInvoke(null);
            _overlayDialogManager.HideAllOverlayDialogs();
            _scrollEnded = false;
        }

        private void UnsubscribeScroller()
        {
            _loopScrollRect.onDragStarted -= DragStartedHandler;
            _loopScrollRect.onItemSpawnedEvent -= OnItemSpawned;
            _loopScrollRect.onScrollEnded -= ScrollEndedHandler;
        }

        public void HandleContentChangeEnded(List<EventLeaderboardItemBase> items, League league)
        {
            _lastScoreShown = -1;
            _gameEventManager.SetLastScoreShown(_eventUid, -1);
            _leaderboardItems = items;
            _currentLeague = league;
            GetIndicies(items, out var ownSubmittedItemIndex, out var ownUnsubmittedItemIndex);

            if (ownUnsubmittedItemIndex != -1)
            {
                var playerItem = _leaderboardItems[ownUnsubmittedItemIndex] as PlayerEventLeaderboardItem;
                playerItem?.ResetScore();
            }

            if (ownSubmittedItemIndex != -1)
            {
                var playerItem = _leaderboardItems[ownSubmittedItemIndex] as PlayerEventLeaderboardItem;
                var place = playerItem?.Place ?? -1;
                _gameEventManager.SetLastRankShown(_eventUid, place);
            }

            UpdateScrollContentModel();

            if (_coroutine != null)
            {
                _coroutineExecutor.StopCoroutine(_coroutine);
            }

            _coroutine = _coroutineExecutor.StartCoroutine(IntroduceItem(-1, 
                ownSubmittedItemIndex, EffectType.LeagueUp, onDone: OnDone));
            return;

            void OnDone()
            {
                SetSnappedItemsTargetIndex(ownSubmittedItemIndex);
            }
        }

        private void SetSnappedItemsTargetIndex(int index)
        {
            _loopScrollSnapItems.SetTargetListItemsIndex(index, snapToTop: true, snapToBottom: true);
            var modelItem = index >= 0 && index < _leaderboardItems.Count ? _leaderboardItems[index] : null;
            if (_loopScrollSnapItems.TopSnappedItemInstance != null)
            {
                OnItemSpawned(_loopScrollSnapItems.TopSnappedItemInstance.GetComponent<ILoopScrollListItem>(), modelItem, index);
            }

            if (_loopScrollSnapItems.BottomSnappedItemInstance != null)
            {
                OnItemSpawned(_loopScrollSnapItems.BottomSnappedItemInstance.GetComponent<ILoopScrollListItem>(), modelItem, index);
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            UnsubscribeScroller();
            _onLeagueRefreshedCallback = null;
            _dragStartedHandler = null;
            _commonViewData?.Destroy();
        }
    }
}