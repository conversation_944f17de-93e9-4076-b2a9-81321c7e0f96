using System;
using BBB.DI;
using BBB.UI.Core;
using BebopBee.UnityEngineExtensions;
using UnityEngine;

namespace BBB.Core.UI
{
    public class GameEventReplaceableGo : BbbMonoBehaviour
    {
        private IGameEventResourceManager _resourceManager;
        private Transform _holder;

        public void Init(IGameEventResourceManager resourceManager)
        {
            _holder = transform;
            _resourceManager = resourceManager;
        }

        public void Refresh(string gameEventUid, string prefabName)
        {
            var prefab = _resourceManager.GetGenericAsset<GameObject>(gameEventUid, prefabName);
            _holder.gameObject.SetActive(prefab);
            if (prefab)
            {
                if (_holder.childCount > 0)
                {
                    var currentGo = _holder.GetChild(0).gameObject;
                    if (currentGo.name == prefab.name)
                        return;
                    Destroy(currentGo);
                    UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{prefab.name}]");
                    var go = Instantiate(prefab, _holder, false);
                    go.transform.SetSiblingIndex(0);
                    UnityEngine.Profiling.Profiler.EndSample();
                    go.name = prefab.name;
                }
                else
                {
                    UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{prefab.name}]");
                    var go = Instantiate(prefab, _holder, false);
                    UnityEngine.Profiling.Profiler.EndSample();
                    go.name = prefab.name;
                }
            }
            else if(_holder.childCount > 0)
            {
                var currentGo = _holder.GetChild(0).gameObject;
                Destroy(currentGo);
            }
        }

        public void Clear()
        {
            if(_holder.childCount > 0)
            {
                var currentGo = _holder.GetChild(0).gameObject;
                Destroy(currentGo);
            }
        }

        public void InvokeOnGo(Action<GameObject> setup)
        {
            if(_holder.childCount > 0)
            {
                var currentGo = _holder.GetChild(0).gameObject;
                setup(currentGo);
            }
        }
    }
}