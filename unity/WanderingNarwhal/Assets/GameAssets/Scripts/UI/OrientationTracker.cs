using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.UI.Core;
using BebopBee;
using UnityEngine;

namespace UI
{
    public class OrientationTracker : BbbMonoBehaviour, IOrientationTracker
    {
        public static bool ENABLED = false;

        // Height of iphone 6 display
        // private const float _minWidthInInches = 4.1f;
        private const float _minWidthInInches = 0f;

#if BBB_DEBUG
        private const float _maxAspectRatio = 16f / 9f;
#else
        private const float _maxAspectRatio = 16f / 10f;
#endif
        /// <summary>
        /// Required for particle system
        /// </summary>
        private const float GRAVITY = -9.81f;

        public static Dictionary<DeviceOrientation, Vector3> GravityPerOrientation = new Dictionary<DeviceOrientation, Vector3>()
        {
            {DeviceOrientation.Portrait, new Vector3(0f, GRAVITY, 0f)},
            {DeviceOrientation.PortraitUpsideDown, new Vector3(0f, -GRAVITY, 0f)},
            {DeviceOrientation.LandscapeLeft, new Vector3(GRAVITY, 0f, 0f)},
            {DeviceOrientation.LandscapeRight, new Vector3(-GRAVITY, 0f, 0f)},
        };

        public static readonly Dictionary<DeviceOrientation, float> AnglesRelativeToPortrait = new Dictionary<DeviceOrientation, float>()
        {
            {DeviceOrientation.Portrait, 0f},
            {DeviceOrientation.PortraitUpsideDown, Mathf.PI},
            {DeviceOrientation.LandscapeLeft, -0.5f * Mathf.PI},
            {DeviceOrientation.LandscapeRight, 0.5f * Mathf.PI},
        };

        private readonly List<ScreenType> _screens = new List<ScreenType>()
        {
            ScreenType.SideMapLevelScreen,
            ScreenType.LevelScreen,
            ScreenType.EpisodeScreen,
            ScreenType.SideMapScreen,
        };

        public event Action<DeviceOrientation> OrientationChanged;
        private IScreensManager _screensManager;

        [SerializeField] private bool _forceDebugEnable = false;
        [SerializeField] private DeviceOrientation _debugDeviceOrientation;
        [SerializeField] private bool _forceDisableEveryWhere = false;

        private DeviceOrientation _lastDeviceOrientation = DeviceOrientation.Portrait;
        private ScreenType _currentScreen;

        public void Init(IScreensManager screensManager)
        {
            _screensManager = screensManager;

            _screensManager.OnScreenChanged -= OnScreenChangedHandler;
            _screensManager.OnScreenChanged += OnScreenChangedHandler;

            float screenWidth = Screen.width / Screen.dpi;
            ENABLED = screenWidth >= _minWidthInInches && (float) Screen.height / Screen.width < _maxAspectRatio;

            if (_forceDebugEnable)
                ENABLED = true;

            if (_forceDisableEveryWhere)
                ENABLED = false;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            if (_screensManager != null)
                _screensManager.OnScreenChanged -= OnScreenChangedHandler;
        }

        private void OnScreenChangedHandler(ScreenType screenType, IScreensController arg2, IViewPresenter arg3)
        {
            _currentScreen = screenType;

            if (!_screens.Contains(_currentScreen))
            {
                _lastDeviceOrientation = DeviceOrientation.Portrait;
                OrientationChanged?.Invoke(_lastDeviceOrientation);
                RefreshGravity();
            }
        }

        private void Update()
        {
            var deviceOrientation = GetDeviceOrientation();

            switch (deviceOrientation)
            {
                case DeviceOrientation.Unknown:
                case DeviceOrientation.FaceUp:
                case DeviceOrientation.FaceDown:
                    break;
                case DeviceOrientation.Portrait:
                case DeviceOrientation.PortraitUpsideDown:
                case DeviceOrientation.LandscapeLeft:
                case DeviceOrientation.LandscapeRight:
                    if (deviceOrientation != _lastDeviceOrientation)
                    {
                        if (_screens.Contains(_currentScreen))
                        {
                            _lastDeviceOrientation = deviceOrientation;
                            OrientationChanged?.Invoke(deviceOrientation);
                            RefreshGravity();
                        }
                    }

                    break;
                default:
                    Debug.LogError("Unhandled Device Orientation: " + deviceOrientation);
                    break;
            }
        }

        private DeviceOrientation GetDeviceOrientation()
        {
            if (!ENABLED)
                return DeviceOrientation.Portrait;

            return AppDefinesConverter.UnityEditor ? _debugDeviceOrientation : Input.deviceOrientation;
        }

        public DeviceOrientation GetCurrentDeviceOrientation()
        {
            return _lastDeviceOrientation;
        }

        private void RefreshGravity()
        {
            if (GravityPerOrientation.ContainsKey(_lastDeviceOrientation))
            {
                Physics.gravity = GravityPerOrientation[_lastDeviceOrientation];
            }
        }

        public float GetAngleOfRotationFromTo(DeviceOrientation from, DeviceOrientation to)
        {
            if (!AnglesRelativeToPortrait.ContainsKey(from))
                from = DeviceOrientation.Portrait;

            if (!AnglesRelativeToPortrait.ContainsKey(to))
                to = DeviceOrientation.Portrait;

            var fromAngle = AnglesRelativeToPortrait[from];
            var toAngle = AnglesRelativeToPortrait[to];

            var diffAngle = toAngle - fromAngle;

            if (diffAngle < -Mathf.PI)
                diffAngle += 2f * Mathf.PI;
            else if (diffAngle > Mathf.PI)
                diffAngle -= 2f * Mathf.PI;

            return diffAngle;
        }

        public bool IsRotationFlip(DeviceOrientation from, DeviceOrientation to)
        {
            return from == DeviceOrientation.Portrait && to == DeviceOrientation.PortraitUpsideDown
                   || from == DeviceOrientation.PortraitUpsideDown && to == DeviceOrientation.Portrait
                   || from == DeviceOrientation.LandscapeLeft && to == DeviceOrientation.LandscapeRight
                   || from == DeviceOrientation.LandscapeRight && to == DeviceOrientation.LandscapeLeft;
        }

        public Vector2 GetOrientationRotatedVector(Vector2 from, bool inverseLandscape = false)
        {
            var rotatedVector = from;

            var currentDeviceOrientation = GetCurrentDeviceOrientation();
            if (currentDeviceOrientation != DeviceOrientation.Portrait)
            {
                var angle = AnglesRelativeToPortrait[currentDeviceOrientation];

                rotatedVector = new Vector2(from.x * Mathf.Cos(angle) - from.y * Mathf.Sin(angle),
                    from.x * Mathf.Sin(angle) + from.y * Mathf.Cos(angle));

                if (inverseLandscape &&
                    (currentDeviceOrientation == DeviceOrientation.LandscapeLeft || currentDeviceOrientation == DeviceOrientation.LandscapeRight))
                    rotatedVector.y *= -1f;
            }

            return rotatedVector;
        }
    }
}