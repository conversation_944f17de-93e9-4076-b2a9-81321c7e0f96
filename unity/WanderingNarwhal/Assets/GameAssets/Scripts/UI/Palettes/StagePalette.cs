using BBB;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;

public class StagePalette : BbbMonoBehaviour, IPalette
{
    [SerializeField] private Color _baseColor;
    [SerializeField] private Sprite _progressBarSprite;
    [SerializeField] private Sprite _hatSprite;
    [SerializeField] private Sprite _starSprite;
    [SerializeField] private Sprite _stampSprite;
    [SerializeField] private TMP_ColorGradient _gradientConfig;
    [SerializeField] private Sprite _topElementBgSprite;
    [SerializeField] private Sprite _fennecBgSprite;
    [SerializeField] private Sprite _boosterHighlightSprite;
    [SerializeField] private Sprite _boosterBgSprite;
    [SerializeField] private Color _settingsBgColor;
    [SerializeField] private Color _shadowColor;
    [SerializeField] private Color _shadow2Color;
    [SerializeField] private Color _bgColor;
    [SerializeField] private Gradient _gradient;
    [SerializeField] private Sprite _ribbonSprite;
    [SerializeField] private Color _separatorColor;
    [Serial<PERSON><PERSON>ield] private Color _separatorShadowColor;

    public Color GetColor1()
    {
        return _baseColor;
    }

    public Color GetColor2()
    {
        return Color.white;
    }

    public Color GetColor3()
    {
        return Color.white;
    }

    public Color GetColor4()
    {
        return Color.white;
    }
    
    public Color GetColor5()
    {
        return Color.white;
    }

    public TMP_ColorGradient GetGradientConfig()
    {
        return _gradientConfig;
    }

    public Color GetBgColorConfig()
    {
        return _bgColor;
    }

    public Sprite GetBgSprite()
    {
        return _topElementBgSprite;
    }

    public Sprite GetFennecBgSprite()
    {
        return _fennecBgSprite;
    }
    
    public Color GetSettingsBgColor()
    {
        return _settingsBgColor;
    }

    public Color GetShadowColor()
    {
        return _shadowColor;
    }

    public Color GetShadow2Color()
    {
        return _shadow2Color;
    }

    public Sprite GetBoosterSprite()
    {
        return _boosterHighlightSprite;
    }

    public Sprite GetBoosterBgSprite()
    {
        return _boosterBgSprite;
    }
    
    public Color GetSeparatorColor()
    {
        return _separatorColor;
    }

    public Color GetSeparatorShadowColor()
    {
        return _separatorShadowColor;
    }

    public Gradient GetGradient()
    {
        return _gradient;
    }

    public Sprite GetRibbonSprite()
    {
        return _ribbonSprite;
    }

    public Material GetMaterial1()
    {
        return null;
    }

    public Sprite GetSprite1()
    {
        return _progressBarSprite;
    }

    public Sprite GetSprite2()
    {
        return _hatSprite;
    }

    public Sprite GetSprite3()
    {
        return _starSprite;
    }

    public Sprite GetSprite4()
    {
        return _stampSprite;
    }
}
