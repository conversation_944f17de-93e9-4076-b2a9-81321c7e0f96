using BBB.Core;
using BBB.DI;
using UnityEngine;

namespace GameAssets.Scripts.Collection.UI
{
    public class WildCardModalController : BaseModalsController<IWildCardModalViewPresenter>
    {
        private ICollectionManager _collectionManager;
        private WildCardButtonWidget _wildCardButtonWidget;

        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);
            _collectionManager = context.Resolve<ICollectionManager>();
        }

        public void Setup(WildCardButtonWidget wildCardButtonWidget)
        {
            _wildCardButtonWidget = wildCardButtonWidget;
        }

        protected override void OnShow()
        {
            base.OnShow();
            Subscribe();
            View.ShowWildCardIconWithEffect(_wildCardButtonWidget);
        }

        protected override void OnHide()
        {
            base.OnHide();
            Unsubscribe();
            _wildCardButtonWidget = null;
        }

        private void Subscribe()
        {
            Unsubscribe();
            View.OnUseButton += UseClickedHandler;
        }

        private void Unsubscribe()
        {
            View.OnUseButton -= UseClickedHandler;
        }

        private void UseClickedHandler()
        {
            _collectionManager.UseWildCard(_wildCardButtonWidget.transform);
        }

        public void EnableUseButton()
        {
            View.EnableUseButton();
        }

        public void DisableUseButton()
        {
            View.DisableUseButton();
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            Unsubscribe();
        }
    }
}
