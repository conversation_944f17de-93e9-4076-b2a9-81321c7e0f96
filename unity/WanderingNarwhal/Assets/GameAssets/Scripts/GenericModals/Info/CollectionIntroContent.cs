using BBB;
using Spine.Unity;
using UnityEngine;

namespace GameAssets.Scripts.GenericModals.Info
{
    public class CollectionIntroContent : BbbMonoBehaviour
    {
        [SerializeField] private SkeletonGraphic _introAnim;

        protected override void OnDestroy()
        {
            base.OnDestroy();

            var introAnimSkeletonDataAsset = _introAnim.skeletonDataAsset;
            var introAnimMainTexture = _introAnim.mainTexture;

            _introAnim.skeletonDataAsset = null;
            _introAnim.Clear();
            _introAnim = null;

            Resources.UnloadAsset(introAnimSkeletonDataAsset);
            Resources.UnloadAsset(introAnimMainTexture);
        }
    }
}