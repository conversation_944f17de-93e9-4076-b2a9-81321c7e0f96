using System;
using BBB.DI;
using BBB.Social;
using BebopBee.Core.UI;
using GameAssets.Scripts.GenericModals.Info.Core;

namespace GameAssets.Scripts.GenericModals.Info
{
    public class SdbIntroModel : GenericInfoModel, IContextInitializable
    {
        private SdbManager _sdbManager;

        private Action<bool> _closeCallback;
        private bool _okayButtonClicked;

        public override string ConfigName => "GenericInfoModalConfig_SuperDiscoIntro";
        public override string Name => "SdbIntroModel";

        public SdbIntroModel(Action<bool> closeCallback)
        {
            _closeCallback = closeCallback;
        }

        public virtual void InitializeByContext(IContext context)
        {
            _sdbManager = context.Resolve<SdbManager>();
        }

        public override void OnShow()
        {
            _okayButtonClicked = false;
        }

        public override void OnHide()
        {
            _closeCallback.SafeInvoke(_okayButtonClicked);
            _closeCallback = null;
            _okayButtonClicked = false;
        }

        public override void PrimaryButtonHandler()
        {
            _okayButtonClicked = true;
        }

        public override void InfoButtonHandler()
        {
            _sdbManager.ShowTutorialModal(TutorialModalHiddenHandler);
        }

        protected void TutorialModalHiddenHandler()
        {
            ModalController.Show(this, ShowMode.Delayed);
        }
    }
}