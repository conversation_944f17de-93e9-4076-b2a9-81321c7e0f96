using BBB;
using BBB.Audio;
using BebopBee.Core.Audio;
using GameAssets.Scripts.GenericModals.Tutorial.Core;
using UnityEngine;

namespace GameAssets.Scripts.GenericModals.Tutorial
{
    public class CompetitionEventLeagueRewardsModel : GenericTutorialModel
    {
        private readonly CompetitionGameEvent _gameEvent;
        public override string ConfigName => "GenericTutorialModalConfig_CompetitionEventLeagueRewards";
        public override string Name  => "CompetitionEventLeagueRewardsModel";

        public CompetitionEventLeagueRewardsModel(CompetitionGameEvent competitionGameEvent)
        {
            _gameEvent = competitionGameEvent;
            EventUid = competitionGameEvent.Uid;
        }

        public override void OnShow()
        {
            AudioProxy.PlaySound(GenericSoundIds.OpenWallet);
        }

        public override void OnHide()
        {
        }

        public override void OnSetupContent(GameObject contentInstance)
        {
            var gameEventVictoryContent = contentInstance.GetComponent<CompetitionEventLeagueRewardsContent>();
            gameEventVictoryContent.Setup(_gameEvent);
        }
    }
}