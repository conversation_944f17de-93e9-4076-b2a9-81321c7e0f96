using BBB.RaceEvents;
using GameAssets.Scripts.GenericModals.Tutorial.Core;
using UnityEngine;

namespace GameAssets.Scripts.GenericModals.Tutorial
{
    public class RoyaleEventWinModel : GenericTutorialModel
    {
        private readonly RoyaleEvent _royaleEvent;
        public override string ConfigName => "GenericTutorialModalConfig_RoyaleEventWin";
        public override string Name  => "RoyaleEventWinModel";

        public RoyaleEventWinModel(RoyaleEvent royaleEvent)
        {
            _royaleEvent = royaleEvent;
            EventUid = royaleEvent.Uid;
        }

        public override void OnShow()
        {
        }

        public override void OnHide()
        {
        }

        public override void OnSetupContent(GameObject contentInstance)
        {
            var royaleEventWinContent = contentInstance.GetComponent<RoyaleEventWinContent>();
            royaleEventWinContent.Setup(_royaleEvent);
        }
    }
}