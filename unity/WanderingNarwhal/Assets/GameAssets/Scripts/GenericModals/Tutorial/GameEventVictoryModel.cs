using BBB;
using GameAssets.Scripts.GenericModals.Tutorial.Core;
using UnityEngine;

namespace GameAssets.Scripts.GenericModals.Tutorial
{
    public class GameEventVictoryModel : GenericTutorialModel
    {
        private readonly GameEventBase _gameEvent;
        public override string ConfigName => "GenericTutorialModalConfig_GameEventVictory";
        public override string Name  => "GameEventVictoryModel";

        public GameEventVictoryModel(GameEventBase gameEvent)
        {
            _gameEvent = gameEvent;
            EventUid = gameEvent.Uid;
        }

        public override void OnShow()
        {
        }

        public override void OnHide()
        {
        }

        public override void OnSetupContent(GameObject contentInstance)
        {
            var gameEventVictoryContent = contentInstance.GetComponent<GameEventVictoryContent>();
            gameEventVictoryContent.Setup(_gameEvent.Uid);
        }
    }
}