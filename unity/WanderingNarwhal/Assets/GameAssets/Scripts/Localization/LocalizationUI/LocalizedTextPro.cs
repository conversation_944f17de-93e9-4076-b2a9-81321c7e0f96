using System.Collections;
using UnityEngine;
using TMPro;
using System.Collections.Generic;
using BBB;
using Bebopbee.Core.Extensions.Unity;

[RequireComponent(typeof(TextMeshProUGUI))]
public class LocalizedTextPro : BbbMonoBehaviour
{
    private static ILocalizationManager _localizationManager;

    [SerializeField] private string _textId = string.Empty;
    [SerializeField] private bool _breakLines = true;
    [SerializeField] private List<TextMeshProUGUI> _secondaryTexts;
    [SerializeField] private bool _resizeFont;

    private object[] _args;

    public TextMeshProUGUI Text { get; private set; }

    public string TextId => _textId;

    public static void Init(ILocalizationManager locManager)
    {
        _localizationManager = locManager;
    }

    void Awake()
    {
        Text = gameObject.GetComponent<TextMeshProUGUI>();
    }

    private IEnumerator Start()
    {
        if (!_resizeFont)
            yield break;
        
        yield return null; //wait resize from canvas layouts
        var text = Text?.text;
        if (!text.IsNullOrEmpty())
        {
            Text.ResizeFont();
        }
    }

    protected override void OnEnable()
    {
        base.OnEnable();
        //Text.OnEnable() is invoked also in editorMode, we have to choose when to call the singleton.
#if UNITY_EDITOR
        if (Application.isPlaying)
        {
#endif
            LoadLocalizedText(_args);
#if UNITY_EDITOR
        }
#endif
    }

    private void LoadLocalizedText(params object[] args)
    {
        if (Text == null)
        {
            Text = gameObject.GetComponent<TextMeshProUGUI>();
        }
        var textId = _textId?.Trim();
        if (textId.IsNullOrEmpty()) return;
        
        if (_localizationManager != null)
        {
            var newText = args is { Length: > 0 } ? _localizationManager.getLocalizedTextWithArgs(_textId, args) : _localizationManager.getLocalizedText(_textId);

            if (newText == null)
            {
                Debug.LogError($"LocalizedText ({gameObject.name}) Missing textId ({_textId}) for localization");
            }
            else
            {
                if (_breakLines)
                {
                    newText = Util.BreakLines(newText);
                }

                if (!newText.Equals(Text.text))
                {
                    SetTextValue(newText);
                }
            }
        }
        else if (textId != null && textId.Equals(Text.text))
        {
            SetTextValue(_textId);
        }
    }

    public void ClearText()
    {
        if (Text == null)
        {
            Text = gameObject.GetComponent<TextMeshProUGUI>();
        }

        SetTextValue(string.Empty);
        _textId = string.Empty;
    }
    
    public void SetTextAsset(TMP_SpriteAsset spriteAsset)
    {
        if (Text == null)
        {
            Text = GetComponent<TextMeshProUGUI>();
        }

        if (Text != null)
        {
            Text.spriteAsset = spriteAsset;
        }
    }
    
    public void SetRawText(string text)
    {
        ClearText();
        SetTextValue(text);
    }

    private void SetTextValue(string textValue)
    {
        if (_resizeFont)
        {
            Text.SetTextAndResizeFont(textValue);
        }
        else
        {
            Text.text = textValue;
        }

        if (_secondaryTexts == null) return;
        
        foreach (var textMeshPro in _secondaryTexts)
        {
            if (textMeshPro == null)
                continue;

            if (_resizeFont)
            {
                textMeshPro.SetTextAndResizeFont(textValue);
            }
            else
            {
                textMeshPro.text = textValue;
            }
        }
    }

    /// <summary>
    /// Used in visual scripting
    /// </summary>
    /// <param name="textId"></param>
    public void SetTextId(string textId)
    {
        SetTextId(textId, null);
    }
    
    public virtual void SetTextId(string textId, params object[] args)
    {
        _textId = textId;
        _args = args;
#if UNITY_EDITOR
        if (Application.isPlaying)
        {
#endif
            LoadLocalizedText(args);
#if UNITY_EDITOR
        }
#endif
    }


    public void FormatSetArgs(params object[] args)
    {
        if (_textId.IsNullOrEmpty())
        {
            Debug.LogError("Can't FormatSetArgs to empty localization id", this);
            return;
        }

        SetTextId(_textId, args);
    }
}