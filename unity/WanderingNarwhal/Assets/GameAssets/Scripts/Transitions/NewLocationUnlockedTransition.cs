using BBB.Audio;
using UnityEngine;
using BBB.Core.ResourcesManager;
using BebopBee.Core.Audio;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Theme;
using Object = UnityEngine.Object;

namespace BBB.UI.Transitions.Impl
{
    public class NewLocationUnlockedTransition : TransitionBase
    {
        private RectTransform _transitionsHolder;
        private GameObject _transitionPrefab;
        private TransitionPrefab _transition;

        public NewLocationUnlockedTransition(GameObject transitionPrefab)
        {
            _transitionPrefab = transitionPrefab;
        }

        public override bool Begin(ITransitionScreen screen, string uid, ScreenType previousScreen)
        {
            _transition.Show();
            return true;
        }

        public override void End()
        {
            AudioProxy.PlaySound(GenericSoundIds.TransitionScreenOut);
            if (_transition != null)
                _transition.Hide();
        }

        public override void Init(TransitionData data, IConfig config, RectTransform transitionsHolder, IGameEventManager gameEventManager)
        {
            _transitionsHolder = transitionsHolder;
            _transition = Object.Instantiate(_transitionPrefab, _transitionsHolder).GetComponent<TransitionPrefab>();
            _transition.Hide(true);
        }

        public override UniTask PreloadAsync(IAssetsManager assetManager, IThemeManager themeManager)
        {
            return UniTask.CompletedTask;
        }

        public override bool IsLoaded()
        {
            return _transition != null;
        }

        public override bool IsShown()
        {
            return _transition != null && _transition.IsShown;
        }
        
        public override void Destroy()
        {
            base.Destroy();
            _transition = null;
        }
    }
}