using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.Narrative.Views;
using GameAssets.Scripts.Narrative.Controllers;
using PBConfig;

namespace BBB.Narrative.Controllers
{
    public class DialogModalController : BaseModalsController<IDialogModalViewPresenter>
    {
        private Dialog _dialog = new Dialog();
        private string _dialogUid;
        private Action _endCallback;
        private IConfig _config;
        private DialogType _dialogType;
        private Dictionary<string, object> _specialParams;

        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);

            _config = context.Resolve<IConfig>();
        }

        protected override void OnShow()
        {
            base.OnShow();

            if (_dialog.IsNotEmpty())
            {
                View.SetupDialog(_dialogUid, _dialog, _dialogType, _specialParams);
            }
            else
            {
                Hide();
            }
        }

        private void TryToGenerateDialog()
        {
            _dialog.Clear();
            var narrativeDialogConfigs = _config.Get<NarrativeDialogConfig>();
            if (narrativeDialogConfigs != null && !_dialogUid.IsNullOrEmpty() && narrativeDialogConfigs.TryGetValue(_dialogUid, out var narrativeDialogConfig))
            {
                var listOfPhrases = narrativeDialogConfig.Dialog;

                if (listOfPhrases != null)
                {
                    _dialog.Generate(listOfPhrases);
                }
            }
        }

        protected override void OnHide()
        {
            base.OnHide();
            OnDialogEnded();
        }

        private void OnDialogEnded()
        {
            var endCallbackTmp = _endCallback;
            _endCallback = null;
            _dialogUid = null;
            endCallbackTmp.SafeInvoke();
        }

        public override void Setup(ModalSetupParamsBase setupParams)
        {
            if (setupParams is DialogModalSetupParams dialogModalSetupParams)
            {
                _dialogUid = dialogModalSetupParams.DialogUid;
                _dialogType = dialogModalSetupParams.DialogType;
                _specialParams = dialogModalSetupParams.SpecialParams;
                _endCallback = dialogModalSetupParams.EndCallback;
                TryToGenerateDialog();
            }
            else
            {
                BDebug.LogError(LogCat.Modals, "Wrong setup params type for DialogModalController: " + setupParams.GetType().Name);
            }
        }
    }
}