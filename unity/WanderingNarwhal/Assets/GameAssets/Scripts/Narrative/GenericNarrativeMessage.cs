using System;
using Cysharp.Threading.Tasks;
using PBConfig;
using UnityEngine;
using UnityEngine.UI;

namespace BBB
{
    public class GenericNarrativeMessage : BbbMonoBehaviour
    {
        [SerializeField] private Transform _tipHolderTransform;
        [SerializeField] private Transform _tagHolderTransform;
        [SerializeField] private Transform _tagTextTransform;
        [SerializeField] private GlobalNarrativeSettings _narrativeSettings;
        [SerializeField] private LocalizedTextPro _tagText;
        [SerializeField] private LocalizedTextPro _messageText;
        [SerializeField] private Image _tagBackground;
        [SerializeField] private Image _tagShadow;
        [SerializeField] private Image _tagOutline;
        [SerializeField] private Animator _animator;

        private UniTaskCompletionSource<bool> _showTcs;
        private UniTaskCompletionSource<bool> _hideTcs;
        private static readonly int ShowId = Animator.StringToHash("Show");
        private static readonly int HideId = Animator.StringToHash("Hide");

        public void Setup(NarrativeCharacterWidget characterWidget, Phrase phrase)
        {
            var type = characterWidget.SpeechBubbleType;
            var colorPalette = characterWidget.SpeechBubbleColorPalette;

            switch (type)
            {
                case SpeechBubbleType.LeftHandGeneric:
                    SetFlip(1f);
                    break;
                case SpeechBubbleType.RightHandGeneric:
                    SetFlip(-1f);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(type), type, null);
            }

            var colorSettings = _narrativeSettings.GetSettings(colorPalette);
            _tagBackground.color = colorSettings.TagBackgroundColor;
            _tagShadow.color = colorSettings.TagShadowColor;
            _tagOutline.color = colorSettings.TagOutlineColor;
            _messageText.Text.color = colorSettings.MessageTextColor;

            transform.localPosition = characterWidget.MessagePosition;

            _tagText.SetTextId(phrase.NameTag);
            _messageText.SetTextId(phrase.Text);
        }

        public UniTask ShowAsync()
        {
            _animator.Rebind();
            gameObject.SetActive(true);

            _showTcs = new UniTaskCompletionSource<bool>();

            _animator.SetTrigger(ShowId);

            return _showTcs.Task;
        }

        public UniTask HideAsync()
        {
            _hideTcs = new UniTaskCompletionSource<bool>();
            _animator.SetTrigger(HideId);
            return _hideTcs.Task;
        }

        private void OnShownAnimationFinished()
        {
            _showTcs?.TrySetResult(true);
            _showTcs = null;
        }

        private void OnHiddenAnimationFinished()
        {
            _hideTcs?.TrySetResult(true);
            _hideTcs = null;
            gameObject.SetActive(false);
        }

        private void SetFlip(float flipScale)
        {
            var tipScale = _tipHolderTransform.localScale;
            tipScale.x = flipScale;
            _tipHolderTransform.localScale = tipScale;
            var tagScale = _tagHolderTransform.localScale;
            tagScale.x = flipScale;
            _tagHolderTransform.localScale = tagScale;
            var tagTextScale = _tagTextTransform.localScale;
            tagTextScale.x = flipScale;
            _tagTextTransform.localScale = tagTextScale;
        }

        public void Reset()
        {
            gameObject.SetActive(false);
            _animator.Rebind();
            _showTcs = null;
            _hideTcs = null;
        }

        public void FastForward()
        {
            _animator.ResetTrigger(ShowId);
            _animator.ResetTrigger(HideId);
            _animator.Play("Idle");
            OnShownAnimationFinished();
        }
    }
}