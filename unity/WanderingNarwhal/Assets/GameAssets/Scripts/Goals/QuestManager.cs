using System;
using UnityEngine;
using System.Collections.Generic;
using PBGame;
using FBConfig;
using BBB.Core.Wallet;
using BBB.Quests.Objectives;
using BBB.Wallet;
using BBB.Core.Analytics;
using BBB.Core;
using BBB.UI.Core;
using Core.Configs;
using GameAssets.Scripts.Lua;
using UnityEngine.Profiling;

namespace BBB
{
    public enum QuestState
    {
        InProgress = 0,
        AllObjectivesClaimed = 1,
        WaitingForCondition = 2,
    }

    public enum QuestType
    {
        Special = 0,
        Other = 1
    }

    public class QuestManager : IObjectiveConfigProvider
    {
        private static readonly Type[] RequiredConfigs =
        {
            typeof(QuestConfig)
        };

        private IConfig _config;
        private IList<string> _completedQuests;
        private IList<string> _expiredQuests;
        private IList<string> _inProgressQuests;
        private QuestManagerValidator _questManagerValidator;
        private IDictionary<string, QuestConfig> _questsConfigDictionary;
        private IDictionary<string, Quest> _questsMap;
        private IList<QuestConfig> _questsConfigList;

        private PlayerObjectivesManager _playerObjectivesManager;
        private LuaManager _luaManager;
        private IEventDispatcher _eventDispatcher;
        private IWalletManager _walletManager;
        private IUIWalletManager _uiWalletManager;
        private IPlayerManager _playerManager;
        private IPlayer Player => _playerManager.Player;
        private IScreensManager _screensManager;
        private ScreenType _currentScreenType;

        private void OnScreenChanged(ScreenType screenType, IScreensController arg2, IViewPresenter arg3)
        {
            _currentScreenType = screenType;
            if ((_currentScreenType & ScreenType.Levels) == 0)
            {
                QuestsConditionsRefresh();
            }
        }

        private void Subscribe()
        {
            Unsubscribe();

            Player.ModalVisited += ModalVisitedHandler;
            _screensManager.OnScreenChanged += OnScreenChanged;
        }

        private void Unsubscribe()
        {
            if (Player != null)
            {
                Player.ModalVisited -= ModalVisitedHandler;
            }

            if (_screensManager != null)
            {
                _screensManager.OnScreenChanged -= OnScreenChanged;
            }

            _eventDispatcher?.Unsubscribe(this);
        }

        private void ModalVisitedHandler(string obj)
        {
            QuestsConditionsRefresh();
        }

        private void ObjectiveCompletedEventHandler(PBObjectiveProgress objectiveProgress)
        {
            var config = _questsConfigDictionary[objectiveProgress.ParentUid];
            var objectiveConfig = FlatBufferHelper.Find(config.ObjectivesFb, config.ObjectivesFbLength, x => x?.Uid == objectiveProgress.Uid).Value;
            var questConfig = _questsConfigDictionary[objectiveProgress.ParentUid];

            var objectiveCompletedEvent = _eventDispatcher.GetMessage<ObjectiveCompletedEvent>();
            objectiveCompletedEvent.Set(new ObjectiveData(objectiveConfig, questConfig, objectiveProgress));
            _eventDispatcher.TriggerEventNextFrame(objectiveCompletedEvent);
            _playerObjectivesManager.ForceCheckObjectives();
        }

        private void ObjectiveExpiredEventHandler(PBObjectiveProgress objectiveProgress)
        {
            var questConfig = _questsConfigDictionary[objectiveProgress.ParentUid];
            if (HasQuest(questConfig.Uid))
            {
                UpdateQuestProgressForExpiration(GetQuest(questConfig.Uid));
            }
        }

        public void Init(IConfig config, PlayerObjectivesManager playerObjectivesManager,
            LuaManager luaManager,
            IEventDispatcher eventDispatcher,
            IWalletManager walletManager,
            IPlayerManager playerManager,
            IScreensManager screensManager)
        {
            _playerManager = playerManager;
            _eventDispatcher = eventDispatcher;
            _walletManager = walletManager;

            _screensManager = screensManager;

            _config = config;
            _completedQuests = Player.CompletedQuests;
            _expiredQuests = Player.ExpiredQuests;
            _luaManager = luaManager;

            _inProgressQuests = new List<string>();
            _questManagerValidator = new QuestManagerValidator(_config, _playerManager, this);

            _playerObjectivesManager = playerObjectivesManager;

            Profiler.BeginSample("QuestManager InitConfigs");
            InitConfigs(_config);
            Profiler.EndSample();

            Config.OnConfigUpdated -= InitConfigs;
            Config.OnConfigUpdated += InitConfigs;

            Profiler.BeginSample("QuestManager ValidateBackwardCampatibility");
            _questManagerValidator.ValidateBackwardCampatibility();
            Profiler.EndSample();

            _playerObjectivesManager.RegisterConfigProvider(this);

            _playerObjectivesManager.OnObjectiveCompleted -= ObjectiveCompletedEventHandler;
            _playerObjectivesManager.OnObjectiveCompleted += ObjectiveCompletedEventHandler;

            _playerObjectivesManager.OnObjectiveExpired -= ObjectiveExpiredEventHandler;
            _playerObjectivesManager.OnObjectiveExpired += ObjectiveExpiredEventHandler;

            Profiler.BeginSample("QuestManager ValidateInProgressQuests");
            ValidateInProgressQuests();
            Profiler.EndSample();

            CleanUselessQuestData();

            Subscribe();
        }

        public void SetUIWalletManager(UIWalletManager uiWalletManager)
        {
            _uiWalletManager = uiWalletManager;
        }

        private void CleanUselessQuestData()
        {
            // We don't use this dictionary anymore, it should be cleaned for old players
            Player.QuestProgress?.Clear();

            foreach (var questUid in _completedQuests)
            {
                if (!HasQuest(questUid))
                    continue;

                foreach (var questObjectiveConfig in GetQuest(questUid).GetObjectives())
                {
                    _playerObjectivesManager.RemoveObjective(questUid, questObjectiveConfig.Uid);
                }
            }

            foreach (var questUid in _expiredQuests)
            {
                if (!HasQuest(questUid))
                    continue;

                foreach (var questObjectiveConfig in GetQuest(questUid).GetObjectives())
                {
                    _playerObjectivesManager.RemoveObjective(questUid, questObjectiveConfig.Uid);
                }
            }
        }

        private void InitConfigs(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            _questsConfigDictionary = config.Get<QuestConfig>();
            
            if (_questsConfigDictionary != null)
            {
                _questsConfigList = new List<QuestConfig>(_questsConfigDictionary.Values.Count);
                foreach (var questConfig in _questsConfigDictionary.Values)
                {
                    _questsConfigList.Add(questConfig);
                }
            }
            else
            {
                _questsConfigList = new List<QuestConfig>();
            }

            Profiler.BeginSample("QuestManager InitQuests");
            InitQuests();
            Profiler.EndSample();
        }
        
        private void InitQuests()
        {
            _questsMap = new Dictionary<string, Quest>();
            foreach (var questConfig in _questsConfigDictionary.Values)
            {
                var quest = new Quest(questConfig, this, _playerObjectivesManager);
                _questsMap.Add(questConfig.Uid, quest);
            }
        }

        /// <summary>
        /// Iterates through not started quests yet
        /// Return true if any quest was started
        /// </summary>
        private bool ForceCheckQuests()
        {
            var anyQuestStarted = false;

            foreach (var questConfig in _questsConfigList)
            {
                if (!CanStartQuest(questConfig)) continue;
                
                StartQuest(questConfig);
                anyQuestStarted = true;
            }

            foreach (var inProgressQuest in _inProgressQuests)
            {
                TryStartNextObjective(_questsConfigDictionary[inProgressQuest]);
            }

            return anyQuestStarted;
        }

        /// <summary>
        /// Consequencely updates quests and objectives states even if completion of one depends on another in one call
        /// CAN BE PERFORMANCE HEAVY, DON'T use it each frame
        /// </summary>
        public void QuestsConditionsRefresh()
        {
            bool anyStateUpdated;
            var iterationsLimitReached = false;

            var iterationsCount = 0;

            do
            {
                var anyQuestStarted = ForceCheckQuests();
                var anyObjectiveCompletedOrExpired = _playerObjectivesManager.ForceCheckObjectives();

                anyStateUpdated = anyQuestStarted || anyObjectiveCompletedOrExpired;

                iterationsCount++;

                if (iterationsCount > 3)
                {
                    iterationsLimitReached = true;
                }
            } while (anyStateUpdated && !iterationsLimitReached);

            if (iterationsLimitReached)
            {
                BDebug.LogError(LogCat.Quest, "IGNORE IF YOU USED MULTIPLE LEVELS UNLOCKATION, Quests refresh was stopped by iterations limit");
            }

            BDebug.Log(LogCat.Quest, $"Number of iterations on quest progression update: {iterationsCount}");
        }

        public bool HasQuest(string questUid)
        {
            return !questUid.IsNullOrEmpty() && _questsMap.ContainsKey(questUid);
        }

        public Quest GetQuest(string questUid)
        {
            if (!questUid.IsNullOrEmpty() && _questsMap.TryGetValue(questUid, out var quest))
            {
                return quest;
            }

            Debug.LogError("Incorrect questUid: " + questUid);
            return null;
        }

        public Quest GetQuestByRequired(string questUid)
        {
            if (questUid.IsNullOrEmpty())
            {
                Debug.LogError("Incorrect questUid: " + questUid);
                return null;
            }

            foreach (var quest in _questsMap.Values)
            {
                if (quest.IsQuestRequired(questUid))
                    return quest;
            }

            return null;
        }


        public List<Quest> GetQuestsInProgress(string location)
        {
            var result = new List<Quest>();
            foreach (var questUid in _inProgressQuests)
            {
                if (!_questsMap.ContainsKey(questUid)) continue;
                
                if (_questsMap[questUid].GetLocation() == location)
                {
                    result.Add(_questsMap[questUid]);
                }
            }

            return result;
        }

        public List<Quest> GetQuestsInProgressByType(string location, QuestType questType)
        {
            var questsInProgress = GetQuestsInProgress(location);
            var filteredQuests = new List<Quest>();

            foreach (var quest in questsInProgress)
            {
                if (quest.GetQuestType() == questType)
                {
                    filteredQuests.Add(quest);
                }
            }

            return filteredQuests;
        }

        private bool CanStartQuest(QuestConfig questConfig)
        {
            return IsQuestNonStarted(questConfig) && IsQuestRequirementSatisfied(questConfig) && IsQuestEnableConditionSatisfied(questConfig);
        }

        private bool IsQuestNonStarted(QuestConfig questConfig)
        {
            return !_completedQuests.Contains(questConfig.Uid) && !_inProgressQuests.Contains(questConfig.Uid);
        }

        public bool IsQuestInProgress(QuestConfig questConfig)
        {
            return _inProgressQuests.Contains(questConfig.Uid);
        }

        public bool IsQuestCompleted(QuestConfig questConfig)
        {
            return _completedQuests.Contains(questConfig.Uid);
        }

        public bool IsQuestCompleted(string questUid)
        {
            return _completedQuests.Contains(questUid);
        }

        public bool IsQuestExpired(string questUid)
        {
            return _expiredQuests.Contains(questUid);
        }

        private bool IsQuestRequirementSatisfied(QuestConfig questConfig)
        {
            if (questConfig.RequirementsLength == 0)
                return true;

            for (var i = 0; i < questConfig.RequirementsLength; i++)
            {
                var requirement = questConfig.Requirements(i);
                var requiredQuestCompleted = _completedQuests.Contains(requirement);
                var requiredQuestExpired = _expiredQuests.Contains(requirement);

                if (!requiredQuestCompleted && !requiredQuestExpired)
                    return false;
            }

            return true;
        }

        public bool IsQuestEnableConditionSatisfied(QuestConfig questConfig)
        {
            return questConfig.EnableCondition.IsNullOrEmpty() || _luaManager.CachedEvaluateExpression(questConfig.EnableCondition);
        }

        private void StartQuest(QuestConfig questConfig)
        {
            _inProgressQuests.Add(questConfig.Uid);

            TryStartNextObjective(questConfig);
            var questStartedEvent = _eventDispatcher.GetMessage<QuestStartedEvent>();
            questStartedEvent.Set(new QuestData(questConfig));
            _eventDispatcher.TriggerEvent(questStartedEvent);
        }

        private bool TryStartNextObjective(QuestConfig questConfig, bool executeActions = true)
        {
            var objToStart = GetNextObjectiveToStart(questConfig);
            StartNextObjective(objToStart, executeActions);
            return !objToStart.Equals(FlatBufferHelper.DefaultQuestObjectiveConfig);
        }

        private QuestObjectiveConfig GetNextObjectiveToStart(QuestConfig questConfig)
        {
            QuestObjectiveConfig objToStart = default;

            for (var i = 0; i < questConfig.ObjectivesFbLength; i++)
            {
                var obj = questConfig.ObjectivesFb(i).Value;
                if (!HasObjectiveStarted(obj.QuestId, obj.Uid))
                {
                    objToStart = obj;
                    break;
                }

                var objStatus = GetObjectiveStatus(obj.QuestId, obj.Uid);
                
                if (objStatus != ObjectiveState.Inactive) 
                    continue;
                
                var parentStatus = GetObjectiveStatus(obj.QuestId, obj.Parent);
                
                if (!obj.Parent.IsNullOrEmpty() && parentStatus != ObjectiveState.Done && parentStatus != ObjectiveState.Claimed && parentStatus != ObjectiveState.Expired) 
                    continue;
                
                objToStart = obj;
                break;
            }

            return objToStart;
        }

        private void StartNextObjective(QuestObjectiveConfig objectiveToStart, bool executeActions = true)
        {
            if (objectiveToStart.Equals(FlatBufferHelper.DefaultQuestObjectiveConfig))
                return;

            var objectiveProgress = _playerObjectivesManager.StartObjective(objectiveToStart, objectiveToStart.QuestId, 0.0f);

            if (executeActions && HasObjectiveActionsToExecute(objectiveToStart))
            {
                StartFirstObjectiveAction(objectiveToStart, objectiveProgress);
            }
        }

        private bool HasObjectiveActionsToExecute(QuestObjectiveConfig objToStart)
        {
            return objToStart.ActionsLength > 0;
        }

        private void StartFirstObjectiveAction(QuestObjectiveConfig objToStart, PBObjectiveProgress objectiveProgress)
        {
            if (objToStart.ActionsLength <= 0) return;
            
            var executeObjectiveActions = _eventDispatcher.GetMessage<ExecuteObjectiveActions>();
            executeObjectiveActions.Set(new ObjectiveActionsData(objToStart, objectiveProgress));
            _eventDispatcher.TriggerEvent(executeObjectiveActions);
        }

        private bool HasObjectiveStarted(string questUid, string uid)
        {
            return _playerObjectivesManager.HasObjectiveStarted(questUid, uid);
        }

        private ObjectiveState GetObjectiveStatus(string questUid, string uid)
        {
            return _playerObjectivesManager.GetObjectiveStatus(questUid, uid);
        }

        // Internal protocol
        public void ClaimObjective(QuestObjectiveConfig objectiveConfig)
        {
            var reward = FlatBufferHelper.ToDict(objectiveConfig.Reward, objectiveConfig.RewardLength);

            if (reward.ContainsKey(InventoryItems.SpecialReward))
                reward.Remove(InventoryItems.SpecialReward);

            if (reward.Count > 0)
            {
                var rewardTransaction = new Transaction()
                    .Earn(reward)
                    .SetAnalyticsData(CurrencyFlow.Quest.Name, objectiveConfig.QuestId, objectiveConfig.Uid)
                    .AddTag(TransactionTag.GoalObjective);
                _walletManager.TransactionController.MakeTransaction(rewardTransaction);
                _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.GoalObjective);
            }

            _playerObjectivesManager.SetObjectiveStatus(objectiveConfig.QuestId, objectiveConfig.Uid, ObjectiveState.Claimed);

            var objectiveClaimedEvent = _eventDispatcher.GetMessage<ObjectiveClaimedEvent>();
            objectiveClaimedEvent.Set(objectiveConfig);
            _eventDispatcher.TriggerEvent(objectiveClaimedEvent);
        }

        public void ForceClaimObjective(QuestObjectiveConfig objectiveConfig)
        {
            if (!_playerObjectivesManager.HasObjectiveStarted(objectiveConfig.QuestId, objectiveConfig.Uid))
                _playerObjectivesManager.StartObjective(objectiveConfig, objectiveConfig.QuestId, 0.0f);

            _playerObjectivesManager.SetObjectiveStatus(objectiveConfig.QuestId, objectiveConfig.Uid, ObjectiveState.Claimed);
        }

        private void UpdateQuestProgressForExpiration(Quest quest)
        {
            if (quest.IsFinished())
            {
                AddQuestToExpired(quest);
                ForceCheckQuests();
            }
            else
            {
                TryStartNextObjective(quest.QuestConfig);
                _playerObjectivesManager.ForceCheckObjectives();
            }
        }

        private void AddQuestToCompleted(Quest quest)
        {
            _inProgressQuests.Remove(quest.QuestUid);
            _completedQuests.Add(quest.QuestUid);

            foreach (var questObjectiveConfig in quest.GetObjectives())
            {
                _playerObjectivesManager.RemoveObjective(questObjectiveConfig.QuestId, questObjectiveConfig.Uid);
            }
        }

        private void AddQuestToExpired(Quest quest)
        {
            _inProgressQuests.Remove(quest.QuestUid);
            _expiredQuests.Add(quest.QuestUid);

            foreach (var questObjectiveConfig in quest.GetObjectives())
            {
                _playerObjectivesManager.RemoveObjective(questObjectiveConfig.QuestId, questObjectiveConfig.Uid);
            }
        }

        // Debug only
        public void ForceUpdateQuestProgressWithoutEvents(Quest quest)
        {
            if (quest.IsFinished())
            {
                AddQuestToCompleted(quest);
            }
            else
            {
                TryStartNextObjective(quest.QuestConfig, false);
            }
        }

        private QuestConfig GetQuestConfig(string questUid)
        {
            return _questsConfigDictionary.GetSafe(questUid);
        }

        public QuestObjectiveConfig GetObjectiveConfig(string questUid, string objectiveUid)
        {
            if (_questsConfigDictionary.TryGetValue(questUid, out var questConfig))
                return FlatBufferHelper.Find(questConfig.ObjectivesFb, questConfig.ObjectivesFbLength,x => x?.Uid == objectiveUid).Value;

            Debug.LogErrorFormat("Missing config for {0}:{1}", questUid, objectiveUid);
            return default;
        }

        private void ValidateInProgressQuests()
        {
            var objectives = _playerObjectivesManager.GetAllObjectivesInProgress();
            IList<PBObjectiveProgress> objectivesToRemove = new List<PBObjectiveProgress>();
            if (objectives != null)
            {
                foreach (var objProgress in objectives)
                {
                    var questConfig = GetQuestConfig(objProgress.ParentUid);
                    if (questConfig.Equals(FlatBufferHelper.DefaultQuestConfig))
                    {
                        objectivesToRemove.Add(objProgress);
                    }
                    else if (objProgress.State == ObjectiveState.InProgress.ToInt())
                    {
                        var objectiveConfig = GetObjectiveConfig(questConfig.Uid, objProgress.Uid);

                        if (!objectiveConfig.Equals(FlatBufferHelper.DefaultQuestObjectiveConfig) && HasObjectiveActionsToExecute(objectiveConfig))
                        {
                            var executeObjectiveActions = _eventDispatcher.GetMessage<ExecuteObjectiveActions>();
                            executeObjectiveActions.Set(new ObjectiveActionsData(objectiveConfig, objProgress));
                            _eventDispatcher.TriggerEvent(executeObjectiveActions);
                        }
                    }
                }
            }

            foreach (var objProgress in objectivesToRemove)
            {
                BDebug.Log($"Removed objectives {objProgress.Uid}");
                _playerObjectivesManager.RemoveObjective(objProgress.ParentUid, objProgress.Uid);
            }
        }

        public void Restart()
        {
            Unsubscribe();
            Config.OnConfigUpdated -= InitConfigs;
            _playerObjectivesManager.OnObjectiveCompleted -= ObjectiveCompletedEventHandler;
            _playerObjectivesManager.OnObjectiveExpired -= ObjectiveExpiredEventHandler;
        }
    }
}