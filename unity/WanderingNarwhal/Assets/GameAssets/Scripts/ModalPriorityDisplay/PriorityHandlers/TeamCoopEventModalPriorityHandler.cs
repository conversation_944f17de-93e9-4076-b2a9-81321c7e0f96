using System;
using System.Collections;
using BBB;
using BBB.DI;
using BBB.TeamEvents;
using GameAssets.Scripts.GameEvents;

namespace GameAssets.Scripts.ModalPriorityDisplay.PriorityHandlers
{
    public class TeamCoopEventIntroModalPriorityHandler : TeamCoopEventModalPriorityHandler
    {
        private readonly ITeamEventManager _eventManager;
        
        public TeamCoopEventIntroModalPriorityHandler(IContext context) : base(context)
        {
            _eventManager = context.Resolve<ITeamEventManager>();
        }

        public override bool ShouldShow(ScreenType currentScreen)
        {
            return _eventManager.ShouldAutoShow(EventAutoshowCondition.NonJoined);
        }
    }
    
    public class TeamCoopEventModalPriorityHandler : ModalPriorityHandlerBase
    {
        private readonly ITeamEventManager _eventManager;
        
        public TeamCoopEventModalPriorityHandler(IContext context) : base(context)
        {
            _eventManager = context.Resolve<ITeamEventManager>();
        }

        public override bool ShouldShow(ScreenType currentScreen)
        {
            return ShouldAutoShow();
        }
        
        private bool ShouldAutoShow()
        {
            return _eventManager.ShouldAutoShow(EventAutoshowCondition.NonJoined) ||
                   _eventManager.ShouldAutoShow(EventAutoshowCondition.Normal);
        }

        public override IEnumerator Show(Action<bool> successCallback)
        {
            var success = _eventManager.TryAutoShowEvent(EventAutoshowCondition.NonJoined);
            if (!success)
            {
                success = _eventManager.TryAutoShowEvent(EventAutoshowCondition.Normal);
                successCallback?.Invoke(success);
            }

            successCallback?.Invoke(success);

            yield break;
        }
    }
}