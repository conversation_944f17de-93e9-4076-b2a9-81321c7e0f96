using System;
using System.Collections;
using BBB;
using BBB.DI;
using BBB.RaceEvents;
using GameAssets.Scripts.GameEvents;

namespace GameAssets.Scripts.ModalPriorityDisplay.PriorityHandlers
{
    public class RoyaleEventIntroModalPriorityHandler : RoyaleEventModalPriorityHandler
    {
        private readonly IRoyaleEventManager _eventManager;

        public RoyaleEventIntroModalPriorityHandler(IContext context) : base(context)
        {
            _eventManager = context.Resolve<IRoyaleEventManager>();
        }

        public override bool ShouldShow(ScreenType currentScreen)
        {
            return _eventManager.ShouldAutoShow(EventAutoshowCondition.NonJoined);
        }
    }
    
    public class RoyaleEventModalPriorityHandler : ModalPriorityHandlerBase
    {
        private readonly IRoyaleEventManager _eventManager;

        public RoyaleEventModalPriorityHandler(IContext context) : base(context)
        {
            _eventManager = context.Resolve<IRoyaleEventManager>();
        }

        public override bool ShouldShow(ScreenType currentScreen)
        {
            return ShouldAutoShow();
        }

        public override IEnumerator Show(Action<bool> successCallback)
        {
            var success = _eventManager.TryAutoShowEvent(EventAutoshowCondition.NonJoined);
            if (!success)
            {
                success = _eventManager.TryAutoShowEvent(EventAutoshowCondition.Normal);
                successCallback?.Invoke(success);
            }

            successCallback?.Invoke(success);

            yield break;
        }

        private bool ShouldAutoShow()
        {
            return _eventManager.ShouldAutoShow(EventAutoshowCondition.NonJoined) ||
                   _eventManager.ShouldAutoShow(EventAutoshowCondition.Normal);
        }
    }
    
    public class RoyaleEventRoundWonModalPriorityHandler : ModalPriorityHandlerBase
    {
        private readonly IRoyaleEventManager _eventManager;

        public RoyaleEventRoundWonModalPriorityHandler(IContext context) : base(context)
        {
            _eventManager = context.Resolve<IRoyaleEventManager>();
        }

        public override bool ShouldShow(ScreenType currentScreen)
        {
            return ShouldAutoShow();
        }

        public override IEnumerator Show(Action<bool> successCallback)
        {
            var success = _eventManager.TryAutoShowEvent(EventAutoshowCondition.RoundWon);
            successCallback?.Invoke(success);

            yield break;
        }

        private bool ShouldAutoShow()
        {
            return _eventManager.ShouldAutoShow(EventAutoshowCondition.RoundWon);
        }
    }
}