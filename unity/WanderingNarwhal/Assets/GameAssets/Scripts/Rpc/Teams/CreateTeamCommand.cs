using RPC.Teams;

namespace Bebopbee.Core.Systems.RpcCommandManager.Teams
{
    public class CreateTeamCommand : RpcCommand<CreateTeam>
    {
        protected override void Parse(object[] args)
        {
            base.Parse(args);
            Dto.TeamUid = (string) args[0];
            Dto.Name = (string) args[1];
            Dto.Desc = (string) args[2];
            Dto.TeamType = (TeamType) args[3];
            Dto.MinTrophyCount = (int) args[4];
        }
        
        protected override int GetExpectedArgumentsCount()
        {
            return 5;
        }
    }
}