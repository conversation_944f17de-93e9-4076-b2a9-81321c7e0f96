using RPC.Social;

namespace Bebopbee.Core.Systems.RpcCommandManager.Social
{
    public class ParticipateInLotteryCommand : RpcCommand<ParticipateInLottery>
    {
        protected override void Parse(object[] args)
        {
            base.Parse(args);
            Dto.EventId = (string) args[0];
        }

        protected override int GetExpectedArgumentsCount()
        {
            return 1;
        }
    }
}