using System;
using BBB.Core;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.UI.Earth;
using BebopBee.Core;
using Core.RPC;
using DG.Tweening;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Core.AssetBundles;

namespace BBB.UI
{
    public class CityLoadingModalController : BaseModalsController<ICityLoadingModalViewPresenter>
    {
        private IPlayerManager _playerManager;
        private GenericModalFactory _genericModalFactory;
        private IEventDispatcher _eventDispatcher;

        private Action _closeInFinishedStateCallback;
        private bool _isInFinishedState;
        private string _locationUid;
        private Tweener _tweener;

        private const float NetworkFailedDelay = 15f;

        public override void Init(IContext previousContext)
        {
            base.Init(previousContext);

            _playerManager = previousContext.Resolve<IPlayerManager>();
            _eventDispatcher = previousContext.Resolve<IEventDispatcher>();
            _genericModalFactory = previousContext.Resolve<GenericModalFactory>();
        }

        public void Setup(string locationUid, Action closeInFinishedStateCallback)
        {
            _closeInFinishedStateCallback = closeInFinishedStateCallback;
            _locationUid = locationUid;
            if (IsReady())
            {
                View.Setup(locationUid);
            }
            else
            {
                DoWhenReady(() =>
                {
                    View.Setup(locationUid);
                });
            }
        }

        protected override void OnShow()
        {
            base.OnShow();
           Subscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();
           
            View.GoButtonClicked += GoButtonHandler;
            View.FinishedEvent += FinishedEventHandler;
            ConnectivityStatusManager.ConnectivityChanged += OnConnectionStatusChanged;
            _eventDispatcher.AddListener<LocationLoadFailed>(OnLocationLoadFailed);
        }

        private void Unsubscribe()
        {
            View.GoButtonClicked -= GoButtonHandler;
            View.FinishedEvent -= FinishedEventHandler; 
            ConnectivityStatusManager.ConnectivityChanged -= OnConnectionStatusChanged;
            _eventDispatcher.RemoveListener<LocationLoadFailed>(OnLocationLoadFailed);
        }

        protected override void OnHide()
        {
            _tweener?.Kill();
            _tweener = null;
            _playerManager.Player.MarkAsVisited(ModalsType.CityLoading);
            Unsubscribe();

            base.OnHide();
        }

        protected override void OnPostHide()
        {
            if (_isInFinishedState)
            {
                _isInFinishedState = false;
                _closeInFinishedStateCallback.SafeInvoke();
                _closeInFinishedStateCallback = null;
            }

            base.OnPostHide();
        }

        protected override void OnCloseButtonClicked()
        {
            //this is needed to not open location if close button clicked
            _isInFinishedState = false;
            _closeInFinishedStateCallback = null;
            base.OnCloseButtonClicked();
        }

        private void FinishedEventHandler()
        {
            _isInFinishedState = true;
        }

        private void GoButtonHandler()
        {
            Hide();
        }
        
        private void OnLocationLoadFailed(LocationLoadFailed ev)
        {
            if (ev.Arg0 == _locationUid)
                OnLoadFailed();
        }

        private void OnConnectionStatusChanged(bool reachable)
        {
            if (!reachable)
            {
                _tweener ??= Rx.Invoke(NetworkFailedDelay, _ =>
                {
                    _tweener = null;
                    OnLoadFailed();
                });
            }
            else
            {
                _tweener?.Kill();
                _tweener = null;
            }
        }

        private void OnLoadFailed()
        {
            _isInFinishedState = false;
            _closeInFinishedStateCallback = null;
            
            Hide();
            _genericModalFactory.ShowNoConnectionModal();
            var failedToLoadLocationBecauseOfNoConnection = _eventDispatcher.GetMessage<FailedToLoadLocationBecauseOfNoConnection>();
            failedToLoadLocationBecauseOfNoConnection.Set(_locationUid);
            _eventDispatcher.TriggerEvent(failedToLoadLocationBecauseOfNoConnection);
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            Unsubscribe();
        }
    }
}