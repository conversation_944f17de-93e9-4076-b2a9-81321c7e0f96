using BBB;
using BBB.DI;
using BebopBee;

namespace Loading.Commands
{
    public class ExecuteOnConfigLoadedCommand: CommandBase
    {
        public override void Execute(IContext context)
        {
            var incrementalContext = context as IIncrementalContext;

            var eventDispatcher = incrementalContext.Resolve<IEventDispatcher>();
            eventDispatcher.TriggerEventNextFrame(eventDispatcher.GetMessage<ConfigLoadedEvent>());
            CurrentStatus = CommandStatus.Success;
        }
    }
}