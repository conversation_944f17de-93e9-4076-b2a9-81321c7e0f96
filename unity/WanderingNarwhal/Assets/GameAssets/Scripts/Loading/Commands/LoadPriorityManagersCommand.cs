using BBB;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.MMVibrations;
using BBB.Modals;
using BBB.Screens;
using BBB.UI.Core;
using BBB.UI.Transitions;
using Bebopbee.Core.Extensions.Unity;
using Bebopbee.Core.Systems.Ticksystem;
using BebopBee;
using UnityEngine.Profiling;

namespace Loading.Commands
{
    public class LoadPriorityManagersCommand : CommandBase
    {
        private readonly TransitionManager _transitionManager;
        private readonly ScreensManager _screensManager;
        private readonly ModalsManager _modalsManager;

        public LoadPriorityManagersCommand(TransitionManager transitionManager,
            ScreensManager screensManager, ModalsManager modalsManager)
        {
            _transitionManager = transitionManager;
            _screensManager = screensManager;
            _modalsManager = modalsManager;
        }

        protected override void CommandExecutionStart(IContext context)
        {
            base.CommandExecutionStart(context);

            var coroutineExecutor = context.Resolve<ICoroutineExecutor>();
            var unityContainer = context.Resolve<IUnityContainer>();
            var assetsManager = context.Resolve<IAssetsManager>();

            var incrementalContext = (IIncrementalContext)context;

            incrementalContext.AddServiceToRegister<IScreensManager>(_screensManager);
            incrementalContext.AddServiceToRegister<IModalsManager>(_modalsManager);
            incrementalContext.AddServiceToRegister<TransitionManager>(_transitionManager);

            Profiler.BeginSample($"new ResourceCache");
            incrementalContext.AddServiceToRegister<IResourceCacheHandle>(new ResourceCache(assetsManager));
            Profiler.EndSample();

            Profiler.BeginSample($"GetComponent<ScreenCommandManager>");
            var screenCommandManager = unityContainer.gameObject.GetOrAddComponent<ScreenCommandManager>();
            incrementalContext.AddServiceToRegister<ScreenCommandManager>(screenCommandManager);
            Profiler.EndSample();

            var screensBuilder = new ScreensBuilder();
            incrementalContext.AddServiceToRegister<IScreensBuilder>(screensBuilder);
            incrementalContext.AddServiceToRegister<IScreenRegisterer>(screensBuilder);

            Profiler.BeginSample($"new ViewsResources");
            incrementalContext.AddServiceToRegister<IViewsResources>(new ViewsResources());
            Profiler.EndSample();
            Profiler.BeginSample($"new ModalsBuilder");
            incrementalContext.AddServiceToRegister<IModalsBuilder>(new ModalsBuilder());
            Profiler.EndSample();

            Profiler.BeginSample($"Manager UpdateTickSystem");
            var tickSystem = new UpdateTickSystem();
            incrementalContext.AddServiceToRegister<ITickSystem>(tickSystem);
            Profiler.EndSample();

            Profiler.BeginSample($"new GenericResourceProvider");
            incrementalContext.AddServiceToRegister<GenericResourceProvider>(new GenericResourceProvider());
            Profiler.EndSample();

            var genericModalsBlocker = new GenericModalsBlocker(coroutineExecutor);
            incrementalContext.AddServiceToRegister<IGenericModalsBlocker>(genericModalsBlocker);

            var config = context.Resolve<IConfig>();
            var localizationManager = new LocalizationManager(coroutineExecutor);
            localizationManager.SetConfig(config);
            incrementalContext.AddServiceToRegister<ILocalizationManager>(localizationManager);

            incrementalContext.AddServiceToRegister<GenericModalFactory>(new GenericModalFactory());

            var vibrations = new MMVibrationsWrapper();
            incrementalContext.AddServiceToRegister<IVibrationsWrapper>(vibrations);
            
            Profiler.BeginSample($"RegisterContext");
            incrementalContext.RegisterContext();
            Profiler.EndSample();

            Profiler.BeginSample($"Manager Init");
            screenCommandManager.Init(context);
            tickSystem.Add(assetsManager as ITickable);
            Profiler.EndSample();

            CurrentStatus = CommandStatus.Success;
        }
    }
}