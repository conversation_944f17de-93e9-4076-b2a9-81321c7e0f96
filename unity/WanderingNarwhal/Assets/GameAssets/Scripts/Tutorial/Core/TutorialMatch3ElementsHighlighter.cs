using System.Collections.Generic;
using UnityEngine;

namespace GameAssets.Scripts.Tutorial.Core
{
    [RequireComponent(typeof(Canvas))]
    public class TutorialMatch3ElementsHighlighter : TutorialElementsHighlighter
    {
        [SerializeField] private Match3Tutorial.Match3Element _match3ElementType;
        public override string ElementType => _match3ElementType.ToString();

        public void SetMatch3ElementType(Match3Tutorial.Match3Element match3ElementType)
        {
            _match3ElementType = match3ElementType;
        }

        public static void EnableHighlightOfMatchElementsOfType(Match3Tutorial.Match3Element elementType, bool enable)
        {
            EnableHighlightElementsOfType(elementType.ToString(), enable);
        }

        public static List<TutorialElementsHighlighter> GetMatch3ElementsOfType(Match3Tutorial.Match3Element elementType)
        {
            return GetElementsOfType(elementType.ToString());
        }
    }
}