using System;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.Navigation;
using BBB.UI.Core;
using FBConfig;
using GameAssets.Scripts.Player;
using UnityEngine;
using UnityEngine.Serialization;

namespace GameAssets.Scripts.Tutorial.Core
{
    public abstract class BaseTutorialStepSetup : BbbMonoBehaviour
    {
        protected Func<bool> EnterCondition;
        protected Func<bool> ExitCondition;

        [FormerlySerializedAs("Name")]
        [SerializeField] private string _name;
        [FormerlySerializedAs("Enabled")]
        [SerializeField] private bool _enabled = true;

        [SerializeField] private string _levelUid;
        [SerializeField] private Transform _specialRoot;
        [SerializeField] private BaseStep _alreadyInstantiatedStep;
        [SerializeField] private GameObject _stepPrefab;
        [SerializeField] private bool _shouldRestartIfWasCompleted = false;

        protected ILocationManager LocationManager;
        protected IScreensManager ScreensManager;
        protected IPlayerManager PlayerManager;
        private ILevelsOrderingManager _levelsOrderingManager;

        protected ScreenType LastScreenType;
        protected ScreenType TargetScreenType;
        private BaseStep _stepInstance;
        private Func<BaseStep> _getStepInstance;

        public virtual string DebugNarrativeUid => LevelUid;
        public string LevelUid => _levelUid;
        public string PreviousLevelUid { get; private set; }
        public string Name => _name;
        public bool Enabled => _enabled;
        public float Order  { get; private set; }
        protected string Location { get; private set; }
        protected string NextLevelUid { get; private set; }
        public bool IsCompleted { get; set; }

        public virtual void Init(IContext context)
        {
            PlayerManager = context.Resolve<IPlayerManager>();
            ScreensManager = context.Resolve<IScreensManager>();
            LocationManager = context.Resolve<ILocationManager>();

            Subscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();

            ScreensManager.OnScreenChangingStarted += ScreenChangingStartedHandler;
            ScreensManager.OnScreenTransitionComplete += ScreenTransitionCompleteHandler;
            ScreensManager.OnScreenChanged += ScreenChangingHandler;
        }

        private void Unsubscribe()
        {
            if (ScreensManager == null) return;

            ScreensManager.OnScreenChangingStarted -= ScreenChangingStartedHandler;
            ScreensManager.OnScreenTransitionComplete -= ScreenTransitionCompleteHandler;
            ScreensManager.OnScreenChanged -= ScreenChangingHandler;
        }

        public void Init(IContext context, TutorialConfig tutorialConfig, Func<BaseStep> getStepInstance)
        {
            _name = tutorialConfig.Name;
            _enabled = tutorialConfig.Enabled;
            _levelUid = tutorialConfig.LevelUid;
            _shouldRestartIfWasCompleted = tutorialConfig.ShouldRestart;
            Order = tutorialConfig.Order;
            Location = MainProgressionLocation.UID;
            
            _levelsOrderingManager = context.Resolve<ILevelsOrderingManager>();
            PreviousLevelUid = _levelsOrderingManager.GetPreviousLevelUid(LevelUid);
            NextLevelUid = _levelsOrderingManager.GetNextLevelUid(LevelUid);

            _getStepInstance = getStepInstance;
            
            Init(context);
        }

        private void ScreenChangingStartedHandler(ScreenType arg1, IScreensController arg2)
        {
            ScreensManager.GetTransitionTargetScreenType();
        }

        private void ScreenChangingHandler(ScreenType screenType, IScreensController arg2, IViewPresenter arg3)
        {
            LastScreenType = screenType;
        }

        private void ScreenTransitionCompleteHandler(ScreenType screenType)
        {
            TargetScreenType = screenType;
        }

        public bool IsEnterCondition()
        {
            return EnterCondition();
        }

        public bool IsExitCondition()
        {
            return (!_shouldRestartIfWasCompleted && PlayerManager.Player.HasCompletedTutorialStep(_name)) || ExitCondition();
        }

        public BaseStep GetStep()
        {
            if (_alreadyInstantiatedStep != null)
                return _alreadyInstantiatedStep;

            _stepInstance = _getStepInstance != null ? _getStepInstance.Invoke() : Instantiate(_stepPrefab, _specialRoot != null ? _specialRoot : transform).GetComponent<BaseStep>();

            return _stepInstance;
        }

        public void ReleaseStepGO()
        {
            if (_alreadyInstantiatedStep != null)
            {
                Destroy(_alreadyInstantiatedStep.gameObject);
            }
            else
            {
                if (_stepInstance != null)
                    Destroy(_stepInstance.gameObject);
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            Unsubscribe();
            EnterCondition = null;
            ExitCondition = null;
            _getStepInstance = null;
            ReleaseStepGO();
        }

        public virtual void OnExit()
        {
        }
    }
}