using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.Quests;
using GameAssets.Scripts.CurrenciesRewardModalUI;

namespace GameAssets.Scripts.Deeplink.Gifts
{
    public class ClaimGiftModalController : BaseModalsController<IClaimGiftModalViewPresenter>
    {
        private IModalsBuilder _modalsBuilder;
        private Dictionary<string, int> _gifts;
        private Action<HashSet<string>> _onRewardClose;
        private string _title;
        private string _subtitle;
        private string _giftTitle;
        private string _message;

        public void Setup(Dictionary<string, int> gifts, string giftTitle, string message, Action<HashSet<string>> onRewardClose, string title = null,
            string subtitle = null)
        {
            _gifts = gifts;
            _onRewardClose = onRewardClose;
            _title = title;
            _subtitle = subtitle;
            _giftTitle = giftTitle;
            _message = message;
        }

        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);
            _modalsBuilder = context.Resolve<IModalsBuilder>();
        }

        protected override void OnShow()
        {
            View.Setup(_giftTitle, _message);
            base.OnShow();
        }

        protected override void OnHide()
        {
            ClaimGift();
            base.OnHide();
        }

        private void ClaimGift()
        {
            var rewardModal = _modalsBuilder.CreateModalView<CurrenciesRewardModalController>(ModalsType.CurrenciesRewardModal);
            var rewardViewOverrideParams = new RewardViewOverride();

            rewardModal.SetupInitialParams(
                new CurrenciesRewardViewModel
                {
                    RewardDict = _gifts,
                    TitleText = _title,
                    SubtitleText = _subtitle,
                    RewardViewOverride = rewardViewOverrideParams,
                    SubtitleTextActiveState = !string.IsNullOrEmpty(_subtitle)
                }, _onRewardClose);
            rewardModal.ShowModal();
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            _onRewardClose = null;
        }
    }
}