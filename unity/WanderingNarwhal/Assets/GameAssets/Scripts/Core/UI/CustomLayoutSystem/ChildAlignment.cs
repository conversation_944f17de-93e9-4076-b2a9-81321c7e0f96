using System;
using UnityEngine;

namespace CustomLayout
{
    public enum ChildAlignment : byte
    {
        UpperLeft = 0,
        UpperCenter = 1,
        UpperRight = 2,
        MiddleLeft = 3,
        MiddleCenter = 4,
        MiddleRight = 5,
        LowerLeft = 6,
        LowerCenter = 7,
        LowerRight = 8
    }

    public enum ChildHorAlignment : byte
    {
        Left = 0,
        Center = 1,
        Right = 2
    }

    public enum ChildVerAlignment : byte
    {
        Upper = 0,
        Middle = 1,
        Lower = 2
    }

    public struct ChildAlignments
    {
        public ChildHorAlignment HorAlignment;
        public ChildVerAlignment VerAlignment;
    }

    public static class ChildAlignmentExtensions
    {
        public static Vector2 GetVerticalOrigin(this ChildAlignment childAlignment, Vector2 rootSizeDelta)
        {
            Vector2 normalizedVec;
            var alignments = childAlignment.GetAlignments();

            switch (alignments.HorAlignment)
            {
                case ChildHorAlignment.Left:
                    normalizedVec.x = 0f;
                    break;
                case ChildHorAlignment.Center:
                    normalizedVec.x = 0.5f;
                    break;
                case ChildHorAlignment.Right:
                    normalizedVec.x = 1f;
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            switch (alignments.VerAlignment)
            {
                case ChildVerAlignment.Upper:
                    normalizedVec.y = 0f;
                    break;
                case ChildVerAlignment.Middle:
                    normalizedVec.y = -0.5f;
                    break;
                case ChildVerAlignment.Lower:
                    normalizedVec.y = -1f;
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }


            return new Vector2(normalizedVec.x * rootSizeDelta.x, normalizedVec.y * rootSizeDelta.y);
        }

        public static Vector2 GetHorizontalOrigin(this ChildAlignment childAlignment, Vector2 rootSizeDelta)
        {
            Vector2 normalizedVec;
            var alignments = childAlignment.GetAlignments();

            switch (alignments.HorAlignment)
            {
                case ChildHorAlignment.Left:
                    normalizedVec.x = 0f;
                    break;
                case ChildHorAlignment.Center:
                    normalizedVec.x = 0.5f;
                    break;
                case ChildHorAlignment.Right:
                    normalizedVec.x = 1f;
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            switch (alignments.VerAlignment)
            {
                case ChildVerAlignment.Upper:
                    normalizedVec.y = 0f;
                    break;
                case ChildVerAlignment.Middle:
                    normalizedVec.y = 0.5f;
                    break;
                case ChildVerAlignment.Lower:
                    normalizedVec.y = 1f;
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }


            return new Vector2(normalizedVec.x * rootSizeDelta.x, normalizedVec.y * rootSizeDelta.y);
        }

        public static Vector2 GetVerticalLayoutPivot(this ChildAlignment childAlignment)
        {
            Vector2 result;
            var alignments = childAlignment.GetAlignments();

            switch (alignments.HorAlignment)
            {
                case ChildHorAlignment.Left:
                    result.x = 0f;
                    break;
                case ChildHorAlignment.Center:
                    result.x = 0.5f;
                    break;
                case ChildHorAlignment.Right:
                    result.x = 1f;
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            switch (alignments.VerAlignment)
            {
                case ChildVerAlignment.Upper:
                    result.y = 1f;
                    break;
                case ChildVerAlignment.Middle:
                    result.y = 1f;
                    break;
                case ChildVerAlignment.Lower:
                    result.y = 0f;
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }


            return result;
        }

        public static Vector2 GetHorizontalLayoutPivot(this ChildAlignment childAlignment)
        {
            Vector2 result;
            var alignments = childAlignment.GetAlignments();

            switch (alignments.HorAlignment)
            {
                case ChildHorAlignment.Left:
                    result.x = 0f;
                    break;
                case ChildHorAlignment.Center:
                    result.x = 0f;
                    break;
                case ChildHorAlignment.Right:
                    result.x = 1f;
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            switch (alignments.VerAlignment)
            {
                case ChildVerAlignment.Upper:
                    result.y = 1f;
                    break;
                case ChildVerAlignment.Middle:
                    result.y = 0.5f;
                    break;
                case ChildVerAlignment.Lower:
                    result.y = 0f;
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }


            return result;
        }

        public static ChildAlignments GetAlignments(this ChildAlignment childAlignment)
        {
            switch (childAlignment)
            {
                case ChildAlignment.UpperLeft:
                    return new ChildAlignments
                    {
                        HorAlignment = ChildHorAlignment.Left,
                        VerAlignment = ChildVerAlignment.Upper
                    };
                case ChildAlignment.UpperCenter:
                    return new ChildAlignments
                    {
                        HorAlignment = ChildHorAlignment.Center,
                        VerAlignment = ChildVerAlignment.Upper
                    };
                case ChildAlignment.UpperRight:
                    return new ChildAlignments
                    {
                        HorAlignment = ChildHorAlignment.Right,
                        VerAlignment = ChildVerAlignment.Upper
                    };
                case ChildAlignment.MiddleLeft:
                    return new ChildAlignments
                    {
                        HorAlignment = ChildHorAlignment.Left,
                        VerAlignment = ChildVerAlignment.Middle
                    };
                case ChildAlignment.MiddleCenter:
                    return new ChildAlignments
                    {
                        HorAlignment = ChildHorAlignment.Center,
                        VerAlignment = ChildVerAlignment.Middle
                    };
                case ChildAlignment.MiddleRight:
                    return new ChildAlignments
                    {
                        HorAlignment = ChildHorAlignment.Right,
                        VerAlignment = ChildVerAlignment.Middle
                    };
                case ChildAlignment.LowerLeft:
                    return new ChildAlignments
                    {
                        HorAlignment = ChildHorAlignment.Left,
                        VerAlignment = ChildVerAlignment.Lower
                    };
                case ChildAlignment.LowerCenter:
                    return new ChildAlignments
                    {
                        HorAlignment = ChildHorAlignment.Center,
                        VerAlignment = ChildVerAlignment.Lower
                    };
                case ChildAlignment.LowerRight:
                    return new ChildAlignments
                    {
                        HorAlignment = ChildHorAlignment.Right,
                        VerAlignment = ChildVerAlignment.Lower
                    };
                default:
                    throw new ArgumentOutOfRangeException("childAlignment", childAlignment, null);
            }
        }
    }
    
}