using System.Collections;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using Cysharp.Threading.Tasks;
using RPC.Core;

namespace Core.Configs
{
    public class ProtobufConfigResolver<T> : ConfigResolverBase<T>
    {
        public static readonly ProtobufConfigResolver<T> Instance = new();
        private IDictionary<string, T> _config;
        private ConfigProtobufLoader _protobufLoader;

        public ProtobufConfigResolver()
        {
            Name = ConfigUtils.GetKey<T>();
        }

        public ProtobufConfigResolver(string name)
        {
            Name = name;
        }

        public override void Init(ConfigManifest manifest)
        {
            _protobufLoader = new ConfigProtobufLoader(manifest, new FileStorage(ConfigUtils.PersistentDataPath));
        }
        
        public override void ChangeFileStorage(IFileStorage fileStorage)
        {
            _protobufLoader.ChangeFileStorage(fileStorage);
        }
        public override async UniTask InitAsync(ConfigManifest manifest, bool fromServer, Dictionary<string, ConfigVersionInfo> configs = null)
        {
            await base.InitAsync(manifest, fromServer);
            await ParseAsync(configs, fromServer);
        }

        public override void Parse(IDictionary<string, object> result, bool fromServer)
        {
            _config = ProcessConfigLegacy<T>(result, GetName());
        }

        public override void Parse(Dictionary<string, ConfigVersionInfo> configs, bool fromServer)
        {
            _config = ProcessConfig<T>(configs, GetName(), fromServer);
        }

        public override async UniTask ParseAsync(Dictionary<string, ConfigVersionInfo> configs, bool fromServer)
        {
            _config = await ProcessConfigAsync<T>(configs, GetName(), fromServer);
        }

        public override IDictionary<string, T1> GetConfig<T1>()
        {
            if (_config == null)
            {
                Parse(new Dictionary<string, ConfigVersionInfo>(), false);
            }

            return _config as IDictionary<string, T1>;
        }

        public override IDictionary GetConfig()
        {
            if (_config == null)
            {
                Parse(new Dictionary<string, ConfigVersionInfo>(), false);
            }

            return (IDictionary)_config;
        }

        public override TDict1 GetCollection<TDict1>() where TDict1 : struct
        {
            return default;
        }

        private IDictionary<string, TProto> ProcessConfigLegacy<TProto>(IDictionary<string, object> result, string configName)
        {
            var configParse = _protobufLoader.ParseConfig<TProto>(result, configName, false);

            if (configParse == null)
                BDebug.LogFormat(LogCat.Config, "LegacyConfig {0} is null", configName);

            return configParse;
        }

        private IDictionary<string, TProto> ProcessConfig<TProto>(Dictionary<string, ConfigVersionInfo> configs, string configName, bool fromServer)
        {
            var configParse = _protobufLoader.ParseConfig<TProto>(configs, configName, fromServer);

            if (configParse == null)
                BDebug.LogFormat(LogCat.Config, "{0} is null", configName);

            return configParse;
        }

        private async UniTask<IDictionary<string, TProto>> ProcessConfigAsync<TProto>(Dictionary<string, ConfigVersionInfo> configs, string configName, bool fromServer)
        {
            var configParse = await _protobufLoader.ParseConfigAsync<TProto>(configs, configName, fromServer);

            if (configParse == null)
                BDebug.LogFormat(LogCat.Config, "{0} is null", configName);

            return configParse;
        }
    }
}