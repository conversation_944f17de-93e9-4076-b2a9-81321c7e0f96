using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace BBB.ContentManagement
{

    public interface IContentResult
    {
        string Tag { get; }
    }

    public interface ISpriteResult : IContentResult
    {
        Sprite Sprite { get; }
    }

    public interface IContentResultReciever
    {
        void ReceiveResult(IContentResult result);

        void Unsubscribe();
    }
    
    public abstract class ContentTask
    {
        protected abstract object Identifier { get; }

        protected bool IsAborted;

        private readonly List<IContentResultReciever> _cachingResultReceivers = new ();
        protected readonly List<IContentResultReciever> ResultReceivers = new ();

        private void Abort()
        {
            IsAborted = true;
        }

        public void CancelAbort()
        {
            IsAborted = false;
        }

        public virtual IEnumerator Tick()
        {
            yield return null;
        }

        /// <summary>
        /// Results receiver, which has lower priority, than regular result receiver.
        /// </summary>
        /// <remarks>
        /// Tasks often get caching receivers, which only used to store result somewhere.
        /// This method allows to mark this type of receivers, to distinct them from regular receivers.
        ///
        /// This used in such use-case:
        /// if task process started normally with some amount of receivers, but for some reason
        /// receivers got unsubscribed from this task before it finished.
        /// When amount of receivers reaching 0 we want to abort task,
        /// but also we don't want to count caching receivers, because they never unsubscribe
        /// and also they are not needed if everyone else already unsubscribed. -VK
        /// </remarks>
        public void AddCachingReceiver(IContentResultReciever resultReciever)
        {
            if(!_cachingResultReceivers.Contains(resultReciever))
                _cachingResultReceivers.Add(resultReciever);

            AddReceiver(resultReciever);
        }

        public void AddReceiver(IContentResultReciever resultReciever)
        {
            ResultReceivers.Add(resultReciever);
        }

        public void RemoveReceiver(IContentResultReciever resultReciever)
        {
            ResultReceivers.Remove(resultReciever);
            _cachingResultReceivers.Remove(resultReciever);

            foreach (var receiver in ResultReceivers)
            {
                if (!_cachingResultReceivers.Contains(receiver))
                {
                    // Found a non-caching receiver, so we don't need to abort.
                    return;
                }
            }

            // No non-caching receivers found, so abort the task.
            Abort();
        }

        public virtual void AddErrorHandler(Action<string> errorHandler)
        {
            
        }

        public virtual void RemoveErrorHandler()
        {
            
        }

        public override bool Equals(object obj)
        {
            return obj != null && Identifier.Equals(((ContentTask) obj).Identifier);
        }
        
        public override int GetHashCode()
        {
            return Identifier != null ? Identifier.GetHashCode() : 0;
        }
    }
}