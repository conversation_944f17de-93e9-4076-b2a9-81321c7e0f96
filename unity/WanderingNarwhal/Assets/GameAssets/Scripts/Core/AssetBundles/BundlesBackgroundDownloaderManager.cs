using System;
using BBB.DI;
using System.Collections.Generic;
using BBB.Core.AssetBundles.Loaders;
using BBB.UI.Core;

namespace BBB.Core.AssetBundles
{
    public enum BackgroundDownloadPriority
    {
        Highest,
        High,
        Normal,
        Low,
        Lowest
    }
    
    public class BackgroundDownloadData
    {
        public BackgroundDownloadPriority Priority;
        public string Name;
    }
    
    public class BundlesBackgroundDownloaderManager: IContextInitializable, IContextReleasable
    {
        private readonly BackgroundDownloadData[] _genericAssetsToPredownload =
        {
            new() {Priority = BackgroundDownloadPriority.Highest, Name = "Paris/SpawnerSettingsAsset"}
        };
        
        private readonly List<IBundlePredownloadProvider> _predownloadProviders = new ();
        
        private IBundleManager _bundleManager;
        private IBundleInfoProvider _bundleInfoProvider;
        private IScreensManager _screensManager;
        
        private static bool Enabled
        {
            get
            {
#if (UNITY_IOS || UNITY_ANDROID)
    #if UNITY_EDITOR
                if (!SimulatedLoader.SimulateAssetBundleInEditor)
    #endif
                {
                    return true;
                }
#endif
                return false;
            }
        }

        public void InitializeByContext(IContext context)
        {
            _bundleManager = context.Resolve<IBundleManager>();
            _bundleInfoProvider = context.Resolve<IBundleInfoProvider>();
            _screensManager = context.Resolve<IScreensManager>();
        }

        public void Setup()
        {
            if (!Enabled) return;
            
            Subscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();
            _screensManager.OnScreenChanged += ScreenChangedHandlerBundleBgLoader;
        }
        
        private void Unsubscribe()
        {
            _screensManager.OnScreenChanged -= ScreenChangedHandlerBundleBgLoader;
        }
        
        public void ReleaseByContext(IContext context)
        {
            Clear();
        }
        
        public void RegisterProvider(IBundlePredownloadProvider provider)
        {
            if (!Enabled) return;
            
            _predownloadProviders.Add(provider);
        }
        
        public void UnregisterProvider(IBundlePredownloadProvider provider)
        {
            if (!Enabled) return;
            
            _predownloadProviders.Remove(provider);
        }

        private void ScreenChangedHandlerBundleBgLoader(ScreenType arg1, IScreensController arg2, IViewPresenter arg3)
        {
            TryPredownloadBundles();
        }
        
        private void TryPredownloadBundles()
        {
            var bundlesToDownload = new HashSet<string>();
            var bundlesToDownloadWithPriority = new Dictionary<BackgroundDownloadPriority, HashSet<string>>();
            
            foreach (var asset in _genericAssetsToPredownload)
            {
                var bundleName = _bundleInfoProvider.GetBundleFromAsset(asset.Name)?.Bundlename;
                if(bundleName.IsNullOrEmpty()) continue;
                
                if (!bundlesToDownloadWithPriority.ContainsKey(asset.Priority))
                {
                    bundlesToDownloadWithPriority[asset.Priority] = new HashSet<string>();
                }
                bundlesToDownloadWithPriority[asset.Priority].Add(bundleName);
            }
            
            foreach (var predownloadProvider in _predownloadProviders)
            {
                var bundlesWithPriorities = predownloadProvider.GetBundlesToPredownload();
                if (bundlesWithPriorities == null) continue;
                
                foreach (var bundle in bundlesWithPriorities)
                {
                    if(bundle.Name.IsNullOrEmpty()) continue;
                    
                    if (!bundlesToDownloadWithPriority.ContainsKey(bundle.Priority))
                    {
                        bundlesToDownloadWithPriority[bundle.Priority] = new HashSet<string>();
                    }
                    bundlesToDownloadWithPriority[bundle.Priority].Add(bundle.Name);
                }
            }

            var priorities = Enum.GetValues(typeof(BackgroundDownloadPriority));
            foreach (var priority in priorities)
            {
                var curPriority = (BackgroundDownloadPriority) priority;
                if(!bundlesToDownloadWithPriority.TryGetValue(curPriority, out var bundles)) continue;
                bundlesToDownload.UnionWith(bundles);
            }

            if (bundlesToDownload.Count == 0) return;
            
            BDebug.Log($"BackgroundDownloadManager assets: [{string.Join(',', bundlesToDownload)}]");
            _bundleManager.DownloadBundleInBackground(new List<object>(bundlesToDownload));
        }

        public void Restart()
        {
            Clear();
        }
        
        private void Clear()
        {
            Unsubscribe();
            _predownloadProviders.Clear();
        }
    }
}