using System.Collections.Generic;
using UnityEngine;

namespace BebopBee.Core
{
    public static partial class PathFactory
    {
        private static readonly List<Vector3> PathFactoryTempList = new List<Vector3>(10);
        public static Vector3[] GetThreePointsRandomPath(Vector2 start, Vector2 end, float minPrepOffset, float maxPrepOffset, float mod = 1f)
        {
            var forward = end - start;
            var prepDir = new Vector2(-forward.y, forward.x).normalized;
            var distance = forward.magnitude;
            var prepOffsetFirst = prepDir * (Random.Range(distance * minPrepOffset, distance * maxPrepOffset) * mod);
            var prepOffsetSecond = prepDir * (Random.Range(distance * minPrepOffset, distance * maxPrepOffset) * mod);

            return new Vector3[] {((start + end) * 0.5f + start) * 0.5f + prepOffsetFirst, ((start + end) * 0.5f + end) * 0.5f + prepOffsetSecond, end};
        }
        
        public static Vector3[] GetPathForBoost(Vector2 start, Vector2 end, float minPrepOffset, float mod = 1f)
        {
            var forward = end - start;
            var prepDir = new Vector2(-forward.y, forward.x).normalized;
            var distance = forward.magnitude;
            var prepOffset = prepDir * (distance * minPrepOffset * mod);
            var midValue = new Vector3(((start.x + end.x) * 0.5f + end.x) * 0.5f + prepOffset.x,
                (start.y + end.y) * 0.5f + end.y + prepOffset.y);
            return new Vector3[] {((start + end) * 0.5f + start) * 0.5f + prepOffset, midValue , end};
        }
        
        public static Vector3[] GetThreePointsRandomPathWithAnticipation(Vector2 start, Vector2 end, float minPrepOffset, float maxPrepOffset, Vector2 anticipationMultipliers, float mod = 1f)
        {
            var result = new Vector3[5];
            var threePointsPath = GetThreePointsRandomPath(start, end, minPrepOffset, maxPrepOffset, mod);

            var forward = end - start;
            var horizontalSpread = Mathf.Abs(forward.magnitude * anticipationMultipliers.x);

            result[0] = start;
            result[1] = start + new Vector2(Random.Range(-horizontalSpread, horizontalSpread), forward.y * anticipationMultipliers.y);
            result[2] = threePointsPath[0];
            result[3] = threePointsPath[1];
            result[4] = threePointsPath[2];

            return result;
        }

        //eccentricity < 1 allows to squeeze a circle and make an elliptic curve
        public static Vector3[] GetCircPath(Vector2 start, Vector2 end, float eccentricity = 1f)
        {
            if (start == end)
            {
                return new[] { (Vector3)end };
            }

            const int n = 20;
            var forward = end - start;
            var circCenter = start + forward / 2f;
            var sqrRad = (forward / 2f).sqrMagnitude;
            var upDir = new Vector2(-forward.y, forward.x).normalized;
            var forwardStep = forward / n;

            PathFactoryTempList.Clear();

            if (PathFactoryTempList.Capacity < n)
                PathFactoryTempList.Capacity = n;

            for (var i = 1; i < n; i++)
            {
                var diamPoint = start + forwardStep * i;
                var horProjSqr = (diamPoint - circCenter).sqrMagnitude;
                if (horProjSqr > sqrRad)
                {
                    horProjSqr = sqrRad; // Prevent negative value inside sqrt
                }
                var verProj = Mathf.Sqrt(sqrRad - horProjSqr);
                verProj *= eccentricity;
                var resultPoint = diamPoint + verProj * upDir;
                PathFactoryTempList.Add(resultPoint);
            }

            PathFactoryTempList.Add(end);
            var result = PathFactoryTempList.ToArray();
            PathFactoryTempList.Clear();
            return result;
        }

        public static Vector3[] GetTwoPointsSimplePath(Vector2 start, Vector2 end, float prepOffsetPercent)
        {
            var forward = end - start;
            var prepDir = new Vector2(-forward.y, forward.x).normalized;
            var distance = forward.magnitude;
            var prepOffset = prepDir * distance * prepOffsetPercent;
            return new Vector3[] {((start + end) * 0.5f + start) * 0.5f + prepOffset, end};
        }
    }
}