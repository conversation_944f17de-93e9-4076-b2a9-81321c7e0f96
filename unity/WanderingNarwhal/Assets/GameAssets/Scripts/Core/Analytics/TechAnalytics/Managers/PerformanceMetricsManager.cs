#if UNITY_ANDROID && !UNITY_EDITOR
#define UNITY_ANDROID_DEVICE
#elif UNITY_IPHONE && !UNITY_EDITOR
#define UNITY_IPHONE_DEVICE
#endif

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using UnityEngine;
using BBB.Core.Analytics.TechAnalytics.EventConstants;
using Cysharp.Threading.Tasks;
using UnityEngine.Profiling;
using Debug = System.Diagnostics.Debug;
#if UNITY_IPHONE_DEVICE
using System.Runtime.InteropServices;
#endif

namespace BBB.Core.Analytics.TechAnalytics.Managers
{
    public static class PerformanceMetricsManager
    {
        private const int InformationNotAvailable = -1;
        private const int FpsHitchThreshold = 15;
        private const float HardwareMetricsUpdateInterval = 3.0f; // 3 seconds
        private static DateTime _lastHardwareUsageUpdateTime = DateTime.MinValue;
        private static readonly Dictionary<string, object> HardwareUsageInformation = new();
        private static readonly Dictionary<string, object> DeviceHardwareInformation = new();
        private static bool _deviceHardwareInfoInitialized;
        
#if UNITY_ANDROID_DEVICE
        private static AndroidJavaObject _deviceHardwareInfo;
#elif UNITY_IPHONE_DEVICE
        [DllImport("__Internal")]
        private static extern ulong HardwareInformation_getProcessMemoryUsage();
        [DllImport("__Internal")]
        private static extern ulong HardwareInformation_getAvailableMemory();
        [DllImport("__Internal")]
        private static extern IntPtr HardwareInformation_getCPUType();
        [DllImport("__Internal")]
        private static extern IntPtr HardwareInformation_getCPUUsage();
        [DllImport("__Internal")]
        private static extern IntPtr HardwareInformation_getGPUType();
        [DllImport("__Internal")]
        private static extern ulong HardwareInformation_getAppBinarySize();
        [DllImport("__Internal")]
        private static extern ulong HardwareInformation_getAppDataStorageUsed();
#endif

        static PerformanceMetricsManager()
        {
            SetupFpsTracker().Forget();
            Initialize().Forget();
        }

        private static async UniTask Initialize()
        {
#if !UNITY_EDITOR
           // await UniTask.SwitchToThreadPool();
#endif

#if UNITY_ANDROID_DEVICE
            AndroidJNI.AttachCurrentThread();
#endif
            InitDeviceInformation();
            UpdateHardwareUsageInformationIfNeeded().Forget();
        }

        private static void InitDeviceInformation()
        {
            if (_deviceHardwareInfoInitialized) return;

#if UNITY_ANDROID_DEVICE
            InitDeviceHardwareInfo();

            if (_deviceHardwareInfo != null)
            {
                try
                {
                    var numberOfCores = _deviceHardwareInfo.CallStatic<int>("getNumberOfCores");
                    DeviceHardwareInformation[EventParameters.CpuNumberOfCores] = numberOfCores;
                }
                catch (Exception ex)
                {
                    BDebug.LogError(LogCat.General, $"Exception while trying to get Device Hardware details:NumberOfCores {ex}");
                }
                
                try
                {
                    var minFrequency = _deviceHardwareInfo.CallStatic<int>("getMinimumFrequency");
                    DeviceHardwareInformation[EventParameters.CpuMinFrequency] = minFrequency;
                }
                catch (Exception ex)
                {
                    BDebug.LogError(LogCat.General, $"Exception while trying to get Device Hardware details:CpuMinFrequency {ex}");
                }
                
                try
                {
                    var maxFrequency = _deviceHardwareInfo.CallStatic<int>("getMaximumFrequency");
                    DeviceHardwareInformation[EventParameters.CpuMaxFrequency] = maxFrequency;
                }
                catch (Exception ex)
                {
                    BDebug.LogError(LogCat.General, $"Exception while trying to get Device Hardware details:CpuMaxFrequency {ex}");
                }
                
                try
                {
                    var is64Bit = _deviceHardwareInfo.CallStatic<bool>("is64Bit");
                    DeviceHardwareInformation[EventParameters.CpuIs64Bit] = is64Bit;
                }
                catch (Exception ex)
                {
                    BDebug.LogError(LogCat.General, $"Exception while trying to get Device Hardware details:CpuIs64Bit {ex}");
                }
                
                try
                {
                    var minScalingFrequency = _deviceHardwareInfo.CallStatic<int>("getMinScalingFrequency");
                    if (minScalingFrequency != InformationNotAvailable)
                    {
                        DeviceHardwareInformation[EventParameters.CpuMinScalingFrequency] = minScalingFrequency;
                    }
                    else
                    {
                        BDebug.LogWarning(LogCat.General, "Min Scaling Frequency not available on this device.");
                    }
                }
                catch (Exception ex)
                {
                    BDebug.LogError(LogCat.General, $"Exception while trying to get Device Hardware details:CpuMinScalingFrequency {ex}");
                }

                try
                {
                    var maxScalingFrequency = _deviceHardwareInfo.CallStatic<int>("getMaxScalingFrequency");
                    if (maxScalingFrequency != InformationNotAvailable)
                    {
                        DeviceHardwareInformation[EventParameters.CpuMaxScalingFrequency] = maxScalingFrequency;
                    }
                    else
                    {
                        BDebug.LogWarning(LogCat.General, "Max Scaling Frequency not available on this device.");
                    }
                }
                catch (Exception ex)
                {
                    BDebug.LogError(LogCat.General, $"Exception while trying to get Device Hardware details:CpuMaxScalingFrequency {ex}");
                }
                
                try
                {
                    var glEsVersion = _deviceHardwareInfo.Call<string>("getGlEsVersion");
                    DeviceHardwareInformation[EventParameters.GlEsVersion] = glEsVersion;
                }
                catch (Exception ex)
                {
                    BDebug.LogError(LogCat.General, $"Exception while trying to get Device Hardware details:GlEsVersion {ex}");
                }
                
                try
                {
                    var gpuSupported = _deviceHardwareInfo.Call<bool>("isGPUSupported");
                    DeviceHardwareInformation[EventParameters.GpuSupported] = gpuSupported;
                }
                catch (Exception ex)
                {
                    BDebug.LogError(LogCat.General, $"Exception while trying to get Device Hardware details:GpuSupported {ex}");
                }
                
                try
                {
                    var supportedAbIsArray = _deviceHardwareInfo.CallStatic<string[]>("getSupportedABIs");
                    if (supportedAbIsArray != null)
                    {
                        var supportedAbIs = string.Join(", ", supportedAbIsArray);
                        DeviceHardwareInformation[EventParameters.CpuSupportedAbis] = supportedAbIs;
                    }
                }
                catch (Exception ex)
                {
                    BDebug.LogError(LogCat.General, $"Exception while trying to get Device Hardware details:CpuSupportedAbis {ex}");
                }
            }
#elif UNITY_IPHONE_DEVICE
            var cpuType = GetCPUType();
            DeviceHardwareInformation[EventParameters.CpuType] = cpuType;
            
            var gpuType = GetGPUType();
            DeviceHardwareInformation[EventParameters.GpuType] = gpuType;
#endif

            DeviceHardwareInformation[EventParameters.DeviceRam] = SystemInfo.systemMemorySize;
#if !UNITY_EDITOR
            DeviceHardwareInformation[EventParameters.StorageUsed] = GetOverallStorageUsed();
            DeviceHardwareInformation[EventParameters.ApplicationSizeOnDisk] = GetApplicationSizeOnDisk();
#endif
            _deviceHardwareInfoInitialized = true;
        }
        
#if UNITY_ANDROID_DEVICE
        private static void InitDeviceHardwareInfo()
        {
            if (_deviceHardwareInfo != null) return;
            
            try
            {
                using var unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
                var currentActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
                _deviceHardwareInfo = new AndroidJavaObject("com.bebopbee.hardwarestats.HardwareInformation", currentActivity);
            }
            catch (Exception ex)
            {
                BDebug.LogError(LogCat.General, $"Exception while trying to get Device Hardware details: {ex}");
            }
        }
#endif
        
#if UNITY_IPHONE_DEVICE
        public static string GetCPUType()
        {
            IntPtr ptr = HardwareInformation_getCPUType();
            return Marshal.PtrToStringAuto(ptr);
        }

        public static string GetCPUUsage()
        {
            IntPtr ptr = HardwareInformation_getCPUUsage();
            return Marshal.PtrToStringAuto(ptr);
        }

        public static string GetGPUType()
        {
            IntPtr ptr = HardwareInformation_getGPUType();
            return Marshal.PtrToStringAuto(ptr);
        }
#endif

        private static long GetOverallStorageUsed()
        {
            long usedStorage = 0;
            try {
#if UNITY_ANDROID_DEVICE        
                usedStorage = _deviceHardwareInfo.Call<long>("getOverallStorageUsed");
#elif UNITY_IPHONE_DEVICE
                usedStorage = (long)HardwareInformation_getAppDataStorageUsed();
#endif
            }
            catch(Exception ex) {
                BDebug.LogError(LogCat.General, $"Failed to update StorageUsageMetrics: {ex}");
            }

            return usedStorage;
        }
        
        private static long GetApplicationSizeOnDisk()
        {
            long usedStorage = 0;
            try {
#if UNITY_ANDROID_DEVICE
                usedStorage = _deviceHardwareInfo.Call<long>("getApplicationSizeOnDisk");
#elif UNITY_IPHONE_DEVICE
                usedStorage = (long)HardwareInformation_getAppBinarySize();
#endif
            }
            catch(Exception ex) {
                BDebug.LogError(LogCat.General, $"Failed to update ApplicationSizeOnDiskMetric: {ex}");
            }

            return usedStorage;
        }
        
        private static async UniTask UpdateHardwareUsageInformationIfNeeded()
        {
            if ((DateTime.UtcNow - _lastHardwareUsageUpdateTime).TotalSeconds < HardwareMetricsUpdateInterval) return;
            _lastHardwareUsageUpdateTime = DateTime.UtcNow;
            
#if UNITY_ANDROID_DEVICE
            InitDeviceHardwareInfo();
#endif
            
            UpdateCpuUsageMetrics();
            UpdateMemoryMetrics();
            UpdateBatteryMetrics();
            await UpdateFpsMetrics();
        }

        private static void UpdateCpuUsageMetrics()
        {
            try {
#if UNITY_ANDROID_DEVICE
                var bogoMips = _deviceHardwareInfo.CallStatic<float>("getBogoMips");
                HardwareUsageInformation[EventParameters.CpuBogoMips] = bogoMips;
            
                var clockSpeed = _deviceHardwareInfo.CallStatic<int>("getClockSpeed");
                HardwareUsageInformation[EventParameters.CpuClockSpeed] = clockSpeed;

                var currentThreadCpuUsage =_deviceHardwareInfo.Call<double>("getCurrentThreadCpuUsage");
                HardwareUsageInformation[EventParameters.CpuMainThreadUsage] = currentThreadCpuUsage;
                
                var allThreadsCpuUsage =_deviceHardwareInfo.Call<int>("getAllThreadsCpuUsage");
                HardwareUsageInformation[EventParameters.CpuAllThreadsUsage] = allThreadsCpuUsage;

                var allCoresUsage = _deviceHardwareInfo.CallStatic<int[]>("getCoresUsageGuessFromFreq");
                if (allCoresUsage != null)
                {
                    var coresUsages = string.Join(", ", allCoresUsage);
                    HardwareUsageInformation[EventParameters.CpuAllCoresUsages] = coresUsages;
                }
#elif UNITY_IPHONE_DEVICE
                var cpuUsage = GetCPUUsage();
                HardwareUsageInformation[EventParameters.CpuMainThreadUsage] = cpuUsage;
#endif
            }
            catch (Exception ex) {
                BDebug.LogError(LogCat.General, $"Failed to update CpuUsageMetrics: {ex}");
            }
        }

        private static void UpdateMemoryMetrics()
        {
            var memoryUsage = 0;
            long availableMemory = 0;
            try {
#if UNITY_ANDROID_DEVICE
                memoryUsage =_deviceHardwareInfo.Call<int>("getTotalPss");
                availableMemory = _deviceHardwareInfo.Call<long>("getAvailableMemory");
#elif UNITY_IPHONE_DEVICE
                memoryUsage = (int)HardwareInformation_getProcessMemoryUsage();
                availableMemory = (long)HardwareInformation_getAvailableMemory();
#endif
            }
            catch(Exception ex) {
                BDebug.LogError(LogCat.General, $"Failed to update MemoryUsageMetrics: {ex}");
            }

            if (memoryUsage > 0)
            {
                HardwareUsageInformation[EventParameters.RealRamUsage] = memoryUsage;
            }

            if (availableMemory > 0)
            {
                HardwareUsageInformation[EventParameters.FreeRam] = availableMemory;
            }

            HardwareUsageInformation[EventParameters.UnityRamUsage] = Profiler.GetTotalReservedMemoryLong() / (1024 * 1024);
        }
        private static void UpdateBatteryMetrics()
        {
            HardwareUsageInformation[EventParameters.BatteryStatus] = SystemInfo.batteryStatus.ToString();
            HardwareUsageInformation[EventParameters.BatteryLevel] = SystemInfo.batteryLevel * 100;
            try
            {
#if UNITY_ANDROID_DEVICE
                var batteryTemperature = _deviceHardwareInfo.Call<int>("getBatteryTemperature");
                HardwareUsageInformation[EventParameters.BatteryTemperature] = batteryTemperature;
#endif
            }
            catch (Exception ex)
            {
                BDebug.LogError(LogCat.General, $"Failed to update BatteryUsageMetrics: {ex}");
            } 
        }

        private static async UniTask SetupFpsTracker()
        {
            await UniTask.SwitchToMainThread();
            
            var hitchThreshold = PlayerPrefs.GetInt(LoadingProcessTracker.FpsHitchThresholdKey, FpsHitchThreshold);
            if (hitchThreshold <= 0)
            {
                hitchThreshold = FpsHitchThreshold;
            }
            
            var fpsTrackingEnabled = PlayerPrefs.GetInt(LoadingProcessTracker.FpsTrackerEnabledKey, 1) == 1;

            FPSTracker.Setup(fpsTrackingEnabled, hitchThreshold);
        }
        
        private static async UniTask UpdateFpsMetrics()
        {
            var fpsStats = FPSTracker.GetStatistics();
            if (fpsStats == null)
            {
                await SetupFpsTracker();
            }
           
            if (fpsStats is not { AverageFps: > 0 }) return;

            HardwareUsageInformation[EventParameters.AverageFps] = fpsStats.AverageFps;
            HardwareUsageInformation[EventParameters.MinFps] = fpsStats.MinFps;
            HardwareUsageInformation[EventParameters.MaxFps] = fpsStats.MaxFps;
            HardwareUsageInformation[EventParameters.FpsHitchCount] = fpsStats.HitchOccurrences;
            
            FPSTracker.Restart();
        }
        
        public static async UniTask<Dictionary<string, object>> GetDeviceHardwareInformation()
        {
            if(_deviceHardwareInfoInitialized) return DeviceHardwareInformation;
            
            //await UniTask.SwitchToThreadPool();

#if UNITY_ANDROID_DEVICE
            AndroidJNI.AttachCurrentThread();
#endif
            InitDeviceInformation();
            return DeviceHardwareInformation;
        }

        public static async UniTask<Dictionary<string, object>> GetHardwareUsageInformation()
        {
#if !UNITY_EDITOR
            //await UniTask.SwitchToThreadPool();

#if UNITY_ANDROID_DEVICE
            AndroidJNI.AttachCurrentThread();
#endif
            await UpdateHardwareUsageInformationIfNeeded();
#else
            await UniTask.CompletedTask; // Ensure the method remains asynchronous even in the editor
#endif
            return HardwareUsageInformation; 
        }
    }
}