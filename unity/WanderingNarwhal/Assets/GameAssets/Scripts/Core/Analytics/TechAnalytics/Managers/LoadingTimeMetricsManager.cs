using System;
using System.Collections.Generic;
using System.Text;
using BugsnagUnityPerformance;
using UnityEngine;

namespace BBB.Core.Analytics.TechAnalytics.Managers
{
    public static class LoadingTimeMetricsManager
    {
        public const byte SpanKindLoading = 0;
        private const byte SpanKindVitalConfig = 1;
        private const byte SpanKindServerConfig = 2;
        
        private static DateTimeOffset _loadingStepStartDateTime;
        private static DateTimeOffset _levelLoadStartDateTime;
        private static DateTimeOffset _configsLoadingStartDateTime;
        
        private static readonly HashSet<string> AlreadyEnteredScreenNames = new();

        private static readonly Dictionary<byte, List<LoadingTimeSpan>> LoadingTimeSpans = new()
        {
            { SpanKindLoading, new List<LoadingTimeSpan>() },
            { SpanKindVitalConfig, new List<LoadingTimeSpan>() },
            { SpanKindServerConfig, new List<LoadingTimeSpan>() },
        };
        
        private static readonly Dictionary<byte, LoadingTimeSpan> LoadingTimeFirstClassSpans = new()
        {
            { SpanKindLoading, new LoadingTimeSpan() },
            { SpanKindVitalConfig, new LoadingTimeSpan() },
            { SpanKindServerConfig, new LoadingTimeSpan () },
        };
        
        private static readonly List<LoadingTimeSpan> CachedSpans = new ();
       
        public static bool InitialLoadingEnded { get; private set; }
        
        public static void Initialize()
        { 
            LoadingStarted(true);
            MetricsManagerHelper.OnDetailedAnalyticsInitialization -= UpdateCachedMetrics;
            MetricsManagerHelper.OnDetailedAnalyticsInitialization += UpdateCachedMetrics;
        }
        
        public static void LevelLoadingStarted()
        {
            _levelLoadStartDateTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
        }

        public static double GetLevelLoadingDuration()
        {
            return Math.Round((MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause - _levelLoadStartDateTime).TotalSeconds, 1, MidpointRounding.AwayFromZero);
        }

        public static void LoadingStarted(bool initial, string screenName = null, string prevScreenName = null, string context = null)
        {
            if (initial && InitialLoadingEnded)
            {
                return;
            }

            var firstTime = !initial && screenName != null && AlreadyEnteredScreenNames.Add(screenName);
            
            var name = new StringBuilder(initial ? EventConstants.EventNames.InitialLoading : EventConstants.EventNames.ScreenLoading);

            if(screenName != null && prevScreenName != null && context != null)
            {
                name.Append("/[").Append(prevScreenName).Append("]->[").Append(screenName).Append("]/[").Append(context).Append("]");
            }

            if (firstTime)
            {
                name.Append("-firstEnter-");
            }
            
            LogLoadingStart(name.ToString());
            _loadingStepStartDateTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
        }
        
        public static void ReportInitialLoadingStep(string command)
        {
            if (MetricsManagerHelper.DetailedAnalyticsStatus == MetricsManagerHelper.DetailedAnalyticsStatusDisabled) return;
            
            LogLoadingTimeMetric(command,  _loadingStepStartDateTime, SpanKindLoading, MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause);
            _loadingStepStartDateTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
        }

        private static void EndFirstClassSpan(byte spanKind)
        {
            var  loadingSpan = BugsnagPerformance.StartSpan(LoadingTimeFirstClassSpans[spanKind].Name, new SpanOptions { StartTime = LoadingTimeFirstClassSpans[spanKind].StartTime });
            LogSpans(LoadingTimeSpans[spanKind], loadingSpan);
            loadingSpan.End(LoadingTimeFirstClassSpans[spanKind].EndTime);
            
            LoadingTimeFirstClassSpans[spanKind].Reset();
        }
        
        public static void ReportLoading()
        {
            InitialLoadingEnded = true;
            ReportFirstClassSpan(SpanKindLoading);

        }
        public static void ReportAllVitalConfigsLoad()
        {
            ReportFirstClassSpan(SpanKindVitalConfig);
        }
        
        public static void ReportAllServerConfigsLoad()
        {
            ReportFirstClassSpan(SpanKindServerConfig);
        }
        
        private static void ReportFirstClassSpan(byte spanKind)
        {
            LoadingTimeFirstClassSpans[spanKind].ShouldBeEnded = true;
            LoadingTimeFirstClassSpans[spanKind].EndTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
            
            if (MetricsManagerHelper.DetailedAnalyticsStatus == MetricsManagerHelper.DetailedAnalyticsStatusNotInitialized) return;
            
            EndFirstClassSpan(spanKind);
        }

        public static void ReportConfigLoadSuccess(string configName, DateTimeOffset startTime, bool fromServer)
        {
            if (MetricsManagerHelper.DetailedAnalyticsStatus == MetricsManagerHelper.DetailedAnalyticsStatusDisabled) return;
            
            LogLoadingTimeMetric($"{EventConstants.EventNames.ConfigLoadSuccess}/{configName}", startTime, fromServer ? SpanKindServerConfig : SpanKindVitalConfig, MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause);
        }

        public static void ReportConfigLoadFail(string configName, string reason, DateTimeOffset startTime, bool fromServer)
        {
            if (MetricsManagerHelper.DetailedAnalyticsStatus == MetricsManagerHelper.DetailedAnalyticsStatusDisabled) return;
            
            LogLoadingTimeMetric($"{EventConstants.EventNames.ConfigLoadFail}/{configName}/{reason}", startTime, fromServer ? SpanKindServerConfig : SpanKindVitalConfig, MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause);
        }
        
        private static void LogLoadingStart(string name)
        {
            LoadingTimeFirstClassSpans[SpanKindLoading].Name = name;
            LoadingTimeFirstClassSpans[SpanKindLoading].StartTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
        }

        public static void VitalsConfigsLoadingStart()
        {
            LoadingTimeFirstClassSpans[SpanKindVitalConfig].StartTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
            LoadingTimeFirstClassSpans[SpanKindVitalConfig].Name = EventConstants.EventNames.AllConfigsLoadVital;
        }
        
        public static void ServerConfigsLoadingStart()
        {
            LoadingTimeFirstClassSpans[SpanKindServerConfig].StartTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
            LoadingTimeFirstClassSpans[SpanKindServerConfig].Name = EventConstants.EventNames.AllConfigsLoadFromServer;
        }

        private static void LogSpans(List<LoadingTimeSpan> spans, ISpanContext parentSpan)
        {
            foreach (var span in spans)
            {
                LogLoadingTimeMetric(span, parentSpan);
            }
            spans.Clear();
        }

        private static void LogLoadingTimeMetric(LoadingTimeSpan span, ISpanContext parentSpan)
        {
            BugsnagPerformance.StartSpan(span.Name, new SpanOptions { StartTime = span.StartTime, ParentContext = parentSpan, IsFirstClass = false}).End(span.EndTime);
        }
        
        public static void LogLoadingTimeMetric(string metricName, DateTimeOffset startTime, byte spanKind, DateTimeOffset endTime)
        {
            if (MetricsManagerHelper.DetailedAnalyticsStatus == MetricsManagerHelper.DetailedAnalyticsStatusDisabled)
                return;
            
            var loadingTimeSpan = new LoadingTimeSpan()
            {
                Kind = spanKind,
                Name = metricName,
                StartTime = startTime,
                EndTime = endTime
            };
            
            switch (MetricsManagerHelper.DetailedAnalyticsStatus)
            {
                case MetricsManagerHelper.DetailedAnalyticsStatusEnabled:
                    LoadingTimeSpans[spanKind].Add(loadingTimeSpan);
                    break;
                case MetricsManagerHelper.DetailedAnalyticsStatusNotInitialized:
                    CachedSpans.Add(loadingTimeSpan);
                    break;
            }
        }
        
        private static void UpdateCachedMetrics()
        {
            if (MetricsManagerHelper.DetailedAnalyticsStatus == MetricsManagerHelper.DetailedAnalyticsStatusEnabled)
            {
                foreach (var cachedNetworkSpan in CachedSpans)
                {
                    LogLoadingTimeMetric(cachedNetworkSpan.Name, cachedNetworkSpan.StartTime, cachedNetworkSpan.Kind, cachedNetworkSpan.EndTime);
                }
            }
            
            CachedSpans.Clear();
            foreach (var firstClassSpan in LoadingTimeFirstClassSpans)
            {
                if (firstClassSpan.Value.ShouldBeEnded)
                {
                    EndFirstClassSpan(firstClassSpan.Key);
                }
            }
        }
        
        public static void Restart()
        {
            CachedSpans.Clear();
            MetricsManagerHelper.OnDetailedAnalyticsInitialization -= UpdateCachedMetrics;
        }
        
        private record LoadingTimeSpan
        {
            public bool ShouldBeEnded;
            public byte Kind;
            public string Name;
            public DateTimeOffset StartTime;
            public DateTimeOffset EndTime;

            public void Reset()
            {
                ShouldBeEnded = false;
                Name = null;
                StartTime = default;
                EndTime = default;
            }
        }
        
    }
}