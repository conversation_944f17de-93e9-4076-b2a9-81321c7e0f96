using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using BBB.Core.Tools.FSM.Extenstions;
using BBB.DI;
using BebopBee;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.Profiling;

namespace BBB.Core.Analytics
{
    public class AmplitudeWrapper : IAnalyticsService
    {
        private string _prevScreen = "";
        private bool _isInitialized;

        private readonly OrderedDictionary _cachedEvents = new();
        private readonly List<string> _screenChanges = new List<string>();
        private readonly List<Pair<string, string>> _cachedUserPropertiesString = new List<Pair<string, string>>();
        private readonly List<Pair<string, bool>> _cachedUserPropertiesBool = new List<Pair<string, bool>>();
        private readonly List<Pair<string, double>> _cachedUserPropertiesDouble = new List<Pair<string, double>>();
        private readonly List<Pair<string, int>> _cachedUserPropertiesInt = new List<Pair<string, int>>();
        private readonly List<Pair<string, long>> _cachedUserPropertiesLong = new List<Pair<string, long>>();
        private readonly List<Pair<string, float>> _cachedUserPropertiesFloat = new List<Pair<string, float>>();
        private string _cachedUserId;

        public void Initialize()
        {
            BDebug.Log(LogCat.Analytics, $"[Amplitude] InitializeService");
            Profiler.BeginSample("Amplitude.Instance");
            Amplitude amplitude = Amplitude.Instance;
            Profiler.EndSample();
            amplitude.trackSessionEvents(true);
            var trackingOptions = new Dictionary<string, bool>
            {
                { "disableCity", false },
                { "disableIPAddress", false },
                { "disableIDFV", false },
                { "disableIDFA", false },
                { "disableCountry", false },
                { "disableCarrier", false },
                { "disableADID", false }
            };
            amplitude.setTrackingOptions(trackingOptions);
            Profiler.BeginSample("Amplitude.init");
            if (AppDefinesConverter.BbbDebug)
            {
                amplitude.logging = AppDefinesConverter.AmplitudeLogs;
                amplitude.init("********************************");
            }
            else
            {
                amplitude.init("********************************");
            }

            Profiler.EndSample();
        }

        public void InitializeWithContext(IContext appController)
        {
        }

        private void FillQueuedEvents()
        {
            BDebug.Log(LogCat.Analytics, $"[Amplitude] FillQueuedEvents");
            _cachedEvents?.Map<BaseEvent, Dictionary<string, object>>(LogEventFromCache);
            _cachedEvents?.Clear();

            _cachedUserPropertiesString?.Map(SetPropertyString);
            _cachedUserPropertiesString?.Clear();

            _cachedUserPropertiesBool?.Map(SetPropertyBool);
            _cachedUserPropertiesBool?.Clear();

            _cachedUserPropertiesDouble?.Map(SetPropertyDouble);
            _cachedUserPropertiesDouble?.Clear();

            _cachedUserPropertiesInt?.Map(SetPropertyInt);
            _cachedUserPropertiesInt?.Clear();

            _cachedUserPropertiesLong?.Map(SetPropertyLong);
            _cachedUserPropertiesLong?.Clear();

            _cachedUserPropertiesFloat?.Map(SetPropertyFloat);
            _cachedUserPropertiesFloat?.Clear();
            return;

            void SetPropertyBool(Pair<string, bool> pair) => SetUserProperty(pair.First, pair.Second);

            void SetPropertyDouble(Pair<string, double> pair) => SetUserProperty(pair.First, pair.Second);

            void SetPropertyInt(Pair<string, int> pair) => SetUserProperty(pair.First, pair.Second);

            void SetPropertyLong(Pair<string, long> pair) => SetUserProperty(pair.First, pair.Second);

            void SetPropertyFloat(Pair<string, float> pair) => SetUserProperty(pair.First, pair.Second);

            void SetPropertyString(Pair<string, string> pair) => SetUserProperty(pair.First, pair.Second);
        }

        public void SetUserId(string userId)
        {
            BDebug.Log(LogCat.Analytics, $"[Amplitude] SetUserId : {userId}");
            Amplitude.Instance.setUserId(userId);
            Amplitude.Instance.useAdvertisingIdForDeviceId();
            Amplitude.Instance.uploadEvents();
            UniRx.MainThreadDispatcher.StartEndOfFrameMicroCoroutine(_internalInitialized());
        }

        private IEnumerator _internalInitialized()
        {
            if (_isInitialized)
                yield break;

            yield return null;
            BDebug.Log(LogCat.Analytics, $"[Amplitude] Delayed Initialization");
            UnityEngine.Profiling.Profiler.BeginSample("Amplitude _internalInitialized");
            _isInitialized = true;
            FillQueuedEvents();
            Amplitude.Instance.uploadEvents();
            UnityEngine.Profiling.Profiler.EndSample();
        }

        public void LogEventFromCache(BaseEvent baseEvent, Dictionary<string, object> eventParams)
        {
            Amplitude.Instance.logEvent(baseEvent.EventName, eventParams);
        }

        public async UniTask LogEvent(BaseEvent logEvent)
        {
            var eventParams = await Analytics.GetExtendedEventParamsWithPerformanceInfo(logEvent);

            if (!_isInitialized)
            {
                _cachedEvents.Add(logEvent, eventParams);
                return;
            }

            Amplitude.Instance.logEvent(logEvent.EventName, Analytics.ConvertEventParams(eventParams));
        }

        public void LogIapCurrency(IAPPurchaseEvent iapEvent)
        {
            if (!_isInitialized)
            {
                Debug.LogError("Tracking IAP when Amplitude is not initialized yet");
                return;
            }

            var amplitudeEventProperties = new Dictionary<string, object>()
            {
                { CustomEventParameters.PurchaseSource, iapEvent.PurchaseSource },
                { CustomEventParameters.PurchasePath, iapEvent.PurchasePathString },
                { CustomEventParameters.ProductId, iapEvent.ProductId }
            };

            if (AppDefinesConverter.UnityIos)
            {
                BDebug.Log(LogCat.Analytics,
                    $"[Amplitude] Log IAP product:{iapEvent.StoreProductId} amount:{iapEvent.Amount} price:{iapEvent.Price} receipt:{iapEvent.Receipt} sign:{iapEvent.Payload}");
                Amplitude.Instance.logRevenue(iapEvent.StoreProductId, iapEvent.Amount, iapEvent.Price, iapEvent.Payload, null, null, amplitudeEventProperties);
            }
            else if (AppDefinesConverter.UnityAndroid)
            {
                BDebug.Log(LogCat.Analytics,
                    $"[Amplitude] Log IAP product:{iapEvent.ProductId} amount:{iapEvent.StoreProductId} price:{iapEvent.Price} receipt:{iapEvent.Receipt} data: {iapEvent.PurchaseDataJson}, signature:{iapEvent.Signature}");
                Amplitude.Instance.logRevenue(iapEvent.StoreProductId, iapEvent.Amount, iapEvent.Price, iapEvent.PurchaseDataJson, iapEvent.Signature, null, amplitudeEventProperties);
            }
        }

        public void SetScreen(string screenName, float loadingTime)
        {
            _prevScreen = screenName;
        }

        public void SetUserProperty(string propertyName, string propertyValue)
        {
            if (!_isInitialized)
            {
                _cachedUserPropertiesString.Add(new Pair<string, string>(propertyName, propertyValue));
                return;
            }

            BDebug.Log(LogCat.Analytics, $"[Amplitude] SetUserProperty: {propertyName} value: {propertyValue}");
            Amplitude.Instance.setUserProperty(propertyName, propertyValue);
        }

        public void SetUserProperty(string propertyName, bool propertyValue)
        {
            if (!_isInitialized)
            {
                _cachedUserPropertiesBool.Add(new Pair<string, bool>(propertyName, propertyValue));
                return;
            }

            BDebug.Log(LogCat.Analytics, $"[Amplitude] SetUserProperty: {propertyName} value: {propertyValue}");
            Amplitude.Instance.setUserProperty(propertyName, propertyValue);
        }

        public void SetUserProperty(string propertyName, double propertyValue)
        {
            if (!_isInitialized)
            {
                _cachedUserPropertiesDouble.Add(new Pair<string, double>(propertyName, propertyValue));
                return;
            }

            BDebug.Log(LogCat.Analytics, $"[Amplitude] SetUserProperty: {propertyName} value: {propertyValue}");
            Amplitude.Instance.setUserProperty(propertyName, propertyValue);
        }

        public void SetUserProperty(string propertyName, float propertyValue)
        {
            if (!_isInitialized)
            {
                _cachedUserPropertiesFloat.Add(new Pair<string, float>(propertyName, propertyValue));
                return;
            }

            BDebug.Log(LogCat.Analytics, $"[Amplitude] SetUserProperty: {propertyName} value: {propertyValue}");
            Amplitude.Instance.setUserProperty(propertyName, propertyValue);
        }

        public void SetUserProperty(string propertyName, int propertyValue)
        {
            if (!_isInitialized)
            {
                _cachedUserPropertiesInt.Add(new Pair<string, int>(propertyName, propertyValue));
                return;
            }

            BDebug.Log(LogCat.Analytics, $"[Amplitude] SetUserProperty: {propertyName} value: {propertyValue}");
            Amplitude.Instance.setUserProperty(propertyName, propertyValue);
        }

        public void SetUserProperty(string propertyName, long propertyValue)
        {
            if (!_isInitialized)
            {
                _cachedUserPropertiesLong.Add(new Pair<string, long>(propertyName, propertyValue));
                return;
            }

            BDebug.Log(LogCat.Analytics, $"[Amplitude] SetUserProperty: {propertyName} value: {propertyValue}");
            Amplitude.Instance.setUserProperty(propertyName, propertyValue);
        }

        public void Restart()
        {
        }

        public void OnApplicationPause(bool focus)
        {
        }

        public void SetNotificationToken(string token)
        {
        }

        public void SetSessionTimeout(double maxSleptTime)
        {
        }

        public void PushEvents()
        {
            Amplitude.Instance.uploadEvents();
        }
    }
}