using System;
using System.Collections.Generic;
using System.Threading;
using BBB.Core.AssetBundles;
using BBB.Core.ResourcesManager.Asset;
using BBB.Core.ResourcesManager.Loaders;
using BebopBee;
using Bebopbee.Core.Systems.Ticksystem;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Object = UnityEngine.Object;

namespace BBB.Core.ResourcesManager
{
    public enum LoadMode
    {
        Speed,
        Smooth,
    }
    
    public class AssetsManager : IAssetsManager, ITickable
    {
        private static List<IAssetLoader> _assetTypeLoaders;
        private static readonly Dictionary<string, object> AssetLoadedCache = new ();
        public static readonly LoadMode LoadMode = LoadMode.Smooth;
        private readonly Dictionary<string, CancellationTokenSource> _loadingTokens = new ();

        private IBundleManager _bundleManager;

        public AssetsManager()
        {
            
        }
        public void Init(IBundleManager bundleManager, IBundleInfoProvider bundleInfoProvider, ISpriteAtlasInfoProvider spriteAtlasInfoProvider)
        {
            _bundleManager = bundleManager;
            _assetTypeLoaders = new List<IAssetLoader>()
            {
                new SpriteAtlasLoader(bundleManager, bundleInfoProvider),
                new SpriteInAtlasLoader(spriteAtlasInfoProvider, this),
                new Match3LevelsLoader(bundleManager, bundleInfoProvider),
                new DefaultAssetLoader(bundleManager, bundleInfoProvider)
            };
        }
        
        public UniTask<Sprite> LoadSpriteAsync(string spriteName, bool logError = true)
        { 
            var tcs = new UniTaskCompletionSource<Sprite>();
            LoadAsync<Sprite>(spriteName, logError: logError)
                .ContinueWith(assetLoaded =>
                {
                    var sprite = assetLoaded?.Get();
                    if (sprite == null && logError)
                    {
                        BDebug.LogError(LogCat.Resources, $"Sprite '{spriteName}' not found");
                    }
                    
                    tcs.TrySetResult(sprite);
                });
            return tcs.Task;
        }

        public async UniTask<IAssetLoaded<T>> LoadAsync<T>(string assetName, AssetLoadPriority priority = AssetLoadPriority.InQueue, bool forceEnqueue = false, bool logError = true, CancellationToken externalToken = default) where T : Object
        {
            if (LoadMode == LoadMode.Speed && !forceEnqueue)
            {
                priority = AssetLoadPriority.Immediately;
            }

            if (string.IsNullOrEmpty(assetName))
            {
                BDebug.LogError(LogCat.Resources, "Trying to load asset with empty filename");
                return null;
            }

            if (AssetLoadedCache.TryGetValue(assetName, out var cached))
            {
                switch (cached)
                {
                    case IAssetLoaded<T> loaded:
                        loaded.Retain();
                        return loaded;
                    case IAssetReleaseable releasable when
                        releasable.GetAssetType().IsAssignableFrom(typeof(T)):
                        releasable.Retain();
                        return releasable.ConvertAsset<T>();
                    default:
                        throw new InvalidCastException($"Cannot cast cached asset '{assetName}' to {typeof(T).Name}");
                }
            }

            var cts = CancellationTokenSource.CreateLinkedTokenSource(externalToken);
            _loadingTokens[assetName] = cts;
            var token = cts.Token;
            try
            {
                if (priority == AssetLoadPriority.InQueue)
                {
                    await UniTask.NextFrame(PlayerLoopTiming.Update, cancellationToken: token);
                }

                var result = await LoadAssetAsync<T>(assetName, token, logError);
                return result;
            }
            finally
            {
                _loadingTokens.Remove(assetName);
            }
        }

        public async UniTask<IAssetLoaded<T>[]> LoadAllAsync<T>(string assetName, AssetLoadPriority priority = AssetLoadPriority.InQueue, bool forceEnqueue = false, bool logError = true, CancellationToken externalToken = default) where T : Object
        {
            if (LoadMode == LoadMode.Speed && !forceEnqueue)
            {
                priority = AssetLoadPriority.Immediately;
            }

            if (string.IsNullOrEmpty(assetName))
            {
                BDebug.LogError(LogCat.Resources, "Trying to load all assets with empty filename");
                return Array.Empty<IAssetLoaded<T>>();
            }

            if (AssetLoadedCache.TryGetValue(assetName, out var cachedObj) && cachedObj is IAssetLoaded<T>[] cachedArr)
            {
                foreach (var loaded in cachedArr)
                {
                    loaded.Retain();
                }
                
                return cachedArr;
            }

            var cts = CancellationTokenSource.CreateLinkedTokenSource(externalToken);
            _loadingTokens[assetName] = cts;
            var token = cts.Token;

            try
            {
                if (priority == AssetLoadPriority.InQueue)
                {
                    await UniTask.NextFrame(PlayerLoopTiming.Update, cancellationToken: token);
                }

                IAssetLoaded<T>[] result = null;

                foreach (var loader in _assetTypeLoaders)
                {
                    if (!loader.CanLoad<T>(assetName))
                        continue;
                    
                    result = await loader.LoadAllAsync<T>(assetName, logError).AttachExternalCancellation(token);
                    break;
                }

                if (result == null)
                    throw new InvalidOperationException($"No asset loader available for '{assetName}'");

                for (var i = 0; i < result.Length; i++)
                {
                    result[i].Retain();
                    AssetLoadedCache[$"{assetName}{i}"] = result[i];
                }

                AssetLoadedCache[assetName] = result;
                return result;
            }
            catch (Exception ex)
            {
                BDebug.LogError(LogCat.Resources, $"LoadAllAsync error for '{assetName}': {ex}");
                return Array.Empty<IAssetLoaded<T>>();
            }
            finally
            {
                _loadingTokens.Remove(assetName);
            }
        }

        public async UniTask<IAssetLoaded<T>> LoadAssetAsync<T>(string assetName, CancellationToken ct, bool logError = true) where T : Object
        {
            foreach (var loader in _assetTypeLoaders)
            {
                if (!loader.CanLoad<T>(assetName))
                    continue;

                try
                {
                    var loaded = await loader.LoadAsync<T>(assetName, logError).AttachExternalCancellation(ct);
                    if (loaded != null)
                    {
                        loaded.Retain();
                        AssetLoadedCache[assetName] = loaded;
                    }
                    return loaded;
                }
                catch (OperationCanceledException)
                {
                    // cancellation is expected
                    return null;
                }
                catch (Exception ex)
                {
                    BDebug.LogError(LogCat.Resources, $"LoadAssetAsync error for '{assetName}': {ex}");
                    throw;
                }
            }

            throw new InvalidOperationException($"No asset loader available for '{assetName}'");
        }

        #region load asset in queue

        public void ReleaseAssets()
        {
            var arrayKeys = new List<string>();
            var singleKeys = new List<string>();

            foreach (var (key, value) in AssetLoadedCache)
            {
                switch (value)
                {
                    case IEnumerable<IAssetReleaseable>:
                        arrayKeys.Add(key);
                        break;
                    case IAssetReleaseable:
                        singleKeys.Add(key);
                        break;
                }
            }

            var keysToRemove = new HashSet<string>();

            foreach (var arrayKey in arrayKeys)
            {
                var allAssetsEnumerable = (IEnumerable<IAssetReleaseable>) AssetLoadedCache[arrayKey];

                var assetList = new List<IAssetReleaseable>(allAssetsEnumerable);

                var allFreeable = true;
                foreach (var singleAsset in assetList)
                {
                    if (singleAsset.CanFree())
                        continue;
                    
                    allFreeable = false;
                    break;
                }

                if (!allFreeable)
                {
                    continue;
                }

                foreach (var singleAsset in assetList)
                {
                    singleAsset.Free();
                }

                keysToRemove.Add(arrayKey);
                
                var count = assetList.Count;
                for (var i = 0; i < count; i++)
                {
                    keysToRemove.Add($"{arrayKey}{i}");
                }
            }
            
            foreach (var singleKey in singleKeys)
            {
                if (keysToRemove.Contains(singleKey))
                    continue;

                var singleAsset = (IAssetReleaseable) AssetLoadedCache[singleKey];
                BDebug.Log(LogCat.Resources, $"Reference : {singleKey} count: {singleAsset.GetReferenceCounter()}");

                if (!singleAsset.CanFree())
                    continue;

                singleAsset.Free();
                keysToRemove.Add(singleKey);
            }

            foreach (var removeKey in keysToRemove)
            {
                AssetLoadedCache.Remove(removeKey);
            }

            _bundleManager.ReleaseUnusedBundles();
        }
        
        public void UnloadAsset(Object asset)
        {
            if (asset == null) return;
            
            UnloadAsset(asset.name);

            Resources.UnloadAsset(asset);
        }

        public void UnloadAsset(string assetName)
        {
            if (AssetLoadedCache == null || !AssetLoadedCache.TryGetValue(assetName, out var value)) return;

            if (value != null)
            {
                var assetToRelease = (IAssetReleaseable)value;
                assetToRelease.Free();
                assetToRelease.Dispose();
            }
            AssetLoadedCache.Remove(assetName);
        }
        
        #endregion

        public void Restart()
        {
            var tokensSnapshot = new List<CancellationTokenSource>(_loadingTokens.Values);
            foreach (var cts in tokensSnapshot)
            {
                cts.Cancel();
            }

            _loadingTokens.Clear();

            ReleaseAssets();
            AssetLoadedCache.Clear();
        }
        
        public abstract class RejectAssetByRestartException : Exception
        {
        }

        public void Tick()
        {
            
        }
    }
}