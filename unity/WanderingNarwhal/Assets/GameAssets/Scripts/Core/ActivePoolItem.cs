using BebopBee.UnityEngineExtensions;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI.Extensions;
using Coffee.UIExtensions;

namespace BBB.Core
{
    /// <summary>
    /// This pool item won't enable/disable the game object on spawn/release if it was initialized from the editor.
    /// Otherwise it will behave as a standart pool item and enable/disable the game object.
    /// </summary>
    public class ActivePoolItem : BbbMonoBehaviour, IPoolItem
    {
        [SerializeField]
        [Tooltip("Enable/Disable the game object on Spawn/Release in case of FALSE. And Enable/Disable components on Spawn/Release in case of TRUE")]
        private bool _initialized;

        [SerializeField]
        private List<Component> _components;

        private bool _wasSpawn;

        public bool Initialized => _initialized;

        protected bool DeactivateOnInit => true;

        public virtual void OnInstantiate()
        {
            if (DeactivateOnInit)
            {
                Deactivate();
            }
        }

        public virtual void OnRelease()
        {
            if (this == null)
                return;

            Deactivate();
        }

        private void Deactivate()
        {
            if (!_initialized)
            {
                gameObject.SetActive(false);
                return;
            }

            UpdateStates(_components, false, _wasSpawn);
        }

        public virtual void OnSpawn()
        {
            if (this == null)
                return;

            _wasSpawn = true;

            if (!_initialized)
            {
                gameObject.SetActive(true);
                return;
            }

            UpdateStates(_components, true, _wasSpawn);
        }

        protected void RegisterComponent(Component component)
        {
            if (component == null)
                return;

            _components.Add(component);
        }

        protected void UnregisterComponent(Component component)
        {
            if (component == null)
                return;

            _components.RemoveSwapBack(component);
        }

        private void UpdateStates(List<Component> components, bool state, bool wasSpawn)
        {
            UnityEngine.Profiling.Profiler.BeginSample("ActivePoolItem.UpdateStates");
            for (int i = components.Count - 1; i >= 0; --i)
            {
                Component component = components[i];
                if (component == null)
                {
                    components.RemoveAtSwapBack(i);
                    continue;
                }

                UpdateState(component, state, wasSpawn);
            }
            UnityEngine.Profiling.Profiler.EndSample();
        }

        private void UpdateState(Component component, bool state, bool wasSpawn)
        {
            UnityEngine.Profiling.Profiler.BeginSample("ActivePoolItem.UpdateStates.UpdateState");
            switch (component)
            {
                case Animator animator:
                    if (animator.enabled != state)
                    {
                        if (!state && wasSpawn && animator.isActiveAndEnabled)
                            animator.PlayMainOrRebind();
                        animator.enabled = state;
                    }
                    break;
                case UIParticle uiParticle:
                    if (!wasSpawn)
                        uiParticle.EnableRendererOnDisable = false;
                    if (uiParticle.enabled != state)
                        uiParticle.enabled = state;
                    break;
                case Behaviour behaviour:
                    if (behaviour.enabled != state)
                        behaviour.enabled = state;
                    break;
                case TrailRenderer trailRenderer:
                    if (!state)
                        trailRenderer.Clear();
                    trailRenderer.enabled = state;
                    break;
                case Renderer renderer:
                    if (renderer.enabled != state)
                        renderer.enabled = state;
                    break;
                case ParticleSystem particleSystem:
                    if (!state)
                    {
                        //disable playOnAwake
                        var mainModule = particleSystem.main;
                        mainModule.playOnAwake = false;

                        particleSystem.Stop(true, ParticleSystemStopBehavior.StopEmittingAndClear);
                    }
                    else
                    {
                        //restore playOnAwake
                        var mainModule = particleSystem.main;
                        mainModule.playOnAwake = true;

                        particleSystem.Play(true);
                    }
                    break;
                default:
                    BDebug.LogError(LogCat.General, $"{component.GetType()} is not supported in ActivePoolItem {component.gameObject.name})!");
                    break;
            }
            UnityEngine.Profiling.Profiler.EndSample();
        }

        public static bool IsSupported(Component component)
        {
            return component is Behaviour or Animator or Renderer or ParticleSystem or UIParticleSystem;
        }
    }
}
