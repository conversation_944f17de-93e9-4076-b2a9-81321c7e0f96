using Newtonsoft.Json;

namespace BBB.BrainCloud
{
    [System.Serializable]
    public class BCGameEventResponse : BCRunScriptResponse<BCUserGameEventData>
    {
        
    }
    
    [System.Serializable]
    public class BCUserGameEventData : BCBasicResponseData
    {
        [JsonProperty("eventId")]
        public string EventId { get; set; }
        [JsonProperty("league")]
        public string League { get; set; }
        [JsonProperty("schedule")]
        public TScheduledTime[] Schedule { get; set; }
        [JsonProperty("currentPeriod")]
        public TScheduledTime CurrentPeriod { get; set; }
    }
}