using System;
using System.Collections.Generic;
using System.IO;
using BBB.Core;
using BBB.Social.Chat;
using BrainCloud;
using BrainCloud.UnityWebSocketsForWebGL.WebSocketSharp.Net;
using GameAssets.Scripts.SocialScreens.Teams.Utils;
using GameAssets.Scripts.Utils;
using Newtonsoft.Json;
using UnityEngine;

namespace BBB.BrainCloud
{
    public partial class BrainCloudManager
    {
        public event Action<BCRTTChatResponse> RTTChatCallbackReceived;
        public event Action<BCRTTPresenceResponse> RTTPresenceCallbackReceived;

        private readonly Dictionary<string, Action<bool, string>> _fileUploadCallbacks = new();

        //Got from the BrainCloud client sdk code
        private const string RttConnectionClosedErrorMessage =
            "RTT Connection has been closed. Re-Enable RTT to re-establish connection";

        private const int MaxChatMessagesFetchCount = 250;

        public void EnableRTT(Action success = null, Action failure = null)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            if (_brainCloudWrapper.RTTService.IsRTTEnabled())
            {
                success.SafeInvoke();
                return;
            }

            _brainCloudWrapper.RTTService.EnableRTT(RTTConnectionType.WEBSOCKET, SuccessCallback, ErrorHandlerCallback(FailureCallback));
            _brainCloudWrapper.RTTService.RegisterRTTChatCallback(RTTChatCallback);
            _brainCloudWrapper.RTTService.RegisterRTTPresenceCallback(RTTPresenceCallback);
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                if ((code == -1 && statusMessage == HttpStatusCode.BadRequest.ToInt() && error.Contains(RttConnectionClosedErrorMessage)) || code == (int)BrainCloudError.SessionExpired)
                {
                    BDebug.LogWarning(LogCat.General, $"RTT has been closed. Needs to be re-enabled: error={error} code={code}");
                }
                else
                {
                    BDebug.LogError(LogCat.General, $"Enable RTT failed. error={error} code={code}");
                }

                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Enable RTT success. jsonResponse={jsonResponse}");
                success.SafeInvoke();
            }
        }

        public void DisableRTT()
        {
            _brainCloudWrapper.RTTService.DisableRTT();
        }

        private void RTTChatCallback(string jsonResponse)
        {
            BDebug.Log(LogCat.General, $"RTT Chat Callback. jsonResponse={jsonResponse}");
            var response = JsonConvert.DeserializeObject<BCRTTChatResponse>(jsonResponse);
            RTTChatCallbackReceived.SafeInvoke(response);
        }

        private void RTTPresenceCallback(string jsonResponse)
        {
            BDebug.Log(LogCat.General, $"Presence callback. jsonResponse={jsonResponse}");
            var response = JsonConvert.DeserializeObject<BCRTTPresenceResponse>(jsonResponse);
            RTTPresenceCallbackReceived.SafeInvoke(response);
        }

        private static string GetChannelIdForTeam(string teamUid)
        {
            return $"{GameConstants.BrainCloudAppId}:gr:{teamUid}";
        }

        public void ConnectToChat(string teamUid, Action<BCChannelResponse> success, Action failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            _brainCloudWrapper.ChatService.ChannelConnect(GetChannelIdForTeam(teamUid), MaxChatMessagesFetchCount, SuccessCallback, ErrorHandlerCallback(FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                BDebug.LogError(LogCat.General, $"Connect to chat failed. error={error} code={code}");
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Connect to chat success. jsonResponse={jsonResponse}");
                var response = JsonConvert.DeserializeObject<BCResponse>(jsonResponse);
                var channelData = response.Data.ToObject<BCChannelResponse>();
                success.SafeInvoke(channelData);

                _brainCloudWrapper.Client.DeregisterFileUploadCallback();

                _brainCloudWrapper.Client.RegisterFileUploadCallback(UploadSuccessCallback, UploadFailedCallback);
                return;

                void UploadFailedCallback(string fileUploadId, int statusCode, int reasonCode, string uploadJsonResponse)
                {
                    BDebug.LogError(LogCat.General, $"File upload failed. fileUploadId={fileUploadId} jsonResponse={uploadJsonResponse}");
                    if (_fileUploadCallbacks.TryGetValue(fileUploadId, out var callback))
                    {
                        callback.SafeInvoke(false, uploadJsonResponse);
                    }
                }

                void UploadSuccessCallback(string fileUploadId, string uploadJsonResponse)
                {
                    BDebug.Log(LogCat.General, $"File upload success. jsonResponse={uploadJsonResponse}");
                    if (_fileUploadCallbacks.TryGetValue(fileUploadId, out var callback))
                    {
                        callback.SafeInvoke(true, uploadJsonResponse);
                    }
                }
            }
        }

        public void DisconnectFromChat(string teamUid)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                return;
            }

            _brainCloudWrapper.ChatService.ChannelDisconnect(GetChannelIdForTeam(teamUid));
        }

        public void RegisterListenersForGroup(string teamUid, Action<BCPresenceResponse> success, Action failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            _brainCloudWrapper.PresenceService.RegisterListenersForGroup(teamUid, true, SuccessCallback, ErrorHandlerCallback(FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                BDebug.LogError(LogCat.General, $"Presence group failed. error={error} code={code}");
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Presence group success. jsonResponse={jsonResponse}");
                var response = JsonConvert.DeserializeObject<BCResponse>(jsonResponse);
                var presenceData = response.Data.ToObject<BCPresenceResponse>();
                success.SafeInvoke(presenceData);
            }
        }

        public void SendMessage(string teamUid, ChatMessageRequest chatMessageRequest, Action success = null, Action failure = null)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            _brainCloudWrapper.ChatService.PostChatMessage(GetChannelIdForTeam(teamUid), chatMessageRequest.ToBcJson(), true, SuccessCallback, ErrorHandlerCallback(FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                BDebug.LogError(LogCat.General, $"Send message failed. error={error} code={code}");
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Send message success. jsonResponse={jsonResponse}");
                success.SafeInvoke();
            }
        }

        public void SendMessage(string teamUid, ChatMessageRequest chatMessageRequest, Action<BCMessageData> success = null, Action failure = null)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            _brainCloudWrapper.ChatService.PostChatMessage(GetChannelIdForTeam(teamUid), chatMessageRequest.ToBcJson(), true, SuccessCallback, ErrorHandlerCallback(FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                BDebug.LogError(LogCat.General, $"Send message failed. error={error} code={code}");
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Send message success. jsonResponse={jsonResponse}");
                try
                {
                    var response = JsonConvert.DeserializeObject<BCResponse>(jsonResponse);
                    var messageData = response.Data.ToObject<BCMessageData>();
                    success.SafeInvoke(messageData);
                }
                catch (Exception e)
                {
                    BDebug.LogError(LogCat.General, $"Failed to parse SendMessage response: {e.Message}");
                    failure.SafeInvoke();
                }
            }
        }

        public void SendReaction(ChatMessage message, string reactionUid, Action success = null, Action failure = null)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            var dict = new Dictionary<string, object>
            {
                { "message", message },
                { "reactionId", reactionUid }
            };
            var jsonData = JsonConvert.SerializeObject(dict);
            _brainCloudWrapper.RunScript("AddReactionToMessage", jsonData, SuccessCallback, FailureCallback);
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                success.SafeInvoke();
            }
        }

        public void DeleteMessage(ChatMessage message, Action success, Action failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            _brainCloudWrapper.ChatService.DeleteChatMessage(message.ChannelId, message.Id, -1, SuccessCallback, ErrorHandlerCallback(FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                BDebug.LogError(LogCat.General, $"Delete message failed. error={error} code={code}");
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Delete message success. jsonResponse={jsonResponse}");
                success.SafeInvoke();
            }
        }

        public void ClaimIap(ChatMessage message, Action success, Action failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            var dict = new Dictionary<string, object>
            {
                { "message", message }
            };
            var jsonData = JsonConvert.SerializeObject(dict);
            _brainCloudWrapper.RunScript("ClaimIapSharedGift", jsonData, SuccessCallback, FailureCallback);
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                success.SafeInvoke();
            }
        }

        private void UploadFileWithCoverToChat(string path, Texture2D cover, Action<BCFileUploadedResponse[]> success, Action failure)
        {
            var filename = $"{Util.UnixUtcTimestamp()}_cover_{Path.GetFileName(path)}";
            _brainCloudWrapper.FileService.UploadFileFromMemory("chat", filename, true, true, cover.EncodeToJPG(50), CoverUploadSuccessCallback, ErrorHandlerCallback(CoverUploadFailureCallback));
            return;

            void CoverUploadSuccessCallback(string coverJsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Upload cover success. jsonResponse={coverJsonResponse}");
                var response = JsonConvert.DeserializeObject<BCResponse>(coverJsonResponse);
                var coverUploadResponse = response.Data.ToObject<BCFileUploadResponse>();
                var fileUploadId = coverUploadResponse.fileDetails.uploadId;

                _fileUploadCallbacks[fileUploadId] = FileUploadCallback;
                return;

                void FileUploadCallback(bool ok, string jsonResponse)
                {
                    DestroyCover();

                    if (!ok)
                    {
                        failure.SafeInvoke();
                        return;
                    }

                    var deserializeObject = JsonConvert.DeserializeObject<BCResponse>(jsonResponse);
                    var coverUploadedResponse = deserializeObject.Data.ToObject<BCFileUploadedResponse>();

                    UploadFileToChat(path, FileUploadedSuccess, failure);
                    return;

                    void FileUploadedSuccess(BCFileUploadedResponse fileUploadedResponse)
                    {
                        success.SafeInvoke(new[] { coverUploadedResponse, fileUploadedResponse });
                    }
                }
            }

            void CoverUploadFailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                BDebug.LogError(LogCat.General, $"Upload cover failed. error={error} code={code}");
                DestroyCover();

                failure.SafeInvoke();
            }

            void DestroyCover()
            {
                cover.hideFlags = HideFlags.HideAndDontSave;
                Destroy(cover);
                Resources.UnloadUnusedAssets();
            }
        }

        public void UploadGifToChat(string path, Action<BCFileUploadedResponse[]> success, Action failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            var firstFrame = GifUtils.GetFirstFrame(path);
            if (firstFrame == null)
            {
                failure.SafeInvoke();
                return;
            }

            UploadFileWithCoverToChat(path, firstFrame, success, failure);
        }


        public void UploadFileToChat(string path, Action<BCFileUploadedResponse> success, Action failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            Stream info = new FileStream(path, FileMode.Open);

            if (info.Length == 0)
            {
                failure.SafeInvoke();
                return;
            }

            byte[] fileData = new byte[(int)info.Length];
            info.Seek(0, SeekOrigin.Begin);
            info.Read(fileData, 0, (int)info.Length);
            info.Close();

            var filename = $"{Util.UnixUtcTimestamp()}_{Path.GetFileName(path)}";
            _brainCloudWrapper.FileService.UploadFileFromMemory("chat", filename, true, true, fileData, SuccessCallback, ErrorHandlerCallback(FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                BDebug.LogError(LogCat.General, $"Upload file failed. error={error} code={code}");
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Upload file success. jsonResponse={jsonResponse}");
                var response = JsonConvert.DeserializeObject<BCResponse>(jsonResponse);
                var fileUploadResponse = response.Data.ToObject<BCFileUploadResponse>();
                var fileUploadId = fileUploadResponse.fileDetails.uploadId;

                _fileUploadCallbacks[fileUploadId] = FileUploadCallback;
                return;

                void FileUploadCallback(bool ok, string uploadResponse)
                {
                    if (!ok)
                    {
                        failure.SafeInvoke();
                        return;
                    }

                    var bcResponse = JsonConvert.DeserializeObject<BCResponse>(uploadResponse);
                    var fileUploadedResponse = bcResponse.Data.ToObject<BCFileUploadedResponse>();
                    success.SafeInvoke(fileUploadedResponse);
                }
            }
        }

        public void UploadVideoToChat(string path, Action<BCFileUploadedResponse[]> success, Action failure)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            var texture = NativeGallery.GetVideoThumbnail(path, markTextureNonReadable: false, maxSize: 512);
            if (texture != null)
            {
                UploadFileWithCoverToChat(path, texture, success, failure);
            }
            else
            {
                // Upload video without preview
                UploadFileToChat(path, response => { success.SafeInvoke(new[] { response }); }, failure);
            }
        }

        public void UpdateActivity(Dictionary<string, object> activty, Action success = null, Action failure = null)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            _brainCloudWrapper.PresenceService.UpdateActivity(JsonConvert.SerializeObject(activty), SuccessCallback, ErrorHandlerCallback(FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                BDebug.LogError(LogCat.General, $"Update activity failed. error={error} code={code}");
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                BDebug.Log(LogCat.General, $"Update activty success. jsonResponse={jsonResponse}");
                success.SafeInvoke();
            }
        }

        public void FlagMessage(ChatMessage message, Action success = null, Action failure = null)
        {
            // Can't do any requests if the user is not authenticated
            if (!IsAuthenticated)
            {
                failure.SafeInvoke();
                return;
            }

            var dict = new Dictionary<string, object>
            {
                { "message", message },
            };
            var jsonData = JsonConvert.SerializeObject(dict);
            _brainCloudWrapper.RunScript("ReportMessage", jsonData, SuccessCallback, ErrorHandlerCallback(FailureCallback));
            return;

            void FailureCallback(int statusMessage, int code, string error, object cbObject)
            {
                failure.SafeInvoke();
            }

            void SuccessCallback(string jsonResponse, object cbObject)
            {
                success.SafeInvoke();
            }
        }
    }
}