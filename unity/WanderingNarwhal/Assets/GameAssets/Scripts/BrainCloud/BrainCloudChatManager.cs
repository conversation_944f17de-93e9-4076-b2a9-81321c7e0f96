using System;
using System.Collections.Generic;
using BBB;
using BBB.Chat;
using BBB.Core;
using BBB.DI;
using BBB.Social.Chat;
using Core.RPC;
using BebopBee;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages;
using GameAssets.Scripts.Utils;
using UnityEngine;

namespace BBB.BrainCloud
{
    public sealed class BrainCloudChatManager : IContextInitializable, IContextReleasable, IChatManager
    {
        public event Action KickedFromChat;
        public event Action ConnectedToChatSuccess;
        public event Action<bool> ConnectedToChatFailure;
        public event Action<ChatMessage> ChatMessageAdded;
        public event Action<ChatMessage> ChatMessageUpdated;
        public event Action<ChatMessage> ChatMessageDeleted;
        public event Action<int> UnreadMessagesUpdated;
        public event Action<Dictionary<string, ChatPresence>> OnlinePresenceUpdated;
        public event Action<Dictionary<string, ChatPresence>> UserTypingEvent;

        public Dictionary<string, ChatMessage> Messages { get; } = new ();
        public Dictionary<string, ChatPresence> OnlineUsers { get; } = new ();
        public Dictionary<string, ChatPresence> TypingUsers { get; } = new ();
        public bool IsTyping {
            set {
                if (_accountManager.IsInTeam)
                {
                    SendUserActivity(new Dictionary<string, object> {
                        { "typing", value }
                    });
                }
            }
        }

        private BrainCloudManager _brainCloudManager;
        private IAccountManager _accountManager;
        private IEventDispatcher _eventDispatcher;

        private int _unreadCount;
        private bool _chatConnected;
        private bool _enablingRtt;
        private readonly HashSet<string> _pendingLocalMessageIds = new();
        private const string AdditionalPropertyKey_IsConfirmed = "isConfirmed";
        private const string AdditionalPropertyKey_IsServerMessage = "isServerMessage";
        private const string AdditionalPropertyKey_ResponseData = "responseData";
        private const string AdditionalPropertyKey_RichContent = "richContent";

        public void InitializeByContext(IContext context)
        {
            _brainCloudManager = context.Resolve<BrainCloudManager>();
            _accountManager = context.Resolve<IAccountManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();

            Subscribe();

            if (_brainCloudManager.IsAuthenticated)
            {
                OnAuthenticationFinishedHandler();
            }
            else
            {
                _brainCloudManager.OnAuthenticationFinished += OnAuthenticationFinishedHandler;
            }
        }

        private void Subscribe()
        {
            Unsubscribe();

            _brainCloudManager.RTTChatCallbackReceived += RTTChatCallbackReceivedHandler;
            _brainCloudManager.RTTPresenceCallbackReceived += RTTPresenceCallbackReceivedHandler;

            _accountManager.TeamChanged += UpdateChatConnection;

            _eventDispatcher.AddListener<IapPurchasedEvent>(IapPurchasedEventHandler);
        }

        private void Unsubscribe()
        {
            _brainCloudManager.OnAuthenticationFinished -= OnAuthenticationFinishedHandler;

            _brainCloudManager.RTTChatCallbackReceived -= RTTChatCallbackReceivedHandler;
            _brainCloudManager.RTTPresenceCallbackReceived -= RTTPresenceCallbackReceivedHandler;
            
            _accountManager.TeamChanged -= UpdateChatConnection;

            _eventDispatcher.RemoveListener<IapPurchasedEvent>(IapPurchasedEventHandler);
        }
        
        public void ReleaseByContext(IContext context)
        {
            Unsubscribe();
        }

        private void RTTChatCallbackReceivedHandler(BCRTTChatResponse chatResponse)
        {
            if (chatResponse.service != "chat") return;

            var chatMessage = ChatMessage.LoadFromResponse(chatResponse.data);
            
            switch (chatResponse.operation)
            {
                case "INCOMING":
                    if (Messages.ContainsKey(chatMessage.Id))
                    {
                        return;
                    }

                    if (IsLocalUser(chatMessage.Sender?.Id) && _pendingLocalMessageIds.Count > 0)
                    {
                        ChatMessage matchingLocalMessage = null;
                        
                        var localMessageId = chatMessage.AdditionalProperties?.GetValueOrDefault("localMessageId")?.ToString();
                        if (!string.IsNullOrEmpty(localMessageId))
                        {
                            if (Messages.TryGetValue(localMessageId, out var exactMatch) && 
                                _pendingLocalMessageIds.Contains(localMessageId))
                            {
                                matchingLocalMessage = exactMatch;
                            }
                        }
                        
                        if (matchingLocalMessage != null)
                        {
                            UpdateLocalMessageToReal(matchingLocalMessage, chatMessage);
                            return;
                        }
                    }

                    Messages[chatMessage.Id] = chatMessage;
                    ChatMessageAdded.SafeInvoke(chatMessage);

                    if (chatMessage.GetMessageType() == MessageType.KickFromTeam)
                        KickedFromChat.SafeInvoke();
                    if (IsMessageUnread(chatMessage))
                    {
                        _unreadCount++;
                    }
                    UnreadMessagesUpdated.SafeInvoke(_unreadCount);
                    break;
                case "UPDATE":
                    Messages[chatMessage.Id] = chatMessage;
                    ChatMessageUpdated.SafeInvoke(chatMessage);
                    break;
                case "DELETE":
                    Messages.Remove(chatMessage.Id);
                    ChatMessageDeleted.SafeInvoke(chatMessage);
                    break;
            }
            
        }

        private bool IsMessageUnread(ChatMessage chatMessage)
        {
            return !IsLocalUser(chatMessage.Sender.Id) && chatMessage.CreatedAt > _accountManager.LocalPlayer.LastSeenMessageTimestamp;
        }

        private void RTTPresenceCallbackReceivedHandler(BCRTTPresenceResponse presenceResponse)
        {
            if (presenceResponse.service != "presence") return;

            switch (presenceResponse.operation)
            {
                case "INCOMING":
                    var profileId = (string) presenceResponse.data.User?["id"];
                    if (profileId == null) break;
                    
                    var chatPresence = ChatPresence.FromBcPresenceData(presenceResponse.data);
                    var activity = presenceResponse.data.activity;
                    if (activity != null)
                    {
                        if (activity.TryGetValue("typing", out var isTyping))
                        {
                            if ((bool) isTyping)
                            {
                                TypingUsers[profileId] = chatPresence;
                            }
                            else
                            {
                                TypingUsers.Remove(profileId);
                            }
                        }

                        UserTypingEvent.SafeInvoke(TypingUsers);
                    }
                    
                    if (presenceResponse.data.online.HasValue)
                    {
                        var isOnline = presenceResponse.data.online.Value;
                        if (isOnline)
                        {
                            OnlineUsers[profileId] = ChatPresence.FromBcPresenceData(presenceResponse.data);
                        }
                        else
                        {
                            OnlineUsers.Remove(profileId);
                        }

                        OnlinePresenceUpdated.SafeInvoke(OnlineUsers);
                    }
                    break;
            }
            
        }

        private void OnAuthenticationFinishedHandler()
        {
            _brainCloudManager.OnAuthenticationFinished -= OnAuthenticationFinishedHandler;
            UpdateChatConnection();
        }

        public bool IsChatConnected()
        {
            return _chatConnected;
        }

        /// <summary>
        /// Call this method in case there is no internet connection at the moment and we don't want to wait until RTT will be disabled automatically,
        /// but want to handle it instantly, considering it as closed connection. Call UpdateChatConnection method for reconnecting again when needed.
        /// </summary>
        public void HandleNoInternetConnection()
        {
            if (ConnectivityStatusManager.ConnectivityReachable)
            {
                return;
            }

            _brainCloudManager.DisableRTT();
            ChatFailureCallback();
        }

        public void UpdateChatConnection()
        {
            TypingUsers.Clear();
            UserTypingEvent.SafeInvoke(TypingUsers);
            
            if (!_accountManager.IsInTeam)
                return;
            
            // We can only have 1 call in progress, so we need to wait until the previous one is done before starting a new one
            if (_enablingRtt)
                return;
            
            _enablingRtt = true;
            _brainCloudManager.EnableRTT(SuccessCallback, ChatFailureCallback);
            return;

            void SuccessCallback()
            {
                _enablingRtt = false;
                //already kicked from the team
                if (!_accountManager.IsInTeam) return;

                _brainCloudManager.ConnectToChat(_accountManager.Profile.CurrentTeam.TeamUid, channelData =>
                {
                    _chatConnected = true;
                    _unreadCount = 0;
                    Messages.Clear();
                    foreach (var message in channelData.messages)
                    {
                        var chatMessage = ChatMessage.LoadFromResponse(message);
                        Messages[message.msgId] = chatMessage;
                        if (IsMessageUnread(chatMessage))
                        {
                            _unreadCount++;
                        }
                    }

                    ConnectedToChatSuccess.SafeInvoke();
                    UnreadMessagesUpdated.SafeInvoke(_unreadCount);
                }, ChatFailureCallback);

                _brainCloudManager.RegisterListenersForGroup(_accountManager.Profile.CurrentTeam.TeamUid, presenceData =>
                {
                    OnlineUsers.Clear();

                    // Add me as an online user
                    OnlineUsers[_brainCloudManager.ProfileId] = new ChatPresence { User = GetChatUserData(), IsOnline = true, };

                    foreach (var presence in presenceData.presence)
                    {
                        var profileId = (string)presence.User["id"];
                        if (profileId == null) break;

                        var chatPresence = ChatPresence.FromBcPresenceData(presence);
                        if (presence.online ?? false)
                        {
                            OnlineUsers[profileId] = chatPresence;
                        }

                        if (presence.activity.TryGetValue("typing", out _))
                        {
                            TypingUsers[profileId] = chatPresence;
                        }
                    }

                    OnlinePresenceUpdated.SafeInvoke(OnlineUsers);
                    UserTypingEvent.SafeInvoke(TypingUsers);
                }, null);
            }
        }

        private ChatUser GetChatUserData()
        {
            return new ChatUser
            {
                Id = _brainCloudManager.ProfileId,
                Name = _accountManager.Profile.Name,
                Pic = _accountManager.Profile.Avatar,
            };
        }

        private void ChatFailureCallback()
        {
            bool repeatedFailure = !_chatConnected;
            _chatConnected = false;
            _enablingRtt = false;
            ConnectedToChatFailure.SafeInvoke(repeatedFailure);
        }

        private void IapPurchasedEventHandler(IapPurchasedEvent obj)
        {
            if (_accountManager.IsInTeam)
            {
                SendMessage(new ChatMessageRequest()
                {
                    Text = _accountManager.Profile.Name,
                    MessageType = MessageType.IAPShared.ToString(),
                    AdditionalProperties = new Dictionary<string, object>
                    {
                        { ChatMessageAdditionalProperties.ProductID, obj.Arg0.Product.definition.id },
                    }
                }, (Action)null, (Action)null);
            }
        }

        public bool IsLocalUser(string profileId)
        {
            return profileId != null && profileId.Equals(_brainCloudManager.ProfileId);
        }

        public void SendMessage(ChatMessageRequest chatMessageRequest, Action success = null, Action failure = null)
        {
            if (!_accountManager.IsInTeam)
            {
                failure.SafeInvoke();
                return;
            }

            chatMessageRequest.AdditionalProperties ??= new Dictionary<string, object>();
            chatMessageRequest.AdditionalProperties[ChatMessageAdditionalProperties.UserCountry] = _accountManager.Profile.Country;
            _brainCloudManager.SendMessage(_accountManager.Profile.CurrentTeam.TeamUid, chatMessageRequest, success, failure);
        }

        public bool HasOwnReactionOnMessage(ChatMessage message, string reactionType)
        {
            return message.Reactions.TryGetValue(reactionType, out var users) && users.Contains(_brainCloudManager.ProfileId);
        }

        public void SendReaction(ChatMessage message, string reactionUid, Action success = null, Action failure = null)
        {
            _brainCloudManager.SendReaction(message, reactionUid, success, failure);
        }

        public void DeleteMessage(ChatMessage message, Action success = null, Action failure = null)
        {
            _brainCloudManager.DeleteMessage(message, success, failure);
        }

        public void SetLocalHelpSent(ChatMessage message)
        {
            if (!AlreadySentHelp(message))
            {
                message.Helps?.Add(_brainCloudManager.ProfileId);
            }
        }

        public bool AlreadySentHelp(ChatMessage message)
        {
            return message.Helps?.Contains(_brainCloudManager.ProfileId) ?? false;
        }

        public void ClaimIap(ChatMessage message, Action success = null, Action failure = null)
        {
            _brainCloudManager.ClaimIap(message, () =>
            {
                // Send IAP claimed system message with same timestamp as original message
                SendIAPClaimedMessage(message);
                success?.Invoke();
            }, failure);
        }

        private void SendIAPClaimedMessage(ChatMessage originalMessage)
        {
            if (_accountManager.IsInTeam)
            {
                var claimedMessageRequest = new ChatMessageRequest()
                {
                    Text = originalMessage.Text, // Buyer name
                    MessageType = MessageType.IAPClaimed.ToString(),
                    AdditionalProperties = new Dictionary<string, object>
                    {
                        { ChatMessageAdditionalProperties.UserCountry, _accountManager.Profile.Country }
                    }
                };

                SendMessage(claimedMessageRequest, (Action)null, (Action)null);
            }
        }

        public void SetLocalIapClaimed(ChatMessage message)
        {
            if (!AlreadyClaimedIap(message))
            {
                message.IapClaims?.Add(_brainCloudManager.ProfileId);
            }
        }

        public bool AlreadyClaimedIap(ChatMessage message)
        {
            return message.IapClaims?.Contains(_brainCloudManager.ProfileId) ?? false;
        }

        public ChatMessage AddLocalMessage(MessageType messageType)
        {
            var localMessage = new ChatMessage
            {
                Id = $"local_{Guid.NewGuid()}",
                Type = messageType.ToString(),
                CreatedAt = Util.UnixUtcTimestamp(),
                ChannelId = _accountManager.IsInTeam ? $"{GameConstants.BrainCloudAppId}:gr:{_accountManager.Profile.CurrentTeam.TeamUid}" : null,
                Sender = GetChatUserData()
            };
            
            _pendingLocalMessageIds.Add(localMessage.Id);
            Messages[localMessage.Id] = localMessage;
            ChatMessageAdded.SafeInvoke(localMessage);
            return localMessage;
        }

        public void RemoveLocalMessage(ChatMessage message)
        {
            if (message.Id != null && Messages.ContainsKey(message.Id))
            {
                Messages.Remove(message.Id);
                _pendingLocalMessageIds.Remove(message.Id);
            }
            ChatMessageDeleted.SafeInvoke(message);
        }

        public void UpdateLocalMessageToReal(ChatMessage localMessage, ChatMessage serverMessage)
        {   
            if (localMessage.AdditionalProperties == null)
            {
                localMessage.AdditionalProperties = new Dictionary<string, object>();
            }
            
            var oldLocalId = localMessage.Id;
            _pendingLocalMessageIds.Remove(oldLocalId);
            
            localMessage.AdditionalProperties[AdditionalPropertyKey_IsConfirmed] = true;
            localMessage.AdditionalProperties[AdditionalPropertyKey_IsServerMessage] = true;
            localMessage.AdditionalProperties[AdditionalPropertyKey_ResponseData] = serverMessage;

            ApplyServerData(localMessage, serverMessage);

            if (Messages.Remove(oldLocalId))
            {
                Messages[localMessage.Id] = localMessage;
            }
            
            ChatMessageUpdated.SafeInvoke(localMessage);
        }

        private void ApplyServerData(ChatMessage localMessage, ChatMessage serverMessage)
        {
            // Copy all relevant data from server message to local message
            localMessage.Id = serverMessage.Id;
            localMessage.ChannelId = serverMessage.ChannelId;
            localMessage.CreatedAt = serverMessage.CreatedAt;
            localMessage.Text = serverMessage.Text;
            localMessage.Type = serverMessage.Type;
            
            if (serverMessage.Sender != null)
            {
                if (localMessage.Sender == null)
                {
                    localMessage.Sender = new ChatUser();
                }
                localMessage.Sender.Id = serverMessage.Sender.Id;
                localMessage.Sender.Name = serverMessage.Sender.Name;
                localMessage.Sender.Pic = serverMessage.Sender.Pic;
            }
            
            // Copy additional properties if they exist
            if (serverMessage.AdditionalProperties != null)
            {
                if (localMessage.AdditionalProperties == null)
                {
                    localMessage.AdditionalProperties = new Dictionary<string, object>();
                }
                
                foreach (var kvp in serverMessage.AdditionalProperties)
                {
                    localMessage.AdditionalProperties[kvp.Key] = kvp.Value;
                }
            }
            
            // Copy reactions, helps, claims, and attachments
            localMessage.Reactions = serverMessage.Reactions ?? new Dictionary<string, HashSet<string>>();
            localMessage.Helps = serverMessage.Helps ?? new HashSet<string>();
            localMessage.IapClaims = serverMessage.IapClaims ?? new HashSet<string>();
            localMessage.Attachments = serverMessage.Attachments ?? new List<ChatMessageAttachement>();
        }

        public void UploadGifToChat(string path, Action<ChatMessageAttachementUploaded[]> success, Action failure)
        {
            Action<BCFileUploadedResponse[]> successCallback = bcResponses => {
                var responses = new ChatMessageAttachementUploaded[bcResponses.Length];
                for (var i = 0; i < bcResponses.Length; i++)
                {
                    responses[i] = new ChatMessageAttachementUploaded {
                        AssetUrl = bcResponses[i].fileDetails.downloadUrl,
                    };
                }
                success.SafeInvoke(responses);
            };
            _brainCloudManager.UploadGifToChat(path, successCallback, failure);
        }

        public void UploadFileToChat(string path, Action<ChatMessageAttachementUploaded> success, Action failure)
        {
            Action<BCFileUploadedResponse> successCallback = response => {
                
                success.SafeInvoke(new ChatMessageAttachementUploaded {
                        AssetUrl = response.fileDetails.downloadUrl,
                    });
            };
            _brainCloudManager.UploadFileToChat(path, successCallback, failure);
        }

        public void UploadVideoToChat(string path, Action<ChatMessageAttachementUploaded[]> success, Action failure)
        {
            Action<BCFileUploadedResponse[]> successCallback = bcResponses => {
                var responses = new ChatMessageAttachementUploaded[bcResponses.Length];
                for (var i = 0; i < bcResponses.Length; i++)
                {
                    responses[i] = new ChatMessageAttachementUploaded {
                        AssetUrl = bcResponses[i].fileDetails.downloadUrl,
                    };
                }
                success.SafeInvoke(responses);
            };
            _brainCloudManager.UploadVideoToChat(path, successCallback, failure);
        }

        public void MarkAllAsRead()
        {
            _unreadCount = 0;
            _accountManager.LocalPlayer.LastSeenMessageTimestamp = Util.UnixUtcTimestamp();
        }

        private void SendUserActivity(Dictionary<string, object> activity)
        {
            _brainCloudManager.UpdateActivity(activity);
        }

        public void FlagMessage(ChatMessage message, Action success = null, Action failure = null)
        {
            _brainCloudManager.FlagMessage(message, success, failure);
        }

        public string GetLanguage()
        {
            return LanguageHelper.GetISOCodeFromSystemLanguageEx().ToLower();
        }
    }
}