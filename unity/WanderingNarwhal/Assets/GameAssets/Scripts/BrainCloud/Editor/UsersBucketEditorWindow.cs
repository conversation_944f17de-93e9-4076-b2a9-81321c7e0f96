using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using Newtonsoft.Json;
using UnityEditor;
using UnityEngine;
using UnityEngine.Networking;

namespace BBB.BrainCloud
{
    public class FloatOrIntConverter : JsonConverter
    {
        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(int);
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Float || reader.TokenType == JsonToken.Integer)
            {
                var value = Convert.ToInt32(reader.Value);
                return value;
            }

            throw new JsonSerializationException($"Unexpected token type: {reader.TokenType}");
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            throw new NotImplementedException();
        }
    }

    public class UsersBucketEditorWindow : EditorWindow
    {
        /*
         * {
             "response": {
               "data": {
                 "bucketId": 2
               }
             },
             "success": true
           }
         */
        [Serializable]
        public class BCResponseS2SBase<TData>
        {
            [JsonProperty("response")]
            protected BCResponse<TData> _response;

            public TData Data => _response.Data;
        }

        [Serializable]
        public class UsersBucketResponse : BCResponseS2SBase<UsersBucketResponseData>
        {
        }

        [Serializable]
        public class UsersBucketResponseData
        {
            [JsonProperty("bucketId")]
            [JsonConverter(typeof(FloatOrIntConverter))]
            public int BucketId;
            [JsonProperty("leaderboardId")]
            public string LeaderboardId;
        }

        // Variables for Environment
        private enum BcEnvironment
        {
            DevEditor,
            Test, 
            Prod
        }

        private const string BrainCloudS2SUrl = "https://api.braincloudservers.com/s2sdispatcher";

        private static readonly Dictionary<BcEnvironment, string> BrainCloudGameIds = new Dictionary<BcEnvironment, string>()
        {
            { BcEnvironment.DevEditor, "14589" },
            { BcEnvironment.Test, "14664" },
            { BcEnvironment.Prod, "" },
        };

        private static readonly Dictionary<BcEnvironment, string> BrainCloudGameSecret = new Dictionary<BcEnvironment, string>()
        {
            { BcEnvironment.DevEditor, "c64062e4-a9a5-4376-aaca-4c1773d1e258" },
            { BcEnvironment.Test, "c64062e4-a9a5-4376-aaca-4c1773d1e258" },
            { BcEnvironment.Prod, "" },
        };

        private BcEnvironment environment = BcEnvironment.DevEditor;
        private string userId = "";
        private string profileId = "";
        private string bucketId = "";
        private string notificationMessage = "";
        private string currentLeaderboardId = "";

        [MenuItem("brainCloud/Custom/Users Bucket Editor", false, 300)]
        public static void ShowWindow()
        {
            EditorWindow.GetWindow(typeof(UsersBucketEditorWindow));
        }

        private void OnGUI()
        {
            GUILayout.Label("Environment: " + environment, EditorStyles.boldLabel);

            // Fields
            GUILayout.Label("Fields", EditorStyles.boldLabel);
            GUILayout.BeginHorizontal();
            userId = EditorGUILayout.TextField("User ID", userId);
            if (GUILayout.Button("Fill My UserId", GUILayout.Width(100)))
            {
                GetMyUserId();
            }

            GUILayout.EndHorizontal();
            profileId = EditorGUILayout.TextField("Profile ID", profileId);
            bucketId = EditorGUILayout.TextField("Bucket ID", bucketId);
            currentLeaderboardId = EditorGUILayout.TextField("Current Event Id", currentLeaderboardId);

            // Actions buttons
            GUILayout.Label("Actions", EditorStyles.boldLabel);
            if (GUILayout.Button("Fetch"))
            {
                FetchData();
            }

            if (GUILayout.Button("Update"))
            {
                UpdateData();
            }

            // Switch Environment buttons
            GUILayout.Label("Switch Environment", EditorStyles.boldLabel);
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("BBB-Dev/Editor"))
            {
                SwitchEnvironment(BcEnvironment.DevEditor);
            }

            if (GUILayout.Button("BBB-Test"))
            {
                SwitchEnvironment(BcEnvironment.Test);
            }

            if (GUILayout.Button("BBB-Prod"))
            {
                SwitchEnvironment(BcEnvironment.Prod);
            }

            GUILayout.EndHorizontal();

            GUILayout.Label("Status", EditorStyles.boldLabel);
            notificationMessage = EditorGUILayout.TextArea(notificationMessage, GUILayout.MinHeight(10 * EditorGUIUtility.singleLineHeight));
        }

        private void GetMyUserId()
        {
            userId = MultiDevice.GetUserId();
            GUI.FocusControl(null);
            Repaint();
        }

        private void FetchData()
        {
            var data = GenerateData("read");
            using var www = CreateRequest(GetServerUrl(), JsonConvert.SerializeObject(data));
            IEnumerator e = CallURL(www);
            notificationMessage = "Fetching data...";
            Repaint();
            while (e.MoveNext()) ;

            if (www.downloadedBytes > 0)
            {
                try
                {
                    var response = JsonConvert.DeserializeObject<UsersBucketResponse>(www.downloadHandler.text);
                    if (response.Data != null)
                    {
                        bucketId = response.Data.BucketId.ToString();
                        currentLeaderboardId = response.Data.LeaderboardId.ToString();
                        notificationMessage = $"Fetch: \n {www.downloadHandler.text}";
                    }
                    else
                    {
                        bucketId = "";
                        currentLeaderboardId = "";
                        notificationMessage = "User does not have a bucket yet";
                    }
                }
                catch (Exception)
                {
                    notificationMessage = $"Error:\n {www.downloadHandler.text}";
                }

                GUI.FocusControl(null);
                Repaint();
            }
        }

        private Dictionary<string, object> GenerateData(string actionType)
        {
            var data = new Dictionary<string, object>()
            {
                { "gameId", BrainCloudGameIds[environment] },
                { "gameSecret", BrainCloudGameSecret[environment] },
                { "serverName", GetServerName() },
                { "service", "script" },
                { "operation", "RUN" },
                {
                    "data",
                    new Dictionary<string, object>()
                    {
                        { "scriptName", "debug/UserBucketDebug" },
                        {
                            "scriptData",
                            new Dictionary<string, object>()
                            {
                                { "actionType", actionType },
                                { "profileId", profileId },
                                { "userId", userId },
                                { "bucketId", bucketId.IsNullOrEmpty() ? string.Empty : int.Parse(bucketId)}
                            }
                        },
                    }
                },
            };
            return data;
        }

        private string GetServerName()
        {
            return environment is BcEnvironment.Test or BcEnvironment.DevEditor ? "TestServer" : "BackOffice";
        }

        private void UpdateData()
        {
            var data = GenerateData("update");
            using var www = CreateRequest(GetServerUrl(), JsonConvert.SerializeObject(data));
            IEnumerator e = CallURL(www);
            notificationMessage = "Updating data...";
            Repaint();
            while (e.MoveNext()) ;
            notificationMessage = $"Bucket updated to:{bucketId}";
            bucketId = "";
            GUI.FocusControl(null);
            Repaint();
        }

        private string GetServerUrl()
        {
            return BrainCloudS2SUrl;
        }

        private void SwitchEnvironment(BcEnvironment newEnvironment)
        {
            // Code to switch environment
            environment = newEnvironment;
            Debug.Log("Switching environment to: " + newEnvironment);
        }

        private UnityWebRequest CreateRequest(string url, string json)
        {
            var request = new UnityWebRequest(url, "POST");
            byte[] bodyRaw = Encoding.UTF8.GetBytes(json);
            request.uploadHandler = string.IsNullOrEmpty(json) ? null : (UploadHandler)new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = (DownloadHandler)new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/json");
            return request;
        }

        private static IEnumerator CallURL(UnityWebRequest www)
        {
            yield return www.SendWebRequest();
            while (!www.isDone)
            {
                yield return true;
            }
        }
    }
}