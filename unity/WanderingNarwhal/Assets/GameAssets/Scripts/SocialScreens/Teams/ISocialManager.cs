using System;
using System.Collections.Generic;
using BBB.Social.Chat;
using GameAssets.Scripts.SocialScreens.Teams.NoTeam.CreateTeam;
using BBB.Chat;
using GameAssets.Scripts.SocialScreens.Teams.TeamInfo;
using RPC.Teams;

namespace GameAssets.Scripts.SocialScreens.Teams
{
    public interface ISocialManager
    {
        event Action CurrentTeamFetched;
        event Action CanAskLivesUpdated;
        List<TeamMemberData> TeamMates { get; }
        TeamData CurrentTeam { get; }
        Dictionary<string, long> CreateTeamPrice { get; }
        public List<TeamPublicInfo> BrowsedTeams { get; }
        void ShowSocialModal(Action onHide = null, bool fromTeamEvent = false);
        void HideSocialModal();
        bool IsSocialUnlocked();
        bool IsTeamMate(string uid);
        void SendHelp(ChatMessage message, Action<bool, string> sendHelpCallback);
        void ClaimIap(ChatMessage message, string productUid, Dictionary<string, int> reward, Action<bool, string> claimIapCallback);
        void AdminPlayer(string teamUid, TeamMemberInfo teamMemberInfo, Action<bool, string> adminPlayerCallback);
        void KickPlayer(string teamUid, TeamMemberInfo teamMemberInfo, bool banUser, Action<bool, string> kickPlayerCallback);
        void BrowseTeams(Action<List<TeamPublicInfo>, bool, string> callback);
        void SearchTeams(string searchQuery, Action<List<TeamPublicInfo>, bool, string> searchTeamsCallback);
        void CreateTeam(TeamCreationData teamCreationData, Action<bool, string> teamCreationCallback);
        void EditTeam(TeamCreationData teamCreationData, Action<bool, string> teamEditingCallback);
        void JoinTeam(string teamUid, Action<bool, string> joinTeamCallback);
        void LeaveTeam(string teamUid, Action<bool, string> leaveTeamCallback);
        void ResignTeam(string teamUid, Action<bool, string> resignCallback);
        void SetForceCanAskForLives(bool forceCanAskForLives);
        bool CanAskForLives();
        double GetRemainingTimeToAskLives();
        void AskForLives<T>(Action<bool, T> askForLivesCallback, string localMessage);
        bool CanNudgeTeamCoop();
        double GetRemainingTimeToNudgeTeamEvent();
        void NudgeTeamCoop(Action<bool, string> nudgeTeamCoopCallback);
        void MarkAllAsRead();
        void FetchTeamData();
        void SendLoginChallenge(string qrCode);
    }
}