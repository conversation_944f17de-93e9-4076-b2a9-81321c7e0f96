using System;
using System.Collections.Generic;
using UnityEngine;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamChat.MessageActions
{
    [CreateAssetMenu(fileName = "ReactionsConfig", menuName = "Teams/ReactionsConfig", order = 1)]
    public class ReactionsConfig : ScriptableObject
    {
        [Serializable]
        public class ReactionConfig
        {
            public string ReactionUid;
            public Sprite ReactionIcon;
        }

        [SerializeField] private List<ReactionConfig> _reactions;

        [HideInInspector]
        public List<ReactionConfig> Reactions;
        public Dictionary<string, ReactionConfig> ReactionsDictionary;

        public void Init()
        {
            for (var i = _reactions.Count - 1; i >= 0; i--)
            {
                if (_reactions[i] == null || _reactions[i].ReactionIcon == null)
                {
                    _reactions.RemoveAt(i);
                }
            }

            Reactions = new List<ReactionConfig>(_reactions);

            ReactionsDictionary = new Dictionary<string, ReactionConfig>();
            foreach (var reaction in Reactions)
            {
                ReactionsDictionary[reaction.ReactionUid] = reaction;
            }
        }
    }
}