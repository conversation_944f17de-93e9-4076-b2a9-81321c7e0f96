using BBB;
using JetBrains.Annotations;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamChat.MessageActions
{
    public class MessageReactionView : BbbMonoBehaviour
    {
        private static readonly int Highlight = Animator.StringToHash("Highlight");

        [SerializeField] private TextMeshProUGUI _scoreText;
        [SerializeField] private Image[] _images;
        [SerializeField] private RuntimeAnimatorController _animatorController;
        [SerializeField] private GameObject _highlightPrefab;
        [SerializeField] private Transform _highlightPrefabRoot;
        private Animator _animator;

        public void Setup(Sprite reactionIcon, int score, bool animate)
        {
            foreach (var image in _images)
            {
                if (image == null)
                    continue;

                image.sprite = reactionIcon;
            }

            _scoreText.text = score.ToString();

            if (animate)
            {
                _highlightPrefabRoot.RemoveAllChildren();
                Instantiate(_highlightPrefab, _highlightPrefabRoot);

                if (_animator == null)
                {
                    _animator = gameObject.AddComponent<Animator>();
                    _animator.runtimeAnimatorController = _animatorController;
                }

                _animator.SetTrigger(Highlight);
            }
        }

        [UsedImplicitly]
        private void HighlightAnimationCompleted()
        {
            ClearHighlight();
        }

        protected override void OnDisable()
        {
            ClearHighlight();
        }

        private void ClearHighlight()
        {
            if (_animator != null)
            {
                Destroy(_animator);
                _animator = null;
            }

            _highlightPrefabRoot.RemoveAllChildren();
        }
    }
}