using System;
using System.Collections.Generic;
using BBB.DI;
using GameAssets.Scripts.SocialScreens.Teams.TeamInfo;
using TMPro;
using UnityEngine;
using UnityEngine.Profiling;
using UnityEngine.UI;

namespace GameAssets.Scripts.SocialScreens.Teams.NoTeam
{
    public class SearchTeamView : NoTeamBaseTabView
    {
        // list of search based quarried teams with ability to see info and join
        [SerializeField] private TMP_InputField _searchInputField;
        [SerializeField] private Button _searchClearButton;
        [SerializeField] private Button _searchButton;

        [SerializeField] private Transform _listRoot;
        [SerializeField] private GameObject _teamPublicInfoViewPrefab;
        [SerializeField] private GameObject _noTeamsPrefab;
        [SerializeField] private GameObject _loadingPrefab;
        
        private Action<TeamPublicInfo> _joinTeamCallback;
        private Action<string, Action, Action<List<TeamPublicInfo>>> _searchQueryCallback;
        private GameObject _loadingGameObject;

        protected override void Awake()
        {
            base.Awake();
            
            _searchClearButton.ReplaceOnClick(SearchClearButtonHandler);
            _searchButton.ReplaceOnClick(SearchButtonHandler);
            _searchInputField.text = string.Empty;
            _listRoot.RemoveAllActiveChilden();
        }

        protected override void InitWithContextInternal(IContext context)
        {
        }

        private void SearchClearButtonHandler()
        {
            _searchInputField.text = string.Empty;
        }

        private void SearchButtonHandler()
        {
            var currentQuery = _searchInputField.text.Trim();
            if (currentQuery.IsNullOrEmpty())
            {
                _searchInputField.ActivateInputField();
                _searchInputField.Select();
            }
            else
            {
                _searchQueryCallback?.Invoke(currentQuery, StartLoading, UpdateTeams);
            }
        }

        private void StartLoading()
        {
            _listRoot.RemoveAllActiveChilden();
            if (_loadingGameObject != null)
            {
                Destroy(_loadingGameObject);
                _loadingGameObject = null;
            }
            
            Profiler.BeginSample($"Instantiate[{_loadingPrefab.name}]");
            _loadingGameObject = Instantiate(_loadingPrefab, _listRoot);
            Profiler.EndSample();
            _loadingGameObject.transform.SetSiblingIndex(0);
        }

        protected override void OnShow()
        {
        }

        protected override void OnHide()
        {
        }

        public void SetupTeams(Action<string, Action, Action<List<TeamPublicInfo>>> searchQueryCallback, Action<TeamPublicInfo> joinTeamCallback)
        {
            _joinTeamCallback = joinTeamCallback;
            _searchQueryCallback = searchQueryCallback;
        }

        private void UpdateTeams(List<TeamPublicInfo> teams)
        {
            _listRoot.RemoveAllActiveChilden();

            foreach (var team in teams)
            {
                Profiler.BeginSample($"Instantiate[{_teamPublicInfoViewPrefab.name}]");
                var go = Instantiate(_teamPublicInfoViewPrefab, _listRoot);
                Profiler.EndSample();
                var view = go.GetComponent<TeamPublicInfoView>();
                view.Setup(team, TeamJoinedHandler);
                go.SetActive(true);
            }

            if (teams.Count == 0)
            {
                Profiler.BeginSample($"Instantiate[{_noTeamsPrefab.name}]");
                Instantiate(_noTeamsPrefab, _listRoot);
                Profiler.EndSample();
            }
        }

        private void TeamJoinedHandler(TeamPublicInfo team)
        {
            _joinTeamCallback?.Invoke(team);
        }
    }
}