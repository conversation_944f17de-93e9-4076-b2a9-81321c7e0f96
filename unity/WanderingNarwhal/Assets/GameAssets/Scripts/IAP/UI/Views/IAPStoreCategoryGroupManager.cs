using System.Collections.Generic;
using BBB;
using UnityEngine;
using UnityEngine.UI;

public class IAPStoreCategoryGroupManager : BbbMonoBehaviour
{
    private readonly Dictionary<string, Button> _categoryButtons = new();

    public void RegisterCategoryButton(string categoryUid, Button button)
    {
        _categoryButtons[categoryUid] = button;
    }

    public void ResetCategoryButtons()
    {
        _categoryButtons.Clear();
    }

    public void CategoryButtonPressed(string categoryUid)
    {
        foreach (var categoryButton in _categoryButtons)
        {
            categoryButton.Value.interactable = !categoryButton.Key.Equals(categoryUid);
        }
    }
}