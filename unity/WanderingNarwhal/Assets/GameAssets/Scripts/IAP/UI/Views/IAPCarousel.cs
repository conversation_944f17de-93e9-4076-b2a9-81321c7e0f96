using GameAssets.Scripts.Promotions.Banners.Pagination;
using UniRx;
using UnityEngine;
using UnityEngine.UI.Extensions;

namespace BBB.UI.IAP.Views
{
    public class IAPCarousel : BbbMonoBehaviour, IPoolItem
    {
        [SerializeField] private RectTransform _rectTransform;

        [SerializeField] private Transform _root;
        [SerializeField] private GameObject _wrappingPrefab;

        [SerializeField] private ScrollRectExtended _scrollRectExtended;
        [SerializeField] private HorizontalScrollSnap _horizontalScrollRect;
        [SerializeField] private IPaginationView _paginationView;
        [SerializeField] private GameObject _paginationPrefab;
        [SerializeField] private float _autoScrollDueTime = 1f;
        [SerializeField] private float _autoScrollDelayTime = 1f;

        private float _currentAutoScrollDelayTime = float.MaxValue;
        private int _childrenCount;
        private bool _isScrolling;
        private RectTransform _constrainedWidthReference;

        private void Start()
        {
            _paginationView.ElementClicked += ToggleButtonClicked;
            _horizontalScrollRect.Snapped += PageChanged;
            _horizontalScrollRect.OnSelectionChangeStartEvent.AddListener(SelectionStarted);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            if (_paginationView != null)
                _paginationView.ElementClicked -= ToggleButtonClicked;

            if (_horizontalScrollRect != null)
            {
                _horizontalScrollRect.Snapped -= PageChanged;
                _horizontalScrollRect.OnSelectionChangeStartEvent.RemoveListener(SelectionStarted);
            }
        }

        public void Setup(RectTransform fullWidthReference, RectTransform constrainedWidthReference)
        {
            _horizontalScrollRect.Awake();
            var totalChildCount = _root.childCount;
            // can't simply remove all because horizontal scroll relies on child count which will be updated only next frame
            for (var i = 0; i < totalChildCount; i++)
            {
                var child = _root.GetChild(0);
                child.gameObject.SetActive(false);
                child.SetParent(_rectTransform);
                Destroy(child.gameObject);
            }

            var sizeDelta = _rectTransform.sizeDelta;
            sizeDelta.x = fullWidthReference.rect.width;
            _rectTransform.sizeDelta = sizeDelta;

            _constrainedWidthReference = constrainedWidthReference;
            _scrollRectExtended.InitParent();
        }

        public void AddChild(GameObject childGameObject)
        {
            var wrapperGo = Instantiate(_wrappingPrefab, _root);
            childGameObject.transform.SetParent(wrapperGo.transform);
            var childRectTransform = childGameObject.GetComponent<RectTransform>();
            childRectTransform.anchorMin = new Vector2(0.5f, 0.5f);
            childRectTransform.anchorMax = new Vector2(0.5f, 0.5f);
            var childSizeDelta = childRectTransform.sizeDelta;
            childSizeDelta.x = _constrainedWidthReference.rect.width;
            childRectTransform.sizeDelta = childSizeDelta;
            childRectTransform.anchoredPosition = Vector2.zero;
            _childrenCount++;
        }

        public void FinishSetup()
        {
            _paginationView.Setup(_childrenCount, _paginationPrefab);

            _horizontalScrollRect.UpdateLayout();
            PageChanged(0);
            _currentAutoScrollDelayTime = _autoScrollDueTime;
        }

        private void Update()
        {
            if (_isScrolling)
            {
                return;
            }

            _currentAutoScrollDelayTime -= Time.deltaTime;
            if (_currentAutoScrollDelayTime <= 0)
            {
                StartAutoScroll();
            }
        }

        private void PageChanged(int pageIndex)
        {
            _isScrolling = false;
            _currentAutoScrollDelayTime = _autoScrollDelayTime;
            _paginationView.SetActiveIndex(pageIndex);
        }

        private void SelectionStarted()
        {
            _isScrolling = true;
        }

        private void ToggleButtonClicked(int index)
        {
            if (_isScrolling)
                return;

            _horizontalScrollRect.GoToScreenImmediately(index);
            _paginationView.SetActiveIndex(index);
            PageChanged(index);
        }

        private void StartAutoScroll()
        {
            if (_horizontalScrollRect.CurrentPage < _childrenCount - 1)
                _horizontalScrollRect.NextScreen();
            else
                _horizontalScrollRect.GoToScreen(0);

            _paginationView.SetActiveIndex(_horizontalScrollRect.CurrentPage);
        }

        public void OnInstantiate()
        {
        }

        public void OnSpawn()
        {
        }

        public void OnRelease()
        {
            _childrenCount = 0;
            _paginationView.Release();

            // can't delete children here because some of them are pooled so we can safely reset only on setup
        }
    }
}