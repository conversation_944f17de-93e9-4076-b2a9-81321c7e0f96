using System;
using BBB.Core;

namespace GameAssets.Scripts.IAP.Baskets
{
    public class BasketController : BaseModalsController<IBasketViewPresenter>
    {
        private Action _iapBuyCallback;
        private Action _closeCallback;
        private Tuple<string, int> _specialReward;
        private IAPBasket _basket;

        private bool _reward;
        private string _price;

        public void SetupInfo(IAPBasket basket, string price, Action iapBuyCallback, Action closeCallback = null)
        {
            _basket = basket;
            _price = price;
            _iapBuyCallback = iapBuyCallback;
            _closeCallback = closeCallback;
            _reward = false;
        }

        public void SetupReward(IAPBasket basket, Tuple<string, int> specialReward = null, Action closeCallback = null)
        {
            _basket = basket;
            _specialReward = specialReward;
            _closeCallback = closeCallback;
            _reward = true;
        }

        protected override void OnShow()
        {
            base.OnShow();

            if (_reward)
                View.SetupReward(_basket, _specialReward);
            else
                View.SetupInfo(_basket, _price, _iapBuyCallback);
        }

        protected override void OnHide()
        {
            base.OnHide();
            _basket = null;
            _iapBuyCallback = null;
            _closeCallback?.Invoke();
            _closeCallback = null;
            _specialReward = null;
        }
    }
}