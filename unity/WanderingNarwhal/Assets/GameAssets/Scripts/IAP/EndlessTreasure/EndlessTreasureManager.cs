using System;
using System.Collections.Generic;
using BBB;
using BBB.DI;
using BebopBee.Core.UI;
using Core.Configs;
using FBConfig;
using JetBrains.Annotations;
using UnityEngine;
using IapManager = BBB.IAP.IapManager;

namespace GameAssets.Scripts.IAP.EndlessTreasure
{
    public class EndlessTreasureManager : IContextInitializable, IContextReleasable
    {
        private static readonly Type[] RequiredConfigs =
        {
            typeof(EndlessTreasureConfig)
        };

        private readonly Dictionary<string, List<EndlessTreasure>> _endlessTreasures = new();

        private IapManager _iapManager;
        private IModalsBuilder _modalsBuilder;
        private IPlayerManager _playerManager;
        private IPlayer Player => _playerManager.Player;

        private bool _isInitialized;

        public void InitializeByContext(IContext context)
        {
            var config = context.Resolve<IConfig>();
            _iapManager = context.Resolve<IapManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _playerManager = context.Resolve<IPlayerManager>();
            _isInitialized = false;

            Init(config);
            Config.OnConfigUpdated -= Init;
            Config.OnConfigUpdated += Init;
        }

        [UsedImplicitly]
        public bool CanBeShown(string endlessTreasureUid)
        {
            if (!_isInitialized || !_endlessTreasures.ContainsKey(endlessTreasureUid))
                return false;

            return !IsCompleted(endlessTreasureUid);
        }

        [UsedImplicitly]
        public void Reset(string endlessTreasureUid)
        {
            if (Player.EndlessTreasureProgress.ContainsKey(endlessTreasureUid))
                Player.EndlessTreasureProgress.Remove(endlessTreasureUid);
        }

        [UsedImplicitly]
        public bool IsCompleted(string endlessTreasureUid)
        {
            var lastClaimed = GetLastClaimed(endlessTreasureUid);
            if (lastClaimed.IsNullOrEmpty())
                return false;

            var index = _endlessTreasures[endlessTreasureUid].IndexOf(x => x.Uid == lastClaimed);
            if (index < 0)
            {
                Debug.LogError($"Couldn't find {lastClaimed} in endless treasure list of {endlessTreasureUid}, uids should not be removed");
                return false;
            }

            return index == _endlessTreasures[endlessTreasureUid].Count - 1;
        }

        public void ShowModal(string endlessTreasureUid)
        {
            if (_endlessTreasures.TryGetValue(endlessTreasureUid, out var endlessTreasure))
            {
                var endlessTreasureController = _modalsBuilder.CreateModalView<EndlessTreasureController>(ModalsType.EndlessTreasure);
                endlessTreasureController.Setup(endlessTreasureUid, endlessTreasure);
                endlessTreasureController.ShowModal(ShowMode.Delayed);
            }
            else
            {
                Debug.LogError($"Couldn't find treasure id of {endlessTreasureUid} in config");
            }
        }

        public void ReleaseByContext(IContext context)
        {
        }

        public void RegisterAsPurchased(EndlessTreasure endlessTreasure)
        {
            Player.EndlessTreasureProgress[endlessTreasure.EndlessTreasureUid] = endlessTreasure.Uid;
        }

        public string GetLastClaimed(string endlessTreasureUid)
        {
            return Player.EndlessTreasureProgress.TryGetValue(endlessTreasureUid, out var lastClaimed) ? lastClaimed : string.Empty;
        }

        public EndlessTreasure GetCurrentEndlessTreasureItem(string endlessTreasureUid)
        {
            if (!_endlessTreasures.ContainsKey(endlessTreasureUid))
            {
                Debug.LogError($"Couldn't find endless treasure with uid{endlessTreasureUid}");
                return null;
            }

            if (IsCompleted(endlessTreasureUid))
                return null;

            var lastClaimed = GetLastClaimed(endlessTreasureUid);
            var endlessTreasure = _endlessTreasures[endlessTreasureUid];
            var indexOfLastClaimed = endlessTreasure.IndexOf(x => x.Uid == lastClaimed);
            var currentItemIndex = indexOfLastClaimed + 1;
            return endlessTreasure[currentItemIndex];
        }

        private void Init(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            var endlessTreasureConfigs = config.Get<EndlessTreasureConfig>();
            if (endlessTreasureConfigs == null)
            {
                Debug.LogError("Endless treasure config is missing");
                return;
            }

            _endlessTreasures.Clear();

            foreach (var endlessTreasureConfig in endlessTreasureConfigs.Values)
            {
                var list = new List<EndlessTreasure>();
                for (var i = 0; i < endlessTreasureConfig.TreasuresListLength; i++)
                {
                    var endlessTreasureConfigItem = endlessTreasureConfig.TreasuresList(i);

                    if (!endlessTreasureConfigItem.HasValue)
                        continue;
                    
                    var endlessTreasure = new EndlessTreasure(endlessTreasureConfig.Uid, endlessTreasureConfigItem.Value, _iapManager);
                    list.Add(endlessTreasure);
                }

                list.Sort((x, y) => x.SortOrder.CompareTo(y.SortOrder));
                _endlessTreasures[endlessTreasureConfig.Uid] = list;
            }

            _isInitialized = true;
        }
    }
}