using System;
using PBGame;

namespace BBB.Testers.Mocks
{
    public class MockLocationManager : ILocationManager
    {
        public event Action<LevelState, bool, bool> LevelPassed = delegate { };

        public event Action LocationsDataUpdated;
        public ILocation MainProgressionLocation => null;

        public void OnLevelPassed(string levelUid, bool nextStageReached, int grindReplaysNumber, bool isFirstTry)
        {
        }

        public int GetLevelStage(string levelUid)
        {
            return 1;
        }

        public LevelState GetLevelState(string levelUid)
        {
            return null;
        }
    }
}