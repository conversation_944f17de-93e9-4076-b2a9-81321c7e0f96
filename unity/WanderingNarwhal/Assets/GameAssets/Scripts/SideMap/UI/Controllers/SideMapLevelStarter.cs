using BBB.Core;
using BBB.DI;
using BBB.EndGameEvents;
using BBB.GameAssets.Scripts.Player;
using BebopBee;
using GameAssets.Scripts.Map.UI.Controllers;
using PBGame;

namespace BBB.UI.UI.Controllers
{
    public class SideMapLevelStarter : LevelStarter
    {
        private IGameEventManager _gameEventManager;

        public override void InitializeByContext(IContext context)
        {
            base.InitializeByContext(context);

            _gameEventManager = context.Resolve<IGameEventManager>();
        }

        protected override LevelRemoteData GetLevelRemoteData(string levelUid)
        {
            var levelConfigs = Config.Get<FBConfig.ProgressionLevelConfig>();

            if (_gameEventManager.GetCurrentSideMapEvent() is not SideMapGameEvent)
            {
                BDebug.LogError(LogCat.General, $"Game event for side map level {levelUid} not found");
                return null;
            }

            if (!levelConfigs.TryGetValue(levelUid, out var levelConfig))
            {
                BDebug.LogError(LogCat.General, $"Level config {levelUid} not found");
                return null;
            }

            var levelData = new LevelRemoteData(levelConfig, new LevelState { Stage = 0 }, true);
            return levelData;
        }

        public override void StartLevel(string levelUid)
        {
            if (_gameEventManager.GetCurrentSideMapEvent() is not SideMapGameEvent gameEvent)
            {
                BDebug.LogError(LogCat.General, $"Game event for side map level {levelUid} not found");
                return;
            }

            var screenType = gameEvent.LevelScreen;
            var levelData = _currentLevelRemoteData;
            CommandBase transitionCommand = new LoadLevelAsyncCommand(levelData);
            LoadingProcessTracker.LogShowScreen(screenType.ToString(), ScreensManager.GetTrackingPreviousScreenType(), "SideMapStartLevel");
            ScreensBuilder.ShowScreen(screenType, transitionCommand);
            if (BoosterManager.StartedLevelWithBoosters(PlayerManager.PlayerInventory.EquippedAutoBoosters))
            {
                BoosterManager.GameStarted = true;
            }
        }
    }
}