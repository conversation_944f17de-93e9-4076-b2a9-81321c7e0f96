using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.Core.Wallet;
using BBB.DI;
using BBB.MMVibrations;
using BBB.UI;
using BBB.Wallet;
using BebopBee.Core;
using BebopBee.Core.UI;
using Core.Configs;
using DG.Tweening;
using FBConfig;
using GameAssets.Scripts.Core.TimeManager;
using GameAssets.Scripts.GenericModals.Tutorial;
using GameAssets.Scripts.GenericModals.Tutorial.Core;
using GameAssets.Scripts.UI.OverlayDialog;
using Unity.Mathematics;
using UnityEngine;

namespace BBB.DailyLogin
{
    public class DailyLoginManager : IContextInitializable, IContextReleasable
    {
        private const string DailyLoginDurationOverrideKey = "DailyLoginDurationOverride";
        private const string DailyLoginPeriodOffsetKey = "DailyLoginPeriodOffset";

        private const int DefaultPeriodSeconds = 24 * 60 * 60;

        public event Action DayChanged;
        public event Action GameEventEnded;
        public event Action CurrentDayClaimed;

        private static readonly Type[] RequiredConfigs =
        {
            typeof(SweepstakesDailyLoginConfig)
        };

        private IDictionary<string, SweepstakesDailyLoginConfig> _dailyLoginConfig;

        private SortedDictionary<int, Dictionary<string, int>> _rewardsByDay = new();
        private readonly Dictionary<string, long> _rewardDictionaryCached = new();

        private IModalsBuilder _modalsBuilder;
        private IEventDispatcher _eventDispatcher;
        private IPlayerManager _playerManager;
        private IScreensBuilder _screensBuilder;
        private IScreensManager _screensManager;
        private INotifier _dailyLoginNotifier;
        private GameNotificationManager _notificationManager;
        private IGameEventManager _gameEventManager;
        private IWalletTransactionController _walletTransactionController;
        private IUIWalletManager _uiWalletManager;
        private TimeManager _timeManager;
        private IOverlayDialogManager _overlayDialogManager;
        private IVibrationsWrapper _vibrationsWrapper;
        
        private readonly OverlayDialogConfig _overlayDialogConfig = new();

        private Tweener _dayChangeTweener;
        private bool _autoShowed;
        private int _periodOffset; // Used to skip days for debug

        public int PeriodSeconds { get; private set; } = DefaultPeriodSeconds;
        public SweepstakesGameEvent GameEvent { get; private set; }

        public List<int> ClaimedDays => Player.PlayerDO.DailyLoginClaimedDays;

        public int CurrentDay => GameEvent == null || GameEvent.GetTimeLeft().TotalSeconds <= 0 ? -1 : 1 + (int)TimePassed / PeriodSeconds + _periodOffset;

        private double StartTime
        {
            get => Player.PlayerDO.DailyLoginStartTime;
            set => Player.PlayerDO.DailyLoginStartTime = value;
        }

        private double LastClaimTime
        {
            get => Player.PlayerDO.DailyLoginLastClaimTime;
            set => Player.PlayerDO.DailyLoginLastClaimTime = value;
        }

        private IPlayer Player => _playerManager.Player;

        private double TimePassed => _timeManager.CurrentTimeStamp() - StartTime;

        public void InitializeByContext(IContext context)
        {
            var config = context.Resolve<IConfig>();
            SetupConfigs(config);

            _playerManager = context.Resolve<IPlayerManager>();
            _modalsBuilder = context.Resolve<IModalsBuilder>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _screensBuilder = context.Resolve<IScreensBuilder>();
            _screensManager = context.Resolve<IScreensManager>();
            _notificationManager = context.Resolve<GameNotificationManager>();
            _dailyLoginNotifier = _notificationManager.GetDailyLoginNotifier();
            _gameEventManager = context.Resolve<IGameEventManager>();
            _walletTransactionController = context.Resolve<IWalletManager>().TransactionController;
            _uiWalletManager = context.Resolve<IUIWalletManager>();
            _timeManager = context.Resolve<TimeManager>();
            _overlayDialogManager = context.Resolve<IOverlayDialogManager>();
            _vibrationsWrapper = context.Resolve<IVibrationsWrapper>();

            Subscribe();

#if BBB_DEBUG
            var overridePeriodDuration = PlayerPrefs.GetInt(DailyLoginDurationOverrideKey);
            if (overridePeriodDuration > 0)
            {
                PeriodSeconds = overridePeriodDuration;
            }

            _periodOffset = PlayerPrefs.GetInt(DailyLoginPeriodOffsetKey, 0);
#endif
        }

        private void SetupConfigs(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            _dailyLoginConfig = config.Get<SweepstakesDailyLoginConfig>();

            _rewardsByDay.Clear();
            foreach (var dailyLoginConfig in _dailyLoginConfig.Values)
            {
                if (_rewardsByDay.ContainsKey(dailyLoginConfig.Day)) continue;
                _rewardsByDay.Add(dailyLoginConfig.Day, new Dictionary<string, int>(dailyLoginConfig.RewardLength));
                for (var i = 0; i < dailyLoginConfig.RewardLength; i++)
                {
                    var reward = dailyLoginConfig.Reward(i);
                    if (!reward.HasValue) continue;

                    _rewardsByDay[dailyLoginConfig.Day].Add(reward.Value.Key, reward.Value.Value);
                }
            }
        }

        private void Unsubscribe()
        {
            Config.OnConfigUpdated -= SetupConfigs;
            if (_screensManager != null)
            {
                _screensManager.OnScreenChangingStarted -= ScreenChangedHandler;
            }
        }

        private void Subscribe()
        {
            Unsubscribe();
            Config.OnConfigUpdated += SetupConfigs;
            _screensManager.OnScreenChangingStarted += ScreenChangedHandler;
        }

        private void ScreenChangedHandler(ScreenType arg1, IScreensController arg2)
        {
            _autoShowed = false;
        }

        private void UpdateNotifications()
        {
            _dailyLoginNotifier ??= _notificationManager.GetDailyLoginNotifier();
            if (_dailyLoginNotifier == null) return;

            _dailyLoginNotifier.ResetNotifier();
            if (ItemAvailableToClaim())
            {
                _dailyLoginNotifier.SetNotifier(1, 1);
            }
        }

        public void ReleaseByContext(IContext context)
        {
            ResetDayChangeTweener();
            Unsubscribe();
            _overlayDialogManager = null;
        }

        private bool ClaimRewards(int currentDay)
        {
            if (!_rewardsByDay.TryGetValue(currentDay, out var rewards))
            {
                BDebug.LogError(LogCat.Config, $"Reward for day {currentDay} is missing in config");
                return false;
            }

            if (!RewardsUtility.IsRewardValid(rewards))
            {
                if (!rewards.IsNullOrEmpty())
                {
                    BDebug.LogError(LogCat.Config, $"Reward for day {currentDay} is invalid: {rewards}");
                }

                return false;
            }

            _rewardDictionaryCached.Clear();
            foreach (var kv in rewards)
            {
                _rewardDictionaryCached[kv.Key] = kv.Value;
            }

            var transaction = new Transaction()
                .AddTag(TransactionTag.DailyLogin)
                .SetAnalyticsData(CurrencyFlow.DailyLogin.Name,
                    CurrencyFlow.DailyLogin.Day, currentDay.ToString())
                .Earn(_rewardDictionaryCached);
            _walletTransactionController.MakeTransaction(transaction);

            _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.DailyLogin);
            _vibrationsWrapper.PlayHaptic(MMVibrations.Plugins.ImpactPreset.MediumImpact);
            return true;
        }

        public bool ItemAvailableToClaim()
        {
            var currentDay = CurrentDay;
            return currentDay > 0 && (ClaimedDays.Count == 0 || currentDay > ClaimedDays[^1]);
        }

        public bool ShouldAutoShowDailyLoginModal()
        {
            return !_autoShowed && _timeManager.HasLastServerTimeStamp && ShouldShowHud() && (!GameEvent.ShouldStartAnnouncementBeAutoShown || GameEvent.CurrentScore > 0);
        }

        public bool ShouldShowHud()
        {
            UpdateGameEvent();
            return GameEvent != null && GameEvent.GetTimeLeft().TotalSeconds > 0 && IsAvailableViaCurrentScreen() && ItemAvailableToClaim();
        }

        private bool IsEndOfContent()
        {
            return (_screensBuilder.CurrentScreenType & ScreenType.SideMap) > 0 &&
                   _gameEventManager.GetHighestPriorityEvent(ev =>
                       ev.GameplayType == GameEventGameplayType.EndOfContent && ev.Status == GameEventStatus.Active) != null;
        }

        private bool IsAvailableViaCurrentScreen()
        {
            return (_screensBuilder.CurrentScreenType & ScreenType.SideMap) <= 0 || IsEndOfContent();
        }

        public void AutoShowDailyLoginModal()
        {
            TryShowDailyLoginModal();
            _autoShowed = true;
        }

        private void ResetDailyLogin(double startTime)
        {
            StartTime = startTime;
            ClaimedDays.Clear();
            LastClaimTime = 0;
            ResetDayChangeTweener();
            _playerManager.MarkDirty();

#if BBB_DEBUG
            _periodOffset = 0;
            PeriodSeconds = DefaultPeriodSeconds;
            PlayerPrefs.DeleteKey(DailyLoginPeriodOffsetKey);
            PlayerPrefs.DeleteKey(DailyLoginDurationOverrideKey);
            PlayerPrefs.Save();
#endif
        }

        public void UpdateGameEvent()
        {
            GameEvent = _gameEventManager.GetHighestPriorityEvent(ev => ev.Status == GameEventStatus.Active && ev.GameplayType == GameEventGameplayType.Sweepstakes) as SweepstakesGameEvent;
            if (GameEvent == null)
            {
                ResetDayChangeTweener();
                return;
            }

            var eventStartTime = GameEvent.GetLastStartTime().ToUnixTimeSeconds();
            if (eventStartTime - StartTime > 1)
            {
                ResetDailyLogin(eventStartTime);
            }

            UpdateNotifications();

            var timeLeft = GameEvent.GetTimeLeft().TotalSeconds;
            if (timeLeft <= 0)
            {
                ResetDayChangeTweener();
                return;
            }

            if (_dayChangeTweener != null) return;
            _dayChangeTweener = Rx.Invoke(GetRemainingTimeForCurrentPeriod(timeLeft) + 1, x =>
            {
                ResetDayChangeTweener();
                DayChanged.SafeInvoke();
                UpdateGameEvent();
            });
        }

        private void ResetDayChangeTweener()
        {
            _dayChangeTweener?.Kill();
            _dayChangeTweener = null;
        }

        private bool ClaimCurrentDay()
        {
            var currentDay = CurrentDay;
            ClaimedDays.Add(currentDay);
            LastClaimTime = _timeManager.CurrentTimeStamp();
            UpdateNotifications();
            var claimed = ClaimRewards(currentDay);
            UpdateGameEvent();
            _playerManager.MarkDirty();
            CurrentDayClaimed.SafeInvoke();
            return claimed;
        }

        public void TryShowDailyLoginModal(Transform floatingTextAnchor = null, ShowMode showMode = ShowMode.Delayed)
        {
            if (!_timeManager.HasLastServerTimeStamp)
            {
                _overlayDialogConfig.DisplayType = DisplayType.FloatingText;
                _overlayDialogConfig.TargetTransform = floatingTextAnchor;
                _overlayDialogConfig.ShowBackground = true;
                _overlayDialogConfig.TextToDisplay = LocalizationManagerHelper.OfflineConnectionProblemKey;
                _overlayDialogManager.ShowOverlayDialog(_overlayDialogConfig);
                return;
            }

            UpdateGameEvent();
            var currentDay = CurrentDay;
            if (currentDay <= 0)
            {
                GameEventEnded.SafeInvoke();
                return;
            }

            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.AutoPopups.Name, DauInteractions.AutoPopups.SweepstakesDailyCollect, $"{CurrencyFlow.DailyLogin.Day}_{currentDay}"));
            DauInteractions.TapOnAutoPopups.AwaitLogs(DauInteractions.TapOnAutoPopups.Name, DauInteractions.TapOnAutoPopups.GameEventClose);

            var modalController = _modalsBuilder.CreateModalView<DailyLoginModalController>(ModalsType.DailyLoginModal);
            modalController.Setup(_rewardsByDay, GameEvent.GetTimeLeft, OnDailyLoginModalClosed);
            modalController.ShowModal(showMode);
        }

        private void OnDailyLoginModalClosed(bool playButtonClicked)
        {
            if (TryClaimCurrentDay() || !playButtonClicked) return;

            GoToNextGameEventLevel();
        }

        public bool TryClaimCurrentDay()
        {
            UpdateGameEvent();
            if (!ItemAvailableToClaim()) return false;

            var claimed = ClaimCurrentDay();
            if (GameEvent == null)
            {
                GameEventEnded.SafeInvoke();
            }

            return claimed;
        }

        public void ShowInfoModal(Action hideCallback = null)
        {
            if (GameEvent == null)
                return;

            var model = new CompetitionEventTutorialModel(GameEvent);
            model.SetupHideCallback(hideCallback);
            _modalsBuilder.CreateModalView<GenericTutorialModalController>(ModalsType.GenericTutorialModal).SetupAndShow(model, ShowMode.Immediate);
        }

        private void GoToNextGameEventLevel()
        {
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<EventFlowRequestedEvent>());
        }

        public int GetRemainingTimeToClaim()
        {
            if (GameEvent == null)
                return 0;

            var timeLeft = GameEvent.GetTimeLeft();
            if (timeLeft.TotalSeconds <= 0)
                return 0;

            if (ItemAvailableToClaim()) return -1;
            return GetRemainingTimeForCurrentPeriod(timeLeft.TotalSeconds);
        }

        private int GetRemainingTimeForCurrentPeriod(double maxTime)
        {
            var remainingTime = PeriodSeconds - TimePassed % PeriodSeconds;
            return (int)math.min(maxTime, remainingTime);
        }

        public void Restart()
        {
            ResetDayChangeTweener();
            Unsubscribe();
        }

        public void DebugSkipPeriods(int periodsCount)
        {
            if (periodsCount <= 0) return;
            _periodOffset += periodsCount;
            PlayerPrefs.SetInt(DailyLoginPeriodOffsetKey, _periodOffset);
            PlayerPrefs.Save();
        }

        public void DebugSetPeriodDuration(int seconds)
        {
            if (seconds <= 0) return;
            PeriodSeconds = seconds;
            PlayerPrefs.SetInt(DailyLoginDurationOverrideKey, seconds);
            PlayerPrefs.Save();
            ResetDayChangeTweener();
            UpdateGameEvent();
        }

        public void DebugStartOver()
        {
            ResetDailyLogin(_timeManager.CurrentTimeStamp());
        }
    }
}