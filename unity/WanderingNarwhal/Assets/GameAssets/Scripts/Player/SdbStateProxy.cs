using PBGame;
using UnityEngine;

namespace BBB
{
    public class SdbStateProxy
    {
        private readonly PBPlayer _playerDo;
        
        public int CurrentWinCount => _playerDo.SdbState.CurrentWinCount;
        public bool IntroductionAlreadyShown => _playerDo.SdbState.IntroductionAlreadyShown;
        public int LastShownScore => _playerDo.SdbState.LastShownScore;
        private bool SdbEnabled => _playerDo.SdbState.SdbEnabled;

        public SdbStateProxy(PBPlayer playerDo)
        {
            _playerDo = playerDo;
        }

        public bool IncreaseCurrentWinCount(int cachedValue, int maxCount)
        {
            var count = cachedValue + 1;
            if (count >= maxCount)
            {
                _playerDo.SdbState.SdbEnabled = true;
            }
            _playerDo.SdbState.CurrentWinCount = Mathf.Min(count, maxCount);
            return count == maxCount;//Return true only when reaching the max count. Further wins should be ignored
        }
        
        public void ResetState()
        {
            _playerDo.SdbState.CurrentWinCount = 0;
            _playerDo.SdbState.SdbEnabled = false;
        }

        public bool TryMarkIntroShown()
        {
            var wasShown = _playerDo.SdbState.IntroductionAlreadyShown;
            _playerDo.SdbState.IntroductionAlreadyShown = true;
            return !wasShown;
        }
        
        public void UpdateLastShownScore()
        {
            SetLastShownScore(CurrentWinCount);
        }
        
        public void SetLastShownScore(int score)
        {
            _playerDo.SdbState.LastShownScore = score;
        }

        public bool ValidateState(int maxCount)
        {
            var inProgress = CurrentWinCount < maxCount && !SdbEnabled;
            var completed = CurrentWinCount == maxCount && SdbEnabled;
            var lastSeenValid = LastShownScore <= maxCount;
            if ((inProgress || completed) && lastSeenValid) return false;

            _playerDo.SdbState.CurrentWinCount = SdbEnabled ? maxCount : Mathf.Min(CurrentWinCount, maxCount);
            UpdateLastShownScore(); // If something was wrong, updating LastShownScore to CurrentWinCount
            _playerDo.SdbState.SdbEnabled = CurrentWinCount == maxCount;
            return true;
        }
    }
}