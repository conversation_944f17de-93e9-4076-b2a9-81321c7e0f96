using BBB;
using BBB.DI;
using BBB.UI;

namespace GameAssets.Scripts.DailyTask
{
    public abstract class BaseTileCollectedTaskProgressTracker : TaskProgressTracker
    {
        protected IEventDispatcher _eventDispatcher;

        public override void Init(IContext context)
        {
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            
            _eventDispatcher.RemoveListener<TileCollectedEvent>(TileCollectedEventHandler);
            _eventDispatcher.AddListener<TileCollectedEvent>(TileCollectedEventHandler);
        }

        public override void DeInit()
        {
            base.DeInit();
            _eventDispatcher.RemoveListener<TileCollectedEvent>(TileCollectedEventHandler);
        }

        public override void OnFlowRequested()
        {
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<LevelFlowRequestedEvent>());
        }

        protected abstract void TileCollectedEventHandler(TileCollectedEvent ev);
    }
}