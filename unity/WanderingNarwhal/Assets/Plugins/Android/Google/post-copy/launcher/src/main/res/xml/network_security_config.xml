<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <base-config>
        <trust-anchors>
            <certificates src="system" />
            <certificates src="user" />
        </trust-anchors>
    </base-config>

    <!-- Start region for HTTP support for servers without HTTPS for API 28 devices -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">cdn-games.bigfishgames.com</domain>
        <domain includeSubdomains="true">cdn-f2p.bigfishgames.com</domain>
        <domain includeSubdomains="true">cdn-games.bigfishsites.com</domain>
        <domain includeSubdomains="true">bigfishgames-a.akamaihd.net</domain>
    </domain-config>
    <!-- End region -->
</network-security-config>
