using BBB;
using FBConfig;
using UnityEngine;
using VipProducts.UI;

namespace VipProducts
{
    public delegate void VipProductsOrderDelegate(Product p);
    
    public class VipProductsMonoBehaviour : BbbMonoBehaviour
    {
        private VipProductsSettings _settings;
        
        /// <summary>
        /// Functions subscribed to this delegate are called whenever a successful order is done. Do note that
        /// at the moment this is supported by Big Screen, Android with UGUI and iOS (only with Apple Pay).
        /// </summary>
        public VipProductsOrderDelegate VipProductsOrderConfirmed;

        private GameObject _currentPrefab;
        
        public VipProductsUI UI { get; private set; }
        
        internal void Init(VipProductsSettings settings)
        {
            _settings = settings;
            DontDestroyOnLoad(gameObject);
            CreateUIPrefab();
        }
        
        private void CreateUIPrefab()
        {
            if (_currentPrefab != null) return; //Don't create the UI twice, accidentally

            _currentPrefab = Instantiate(_settings.uiPrefab, null, true);
            DontDestroyOnLoad(_currentPrefab);
            UI = _currentPrefab.GetComponent<VipProductsUI>();
        }
        
        /// <summary>
        /// Open a product view for a given <see cref="Product"/> <paramref name="p"/>. 
        /// This requires a <see cref="Product"/> that has been correctly created.
        /// </summary>
        /// <param name="p">Product to show</param>
        private void ShowProduct(Product p, string icon)
        {
            UI.SetLoadingIndicator(true);
            UI.ProductPage.Init(p, icon);
        }

        /// <summary>
        /// Asynchronously loads and shows a <see cref="Product"/> for a given <paramref name="tag"/>. 
        /// This will immediately show a loading screen, unless disabled.
        /// </summary>
        /// <param name="productTag">Product to show</param>
        /// <param name="locked">Whether to display this offer as locked and disallow ordering</param>
        public void ShowProductForTag(VIPProductsConfg productsConfig, string productTag)
        {
            var p = Product.CreateFromDto(productsConfig, productTag);
            ShowProduct(p, p.Image);
        }
    }
}