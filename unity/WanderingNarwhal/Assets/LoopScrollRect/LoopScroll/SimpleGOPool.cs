using System;
using System.Collections.Generic;
using UnityEngine;

namespace BBB.UI
{
    [DisallowMultipleComponent]
    public class SimpleGOPool : MonoBehaviour
    {
        public static SimpleGOPool Instance
        {
            get
            {
                if (_instance == null)
                {
                    var go = new GameObject("SimpleGOPool", typeof(SimpleGOPool));
                    _instance = go.GetComponent<SimpleGOPool>();
                }

                return _instance;
            }
        }

        private static SimpleGOPool _instance;
        private Dictionary<GameObject, List<PooledItem>> _pool = new Dictionary<GameObject, List<PooledItem>>();
        private Dictionary<GameObject, Transform> _poolSubObjects = new Dictionary<GameObject, Transform>();

        public void PreWarmPool(GameObject prefab, int count)
        {
            if (count <= 0) return;

            List<PooledItem> pool;
            if (_pool.ContainsKey(prefab))
            {
                pool = _pool[prefab];
            }
            else
            {
                pool = CreatePool(prefab);
            }

            int targetCount = count - pool.Count;
            for (int i = 0; i < targetCount; i++)
            {
                CreateNewItemInPool(pool, prefab, isWillRemainPooled: true);
            }
        }

        public GameObject GetObjectFromPool(GameObject prefab)
        {
            List<PooledItem> pool;
            if (_pool.ContainsKey(prefab))
            {
                pool = _pool[prefab];
            }
            else
            {
                pool = CreatePool(prefab);
            }

            var pooledItem = GetNextAvailableObject(pool, prefab);
            return pooledItem.gameObject;
        }

        private List<PooledItem> CreatePool(GameObject prefab)
        {
            var newPool = new List<PooledItem>();
            _pool[prefab] = newPool;
            var subRoot = new GameObject("[Pool]" + prefab.name).transform;
            subRoot.SetParent(transform);
            _poolSubObjects[prefab] = subRoot;
            return newPool;
        }

        public void ReturnObjectToPool(GameObject go)
        {
            var pooledItem = go.GetComponent<PooledItem>();
            if (pooledItem == null) return;

            foreach (var kv in _pool)
            {
                var pool = kv.Value;
                if (pool.Contains(pooledItem))
                {
                    var prefab = kv.Key;
                    pooledItem.isPooled = true;
                    pooledItem.gameObject.SetActive(false);
                    pooledItem.transform.SetParent(_poolSubObjects[prefab]);
                    pooledItem.OnRelesed();
                }
            }
        }

        private GameObject GetNextAvailableObject(List<PooledItem> pool, GameObject prefab)
        {
            PooledItem result = null;
            bool isAnyNull = false;
            foreach (var p in pool)
            {
                if (p == null)
                {
                    isAnyNull = true;
                    continue;
                }

                if (p.isPooled)
                {
                    result = p;
                    break;
                }
            }

            if (isAnyNull)
            {
                pool.RemoveAll(item => item == null);
            }

            if (result == null)
            {
                result = CreateNewItemInPool(pool, prefab);
            }

            if (result != null)
            {
                result.isPooled = false;
                result.OnSpawned();
                return result.gameObject;
            }

            return null;
        }

        private PooledItem CreateNewItemInPool(List<PooledItem> pool, GameObject prefab, bool isWillRemainPooled = false)
        {
            PooledItem result;
            if (isWillRemainPooled)
            {
                var go = Instantiate(prefab, parent: _poolSubObjects[prefab]);
                result = go.GetComponent<PooledItem>();
                if (result == null)
                {
                    result = go.AddComponent<PooledItem>();
                }
                result.isPooled = true;
                result.OnRelesed();
                result.gameObject.SetActive(false);
            }
            else
            {
                var go = Instantiate(prefab);
                result = go.GetComponent<PooledItem>();
                if (result == null)
                {
                    result = go.AddComponent<PooledItem>();
                }
                result.isPooled = false;
                result.OnSpawned();
            }

            pool.Add(result);
            return result;
        }
    }

    [DisallowMultipleComponent]
    public class PooledItem : MonoBehaviour
    {
        /// <summary>
        /// Current alive status of item.
        /// </summary>
        /// <remarks>
        /// When item is spawned it is marked as not pooled.
        /// </remarks>
        public bool isPooled;

        /// <summary>
        /// One-time-use event of spawning from pool.
        /// </summary>
        public event Action onSpawnedEvent;

        /// <summary>
        /// One-time-use event of returning to pool.
        /// </summary>
        public event Action onReleasedEvent;

        public void OnSpawned()
        {
            var ev = onSpawnedEvent;
            onSpawnedEvent = null;
            ev?.Invoke();
        }

        public void OnRelesed()
        {
            var ev = onReleasedEvent;
            onReleasedEvent = null;
            ev?.Invoke();
        }
    }
}