using System;
using UnityEngine;
using UnityEditor;

namespace BBB
{
    public class LocalSavesManagerWindow : EditorWindow
    {
        private LocalSavesManager _savesManager = new LocalSavesManager();
        private Vector2 _scroll;

        [MenuItem("BebopBee/Debug/SavesManager", priority = 10)]
        public static void Create()
        {
            GetWindow<LocalSavesManagerWindow>(title: "Saves Manager");
        }

        private void OnGUI()
        {
            if (GUILayout.Button("Refresh List"))
            {
                _savesManager.LoadSlotsDataVer2();
            }
            if (GUILayout.<PERSON><PERSON>("Reveal in explorer"))
            {
                EditorUtility.RevealInFinder(_savesManager.SaveDir);
            }

            EditorGUILayout.HelpBox("Load save will go into local storage." +
                "\nWhen you start game next time ServerSync popup will show up" +
                "\nand you should select local state.", MessageType.Info);

            _scroll = EditorGUILayout.BeginScrollView(_scroll);
            {
                for (int i = 0; i < _savesManager.Slots.Count; i++)
                {
                    bool del;
                    bool isChanged;
                    DisplaySingleSlotItem(_savesManager.Slots[i], isChanged: out isChanged, del: out del);
                    if (del)
                    {
                        _savesManager.DeleteSaveFile(_savesManager.Slots[i].FileNameDataFile);
                        _savesManager.Slots.RemoveAt(i);
                        break;
                    }
                    else if (isChanged)
                    {
                        _savesManager.Slots[i].FileName = null;
                        _savesManager.SaveSlotMetaData(_savesManager.Slots[i]);
                    }
                }

                if (GUILayout.Button("Add Slot"))
                {
                    var slot = new SlotDataContainer();
                    int index = 0;
                    while (IsExistsSlotWithIndex(index))
                    {
                        index++;
                    }
                    slot.SaveSlotIndex = index;
                    _savesManager.Slots.Add(slot);
                    _savesManager.SaveSlotMetaData(slot);
                }
            }
            EditorGUILayout.EndScrollView();
        }

        private bool IsExistsSlotWithIndex(int index)
        {
            foreach (var s in _savesManager.Slots)
            {
                if (s.SaveSlotIndex == index)
                {
                    return true;
                }
            }

            return false;
        }

        private void OnEnable()
        {
            _savesManager.LoadSlotsDataVer2();
        }

        private void DisplaySingleSlotItem(SlotDataContainer slot, out bool isChanged, out bool del)
        {
            if (!_savesManager.IsInitialized)
            {
                _savesManager.LoadSlotsDataVer2();
            }

            isChanged = false;
            del = false;
            EditorGUILayout.BeginHorizontal("box");
            {
                EditorGUILayout.BeginVertical();
                {
                    string slotName = EditorGUILayout.TextField("Name", slot.Name);
                    if (slotName != slot.Name)
                    {
                        slot.Name = slotName;
                        isChanged = true;
                    }

                    EditorGUILayout.BeginHorizontal("box");
                    {
                        EditorGUILayout.BeginVertical("box");
                        {
                            if (!Application.isPlaying)
                            {
                                GUI.enabled = false;
                            }

                            if (GUILayout.Button("Save"))
                            {
                                isChanged = true;
                                var now      = DateTime.Now;
                                if (slot.FileNameV2.IsNullOrEmpty())
                                {
                                    slot.FileNameV2 = _savesManager.GenerateUniqueSlotName(slot.SaveSlotIndex);
                                }

                                string maxLevel = "";

                                _savesManager.SaveSlotMetaData(slot);
                                if (_savesManager.TrySaveGameStateInFileName(slot.FileNameDataFile, ref maxLevel))
                                {
                                    Debug.Log("Saving game in slot " + slot.SaveSlotIndex);
                                    if (slot.Name.IsNullOrEmpty())
                                    {
                                        slot.Name = "SaveSlot_" + slot.SaveSlotIndex;
                                    }

                                    slot.Version = Application.version;
                                    slot.MaxLevel = maxLevel;
                                    slot.SaveDateTime = now;
                                }
                            }

                            GUI.enabled = !slot.FileNameV2.IsNullOrEmpty();

                            if (GUILayout.Button("Load"))
                            {
                                var loadedBytes = _savesManager.LoadBytesFromSaveFile(slot.FileNameDataFile);
                                if (loadedBytes == null || loadedBytes.Length == 0)
                                {
                                    Debug.Log("Load failed: no save data with file name: " + slot.FileNameV2);
                                }
                                else
                                {
                                    if (Application.isPlaying)
                                    {
                                        if (_savesManager.TryLoadSaveGameIntoRuntimeState(loadedBytes))
                                        {
                                            _savesManager.TryLoadSaveGameToLocalStorage(loadedBytes);
                                            Debug.Log("Load save success");
                                            EditorApplication.isPlaying = false;
                                        }
                                        else
                                        {
                                            if (_savesManager.TryLoadSaveGameToLocalStorage(loadedBytes))
                                            {
                                                Debug.Log("Load save local2 success");
                                                EditorApplication.isPlaying = false;
                                            }
                                        }
                                    }
                                    else
                                    {
                                        if (_savesManager.TryLoadSaveGameToLocalStorage(loadedBytes))
                                        {
                                            Debug.Log("Load save local success");
                                        }
                                    }
                                }
                            }

                            GUI.enabled = true;
                        }
                        EditorGUILayout.EndVertical();
                        EditorGUILayout.BeginVertical("box");
                        {
                            EditorGUILayout.LabelField("Version:",  slot.Version);
                            EditorGUILayout.LabelField("MaxLevel:", slot.MaxLevel);
                            EditorGUILayout.LabelField("Date:", slot.GetDateString());
                        }
                        EditorGUILayout.EndVertical();
                    }
                    EditorGUILayout.EndVertical();
                }
                EditorGUILayout.EndVertical();
                EditorGUILayout.BeginVertical("box");
                {
                    EditorGUILayout.LabelField("Slot " + slot.SaveSlotIndex + ". File = " + slot.FileNameV2);
                    if (GUILayout.Button("Clear"))
                    {
                        isChanged = true;
                        _savesManager.DeleteSaveFile(slot.FileNameDataFile);
                        slot.FileNameV2 = "";
                        slot.Name = "";
                        slot.Version = "";
                        slot.MaxLevel = "";
                        slot.SaveDate = 0;
                    }
                    else if (GUILayout.Button("DEL"))
                    {
                        del = true;
                    }
                }
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndHorizontal();
        }
    }
}