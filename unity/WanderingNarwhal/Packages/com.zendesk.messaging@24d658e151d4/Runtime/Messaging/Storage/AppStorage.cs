using Zendesk.Runtime.Models;
using Zendesk.Runtime.Storage;

namespace Zendesk.Runtime.Messaging.Storage
{
    /// <summary>
    /// Storage for objects related to the current Messaging user.
    /// </summary>
    internal interface IAppStorage
    {
        User GetUser();
        void SetUser(User user);
        void Clear();
    }

    internal class AppStorage : IAppStorage
    {
        private static string NameSpace(string integrationId) => $"ZendeskAppStorage_{integrationId}";
        private const string _UserKey = "USER";

        private readonly IStorage _storage;

        internal AppStorage(string integrationId)
        {
            _storage = StorageFactory.Create(NameSpace(integrationId));
        }

        public void SetUser(User user)
        {
            _storage.Set(_UserKey, user);
        }

        public User GetUser()
        {
            return _storage.TryGet<User>(_UserKey, out var user) ? user : null;
        }

        public void Clear()
        {
            _storage.Clear();
        }
    }
}