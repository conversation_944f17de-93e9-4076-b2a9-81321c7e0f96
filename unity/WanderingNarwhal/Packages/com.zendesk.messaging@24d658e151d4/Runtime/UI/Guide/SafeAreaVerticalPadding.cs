using UnityEngine;

namespace Zendesk.Runtime.UI.Guide
{
    public class SafeAreaVerticalPadding : MonoBehaviour
    {
        [SerializeField] private CanvasHelper _canvasHelper;
        [SerializeField] private RectTransform _canvasRectTransform;
        [SerializeField] private RectTransform _safeAreaRectTransform;
        [SerializeField] private RectTransform _bottomForegroundRectTransform;
        [SerializeField] private RectTransform _headerForegroundRectTransform;
        
        // Extra padding to prevent gaps from scaling issues
        private const int _ExtraPadding = 3;

        private void HandleCanvasUpdated()
        {
            int bottomBackgroundHeight =
                (int)(_canvasRectTransform.sizeDelta.y * _safeAreaRectTransform.anchorMin.y);
            _bottomForegroundRectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical,
                bottomBackgroundHeight);

            int headerBackgroundHeight =
                (int)(_canvasRectTransform.sizeDelta.y * (1 - _safeAreaRectTransform.anchorMax.y)) + _ExtraPadding;
            _headerForegroundRectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical,
                headerBackgroundHeight);
        }

        private void Start()
        {
            HandleCanvasUpdated();
        }

        private void OnEnable()
        {
            _canvasHelper.CanvasUpdatedEvent += HandleCanvasUpdated;
        }


        private void OnDisable()
        {
            _canvasHelper.CanvasUpdatedEvent -= HandleCanvasUpdated;
        }

        private void OnDestroy()
        {
            if (_canvasHelper != null)
                _canvasHelper.CanvasUpdatedEvent -= HandleCanvasUpdated;
        }
    }
}