using System.Collections.Generic;
using UnityEngine;
using Zendesk.MessagingCore.Logging;
using Zendesk.Runtime.UI.Guide.Shared.ViewModels;
using Logger = Zendesk.MessagingCore.Logging.Logger;

namespace Zendesk.Runtime.UI.Guide
{
    internal class GuideArticleListView
    {
        private readonly RectTransform _articlesContainer;
        private readonly IPrefabViewFactory<IGuideArticleButtonView> _articleViewFactory;
        private readonly int _maxArticleCount;

        private readonly List<IGuideArticleButtonView> _articles = new List<IGuideArticleButtonView>();
        private IReadOnlyList<ArticleViewModel> _articleViewModels;
        private readonly Logger _logger = LoggerManager.GetLogger<GuideArticleListView>();

        public GuideArticleListView(IPrefabViewFactory<IGuideArticleButtonView> articleViewFactory,
            RectTransform articlesContainer, int maxArticleCount)
        {
            _articleViewFactory = articleViewFactory;
            _articlesContainer = articlesContainer;
            _maxArticleCount = maxArticleCount;
        }

        public List<IGuideArticleButtonView> CreateArticles(IReadOnlyList<ArticleViewModel> articleViewModels)
        {
            if (articleViewModels.Count == 0)
            {
                _logger.LogError("No articles found");
                return null;
            }

            _articleViewModels = articleViewModels;

            SpawnArticlePrefabs();

            return _articles;
        }

        private void SpawnArticlePrefabs()
        {
            _articleViewFactory.SetInactiveItems(new List<IGuideArticleButtonView>(_articles));
            _articles.Clear();

            int articleCount = Mathf.Min(_articleViewModels.Count, _maxArticleCount);
            for (int i = 0; i < articleCount; i++)
            {
                var guideArticle = _articleViewFactory.GetPrefab(_articlesContainer);

                var guideArticleViewModel = _articleViewModels[i];

                SetupArticle(guideArticle, guideArticleViewModel);

                _articles.Add(guideArticle);
            }

            _articleViewFactory.DisableInactiveItems();
        }

        private void SetupArticle(IGuideArticleButtonView guideArticleButtonView, ArticleViewModel model)
        {
            guideArticleButtonView.SetData(model);
            guideArticleButtonView.Show();
        }
    }
}