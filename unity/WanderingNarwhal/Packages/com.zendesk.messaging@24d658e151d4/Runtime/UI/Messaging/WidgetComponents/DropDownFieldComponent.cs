using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Zendesk.Runtime.Models;
using Zendesk.Runtime.UI.Messaging.Widgets;
using Zendesk.Runtime.UI.ZMPopUpList;

namespace Zendesk.Runtime.UI.Messaging.WidgetComponents
{
    public class DropDownFieldComponent : FieldComponent
    {
        private const string _FieldValidationErrorMessageKey = "messaging_unity_zmu_form_field_required_label";
        
        [Header("Dropdown List")] 
        [SerializeField] private ZMPopUpListButton _zmPopUpListButton;
        [SerializeField] private GameObject _popupListGameObject;

        private DropDownUserInput _userInputData;
        
        public override IFormFieldUserInput UserInputData => _userInputData;
        public override MessageFormFieldType FieldType => MessageFormFieldType.Select;
        protected override string ErrorTextKey => _FieldValidationErrorMessageKey;

        public override bool IsValid => 
            base.IsValid &&
            _zmPopUpListButton != null &&
            _popupListGameObject != null;
        public override bool IsUserInputValid => _userInputData.Selection != null;
        
        protected override string UserFacingSentValue => _userInputData.Selection?.Label ?? string.Empty;

        protected override void OnSetContent()
        {
            _popupListGameObject.SetActive(true);
            
            _zmPopUpListButton.PlaceholderText = _userInputData.FormField.Label;
            _zmPopUpListButton.ListTitleText = _userInputData.FormField.Label;
            var dropdownOptions = new List<ZMOptionData>(_userInputData.FormField.Options.Select(
                formFieldItem => new ZMOptionData(formFieldItem.Label, formFieldItem)).ToList());
            _zmPopUpListButton.SetOptions(dropdownOptions);
            _zmPopUpListButton.ItemSelected += ItemSelectedEventHandler;
            _zmPopUpListButton.ItemDeselected += ItemDeselectedEventHandler;
            _zmPopUpListButton.CancelButtonClicked += CancelButtonPressedEventHandler;
            SetInitialValue(dropdownOptions);
        }

        private void SetInitialValue(List<ZMOptionData> dropdownOptions)
        {
            if (string.IsNullOrWhiteSpace(_userInputData.FormField.Placeholder)) 
                return;
            
            ZMOptionData result = null;
            foreach (ZMOptionData option in dropdownOptions)
            {
                var optionName = (option.Data as MessageFormSelectField)?.Name;
                if (optionName != null && optionName == _userInputData.FormField.Placeholder)
                {
                    result = option;
                    break;
                }
            }

            if (result != null)
            {
                _zmPopUpListButton.SetCurrentOption(result);
                ItemSelectedEventHandler(result);
            }
        }

        public override void Clear()
        {
            _zmPopUpListButton.ItemSelected -= ItemSelectedEventHandler;
            _zmPopUpListButton.ItemDeselected -= ItemDeselectedEventHandler;
            _zmPopUpListButton.CancelButtonClicked -= CancelButtonPressedEventHandler;
            
            base.Clear();
        }

        public override MessageFormField CreateFormFieldFromInput()
        {
            return UserInputData.FormField.CreateCloneWithSelection(_userInputData.Selection);
        }

        protected override void SetInputActive(bool isActive)
        {
            _popupListGameObject.SetActive(isActive);
        }

        protected override void CreateUserInputData(
            MessageFormField messageFormField,
            IFormFieldUserInput existingFormFieldInput)
        {
            
            _userInputData = existingFormFieldInput is DropDownUserInput existingDropdownInput
                ? existingDropdownInput
                : new DropDownUserInput(messageFormField);
        }

        private void ItemDeselectedEventHandler()
        {
            _userInputData.Selection = null;
            TriggerValueChangedEvent();
        }

        private void ItemSelectedEventHandler(IOptionData optionData)
        {
            if (!(optionData is ZMOptionData newSelection))
                return;

            _userInputData.Selection = newSelection.Data as MessageFormSelectField;
            TriggerValueChangedEvent();
        }

        private void CancelButtonPressedEventHandler()
        {
            TriggerValueChangedEvent();
        }

        private class DropDownUserInput : IFormFieldUserInput
        {
            public MessageFormField FormField { get; }
            public MessageFormSelectField Selection;

            public DropDownUserInput(MessageFormField formField)
            {
                FormField = formField;
                Selection = formField.Select?.Count > 0 
                    ? formField.Select[0] 
                    : null;
            }
        }
    }
}
