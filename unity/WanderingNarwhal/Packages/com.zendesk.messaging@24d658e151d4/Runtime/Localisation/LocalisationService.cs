namespace Zendesk.Runtime.Localisation
{
    /// <summary>
    /// Provides an interface for locale services.
    /// </summary>
    internal interface ILocalisationService
    {
        /// <summary>
        /// Gets the current locale of the application.
        /// </summary>
        /// <returns>A string representing the current locale.</returns>
        string GetCurrentLocale();
    }

    /// <summary>
    /// Implementation of the ILocalisationService interface.
    /// </summary>
    internal class LocalisationService : ILocalisationService
    {
        /// <summary>
        /// Gets the current locale of the application.
        /// </summary>
        /// <returns>A string representing the current locale.</returns>
        public string GetCurrentLocale()
        {
            return ZMLocalisation.Instance.CurrentLocale; 
        }
    }
}