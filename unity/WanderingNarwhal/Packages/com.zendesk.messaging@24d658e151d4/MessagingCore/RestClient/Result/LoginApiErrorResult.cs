namespace Zendesk.MessagingCore.RestClient.Result
{
    public class LoginApiErrorResult<T> : Result<T>
    {
        public ErrorReason Reason { get; }

        public LoginApiErrorResult(ErrorReason reason) : base(default)
        {
            Reason = reason;
        }
    }

    public enum ErrorReason
    {
        JwtValidButUserNotFound,
        UserDoesNotExist,
        InvalidJwt,
        ForbiddenAccess,
        UnhandledError,
        FailedToParse
    }
}