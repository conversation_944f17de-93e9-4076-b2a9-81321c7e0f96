using System;
using System.Threading.Tasks;
using Zendesk.MessagingCore.Environment;
using Zendesk.MessagingCore.FayeClient.DTO;
using Zendesk.MessagingCore.Messaging;
using Zendesk.MessagingCore.Models;
using Zendesk.MessagingCore.RestClient.Result;
using Zendesk.MessagingCore.SocketClient;

namespace Zendesk.MessagingCore.FayeClient.Client
{
    public interface IZMFayeClient : IDisposable
    {
        Task<Result> EstablishConnection();
        event Action<ZendeskWsEventArgs> MessageReceived;

        /// <summary>
        /// Suspend the websocket connection while retaining the connection settings for future resume 
        /// </summary>
        /// <returns>Result of the attempt to pause connection </returns>
        Task<Result> PauseConnection();

        /// <summary>
        /// Resume the websocket connection using previously saved realtime connection configuration 
        /// </summary>
        /// <returns>Result of the attempt to resume connection </returns>
        Task<Result> ResumeConnection();
    }

    internal class ZMFayeClient : IZMFayeClient
    {
        private readonly RealtimeConfiguration _realtimeConfiguration;
        private readonly IZendeskFayeClient _fayeClient;
        private string _fayeClientId;
        public event Action<ZendeskWsEventArgs> MessageReceived;

        internal ZMFayeClient(RealtimeConfiguration configuration)
        {
            _realtimeConfiguration = configuration;
            _fayeClient = new ZendeskFayeClient(configuration.baseUrl, configuration.maxConnectionAttempts);
            _fayeClient.MessageReceived += OnMessageReceived;
        }

        //Test purposes only
        internal ZMFayeClient(IZendeskFayeClient fayeClient)
        {
            _fayeClient = fayeClient;
            _fayeClient.MessageReceived += OnMessageReceived;
        }

        public async Task<Result> EstablishConnection()
        {
            if(_fayeClient.State == ZendeskSocketState.Open)
                return new SuccessResult();
            
            var initResult = await _fayeClient.Init();
            if (initResult.Failure)
            {
                if (IsSocketConnectionError(initResult, out Result socketError)) 
                    return socketError;
                
                Env.Current?.MessagingCoreEvents?.RtcLogError(ZMConstants.FayeClientInitErrorMessage);
                return new FayeClientErrorResult(ZMConstants.FayeClientInitErrorMessage);
            }

            var handshakeResult = await _fayeClient.Handshake();
            if (handshakeResult.Failure)
            {
                Env.Current?.MessagingCoreEvents?.RtcLogError(ZMConstants.FayeClientHandshakeErrorMessage);
                return new FayeClientErrorResult(ZMConstants.FayeClientHandshakeErrorMessage);
            }

            return new SuccessResult();
        }

        private static bool IsSocketConnectionError(Result result, out Result errorResult)
        {
            if (IsWebSocketAlreadyStarted(result))
            {
                errorResult = new FayeClientErrorResult(ZMConstants.FayeClientSocketConnectedErrorMessage);
                return true;
            }

            errorResult = result;
            return false;
        }

        private static bool IsWebSocketAlreadyStarted(Result initResult)
        {
            return initResult is SocketConnectionErrorResult result &&
                   result.Message.ToLower().Contains("websocket has already been started");
        }

        public Task<Result> ResumeConnection()
        {
            return _fayeClient.ResumeConnection();
        }

        private async Task HandshakeReceived(string clientId)
        {
            _fayeClientId = clientId;
            await _fayeClient.Connect(_fayeClientId);
            await _fayeClient.Subscribe(_fayeClientId, _realtimeConfiguration.appId, _realtimeConfiguration.userId,
                _realtimeConfiguration.authentication);
        }

        private async Task Connected(string fayeClientId)
        {
            await _fayeClient.Connect(fayeClientId);
        }

        private void Subscribed()
        {
            Env.Current?.MessagingCoreEvents?.RtcLog("Subscribed");
        }

        private async void OnMessageReceived(ZendeskWsEventArgs messageEventArgs)
        {
            try
            {
                if (messageEventArgs.response == null || messageEventArgs.response.Count <= 0) return;
                switch (messageEventArgs.response[0].ChannelType)
                {
                    case WsChannelType.Handshake:
                        await HandshakeReceived(messageEventArgs.response[0].clientId);
                        break;
                    case WsChannelType.Connect:
                        await Connected(_fayeClientId);
                        break;
                    case WsChannelType.Subscribe:
                        Subscribed();
                        break;
                    case WsChannelType.Disconnect:
                        break;
                    case WsChannelType.Message:
                        MessageReceived?.Invoke(messageEventArgs);
                        break;
                    default:
                        throw new ArgumentOutOfRangeException();
                }
            }
            catch (Exception exception)
            {
                Env.Current?.MessagingCoreEvents?.ExceptionCaught(exception);
            }
        }

        public async Task<Result> PauseConnection()
        {
            if (_fayeClient == null || string.IsNullOrWhiteSpace(_fayeClientId))
                return new ErrorResult("Tried to pause connection but faye client was not available");

            Result result;
            try
            {
                await _fayeClient.Disconnect(_fayeClientId);
                result = await _fayeClient.Close();
            }
            catch (Exception e)
            {
                return new ErrorResult(e.Message);
            }

            return result;
        }

        public void Dispose()
        {
            RemoveAllSubscriptions();
        }

        private void RemoveAllSubscriptions()
        {
            var subscribers = MessageReceived?.GetInvocationList();
            if (subscribers == null) return;

            foreach (var subscriber in subscribers)
                MessageReceived -= subscriber as Action<ZendeskWsEventArgs>;
        }
    }
}
